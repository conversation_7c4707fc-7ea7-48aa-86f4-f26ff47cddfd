<template>
  <div>
    <a-modal
      :visible="visible"
      :destroyOnClose="true"
      width="1200px"
      @ok="handleOk"
      @cancel="() => cancel()"
      :bodyStyle="{
        overflow: 'auto',
        'max-height': '68vh',
        'min-height': '500px',
      }"
      :footer="false"
    >
      <div class="title">
        <div>
          <p style="font-size: 24px">
            <span style="font-weight: bold"> {{ formState.unitName }} </span>
            <!-- 信息被采用情况 -->
            上报信息查询
          </p>
          <p style="font-size: 18px">总上报 {{ formState.totalReport || 0 }} 条，被采用 {{ formState.totalCollect }} 条</p>
        </div>
        <a-space class="btn">
          <a-button type="primary" @click="down"> 导出Excel </a-button>
        </a-space>
      </div>
      <table class="table">
        <template v-if="propObj.detailType != 1">
          <colgroup width="300"></colgroup>
          <colgroup width="300"></colgroup>
          <colgroup width="300"></colgroup>
          <colgroup width="300"></colgroup>
          <colgroup width="300"></colgroup>
        </template>
        <template v-else>
          <colgroup width="200"></colgroup>
          <colgroup width="200"></colgroup>
          <colgroup width="200"></colgroup>
          <colgroup width="200"></colgroup>
          <colgroup width="200"></colgroup>
          <colgroup width="200"></colgroup>
          <colgroup width="200"></colgroup>
          <colgroup width="200"></colgroup>
        </template>
        <thead>
          <tr style="font-size: 16px; font-weight: bold">
            <template v-if="propObj.detailType != 1">
              <td>报送标题</td>
              <td>报送时间</td>
              <td>期号</td>
              <td>报送单位（委员）</td>
              <td>是否采用</td>
            </template>
            <template v-else>
              <td>采用标题</td>
              <td>采用时间</td>
              <td>领导批示（人次）</td>
              <td>期号</td>
              <td>报送单位（委员）</td>
              <td>得分</td>
              <td>加分</td>
              <td>总分</td>
            </template>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(item, index) in data" :key="index">
            <template v-if="propObj.detailType != 1">
              <td>{{ item["title"] }}</td>
              <td>
                {{ item["reportTime"] ? dayjs(item["reportTime"]).format("YYYY-MM-DD") : undefined }}
              </td>
              <td>
                {{ item["year"] ? `${item["year"]}年` : "" }}
                {{ item["issue"] ? `第${item["issue"]}期` : "" }}
              </td>

              <td>{{ item["reportUnitName"] }}</td>
              <td>{{ item["status"] == "3" ? "已采用" : "未采用" }}</td>
            </template>
            <template v-else>
              <td>{{ item["title"] }}</td>
              <td>
                {{ item["collectionTime"] ? dayjs(item["collectionTime"]).format("YYYY-MM-DD") : undefined }}
              </td>
              <td>
                {{ item["personCount"] }}
              </td>
              <td>
                {{ item["year"] ? `${item["year"]}年` : "" }}
                {{ item["issue"] ? `第${item["issue"]}期` : "" }}
              </td>
              <td>{{ item["reportUnitName"] }}</td>
              <td>{{ item["score"] }}</td>
              <td>{{ item["scoreItemValue"] }}</td>
              <td>{{ item["score"] + item["scoreItemValue"] }}</td>
            </template>
          </tr>
        </tbody>
      </table>
    </a-modal>
  </div>
</template>

<script>
import { auth as http } from "@zh/common-utils/http";
import dayjs from "dayjs";
import qs from "qs";
export default {
  components: {},
  props: {},
  data() {
    return {
      dayjs,
      visible: false,
      propObj: {},
      formState: {},
      data: [],
    };
  },
  methods: {
    open(record) {
      if (record) {
        this.propObj = { ...record };
        this.getDetail();
      }
      this.visible = true;
    },
    getDetail() {
      http
        .get(
          `/syReport/collectEditStatistics/detail?${qs.stringify({
            pageNum: 1,
            pageSize: 999,
            ...this.propObj,
          })}`
        )
        .then((res) => {
          this.visible = true;
          this.formState = res;
          this.data = res["list"].map((obj) => {
            return {
              ...obj,
              collectionTime: obj["collectionTime"] ? dayjs(obj["collectionTime"]).format("YYYY-MM-DD") : undefined,
            };
          });
        });
    },
    cancel() {
      this.visible = false;
      this.formState = {};
      this.propObj = {};
      this.data = [];
    },
    handleOk() {
      this.cancel();
    },
    down() {
      http
        .get(
          `/syReport/collectEditStatistics/detailExport?${qs.stringify({
            pageNum: 1,
            ...this.propObj,
          })}`,
          { responseType: "blob" }
        )
        .then((res) => {
          console.log("🚀 ~ file: addEdit.vue:124 ~ .then ~ res:", res);
          const blob = res.data;
          let contentDisposition = decodeURI(res.headers["content-disposition"]) || "";
          const url = window.URL.createObjectURL(blob);

          let link = document.createElement("a");

          link.style.display = "none";

          link.href = url;

          link.setAttribute("download", contentDisposition.substring(20)); //文件名后缀记得添加，我写的zip

          document.body.appendChild(link);

          link.click();

          document.body.removeChild(link); //下载完成移除元素

          window.URL.revokeObjectURL(url); //释放掉blob对象
        });
    },
  },
};
</script>

<style scoped lang="less">
.title {
  display: flex;
  position: relative;
  margin: 40px auto;
  p {
    margin: 0;
  }
  & > div:first-child {
    width: 100%;
    text-align: center;
  }
  .btn {
    position: absolute;
    right: 26px;
    top: 12px;
  }
}
.table {
  margin: auto;
  table {
    table-layout: fixed;
  }

  td {
    border: 1px solid #b7b7b7;
    text-align: center;
    height: 46px;
  }
  thead > tr > td {
    height: 60px;
  }
}
</style>
