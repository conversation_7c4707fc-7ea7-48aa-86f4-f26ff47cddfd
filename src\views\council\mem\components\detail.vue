<template>
  <div class="page">
    <div>
      <table>
        <tr>
          <th>委员证号</th>
          <td>{{ formState["lastMemNum"] }}</td>
          <th>姓名/性别</th>
          <td>{{ formState["name"] }}/{{ formState["sexCode"] == "1" ? "男" : "女" }}</td>
          <th>民族/组别</th>
          <td>{{ formState["nationName"] }}/{{ formState["memGroupPartName"] }}</td>
        </tr>
        <tr>
          <th>继任委员证号</th>
          <td>{{ formState["lastMemNum"] }}</td>
          <th>身份证号码</th>
          <td>{{ formState["idCard"] }}</td>
          <th>籍贯</th>
          <td>{{ formState["nativePlace"] }}</td>
        </tr>
        <tr>
          <th>出生地</th>
          <td>{{ formState["birthplace"] }}</td>
          <th>健康状况</th>
          <td>{{ formState["healthName"] }}</td>
          <th>有何专长</th>
          <td>{{ formState["speciality"] }}</td>
        </tr>
        <tr>
          <th>政治面貌</th>
          <td>{{ formState["politicalAffiliationCodeName"] }}</td>
          <th>党派</th>
          <td>{{ formState["politicalName"] }}</td>
          <th>入党时间</th>
          <td>{{ formState["politicalDate"] ? dayjs(formState["politicalDate"]).format('YYYY.MM.DD') : '' }}</td>
        </tr>
        <tr>
          <th>职称</th>
          <td>{{ formState["rankName"] }}</td>
          <th>第二党派</th>
          <td>{{ formState["politicalTwoName"] }}</td>
          <th>第二党派入党时间</th>
          <td>{{ formState["politicalTwoDate"] ? dayjs(formState["politicalTwoDate"]).format('YYYY.MM.DD') : '' }}</td>
        </tr>
        <tr>
          <th>委员类型/界别</th>
          <td>{{ formState["oldMemTypeName"] }}/{{ formState["jbName"] }}</td>
          <th>兼任情况</th>
          <td>
            <template v-if ="formState['memTypeAll'] == '1'"><div>全国兼任市政协</div></template>
            <template v-if ="formState['memTypeAll'] == '2'"><div>市政协兼任区县</div></template>
            <template v-if ="formState['memTypeAll'] == '3'"><div>全国兼任区县</div></template>
          </td>
          <th>区县政协</th>
          <td>{{ formState["districtsName"] }}</td>
        </tr>
        <tr>
          <th>委员职务</th>
          <td>{{ formState["committeePostStatusName"] }}</td>
          <th>委员任职时间</th>
          <td colspan="3">{{ formState["committeePostDate"] ? dayjs(formState["committeePostDate"]).format('YYYY.MM.DD') : '' }}</td>
        </tr>
        <tr>
          <th>委员任免记录</th>
          <td colspan="5">{{ formState["removalRecord"] }}</td>
        </tr>
        <tr>
          <th>常委组成人员</th>
          <td>{{ formState["oftenMemPostName"] }}</td>
          <th>常委任职时间</th>
          <td>{{ formState["oftenMemPostDate"] ? dayjs(formState["oftenMemPostDate"]).format('YYYY.MM.DD') : '' }}</td>
          <th>常委离任时间</th>
          <td>{{ formState["oftenMemPostOutDate"] ? dayjs(formState["oftenMemPostOutDate"]).format('YYYY.MM.DD') : '' }}</td>
        </tr>
        <tr>
          <th>常委任免记录</th>
          <td colspan="5">{{ formState["oftenMemRemovalRecord"] }}</td>
        </tr>
        <tr>
          <th>专委会名称</th>
          <td>{{ formState["specialName"] }}</td>
          <th>专委会职务</th>
          <td>{{ formState["specialPostName"] }}</td>
          <th>专委会任职时间</th>
          <td>{{ formState["specialPostDate"] ? dayjs(formState["specialPostDate"]).format('YYYY.MM.DD') : ''}}</td>
        </tr>
        <tr>
          <th>专委会离任时间</th>
          <td>{{ formState["specialPostOutDate"] ? dayjs(formState["specialPostOutDate"]).format('YYYY.MM.DD') : '' }}</td>
          <th>区县政协职务</th>
          <td colspan="3">{{ formState["regionalPost"] }}</td>
        </tr>
        <tr>
          <th>专委会任免记录</th>
          <td colspan="5">{{ formState["specialRemovalRecord"] }}</td>
        </tr>
        <tr>
          <th>区县政协安排记录</th>
          <td colspan="5">{{ formState["regionalRecord"] }}</td>
        </tr>
        <tr>
          <th>全日制学历</th>
          <td>{{ formState["fullEducationName"] }}</td>
          <th>全日制学位</th>
          <td>{{ formState["fullEducationDegreeCode"] }}</td>
          <th>全日制院校</th>
          <td>{{ formState["fullEducationSchool"] }}</td>
        </tr>
        <tr>
          <th>全日制专业</th>
          <td>{{ formState["fullEducationSpecialty"] }}</td>
          <th>在职学历</th>
          <td>{{ formState["workEducationName"] }}</td>
          <th>在职学位</th>
          <td>{{ formState["workEducationDegreeCode"] }}</td>
        </tr>
        <tr>
          <th>在职院校</th>
          <td>{{ formState["workEducationSchool"] }}</td>
          <th>在职专业</th>
          <td>{{ formState["workEducationSpecialty"] }}</td>
          <th>参加工作时间</th>
          <td>{{ formState["workDate"] ? dayjs(formState["workDate"]).format('YYYY.MM.DD') : '' }}</td>
        </tr>
        <tr>
          <th>单位及职务</th>
          <td colspan="3">{{ formState["workAndPost"] }}</td>
          <th>单位电话</th>
          <td>{{ formState["workUnitPhone"] }}</td>
        </tr>
        <tr>
          <th>兼任职务</th>
          <td>{{ formState["otherWorkPost"] }}</td>
          <th>电子邮箱</th>
          <td>{{ formState["email"] }}</td>
          <th>移动电话</th>
          <td>{{ formState["phone"] }}</td>
        </tr>
        <tr>
          <th>联系地址</th>
          <td colspan="5">{{ formState["address"] }}</td>
        </tr>
        <tr>
          <th>个人简历</th>
          <td colspan="5">{{ formState["resume"] }}</td>
        </tr>
        <tr>
          <th>主要表现</th>
          <td colspan="5">{{ formState["expression"] }}</td>
        </tr>
        <tr>
          <th>主要成就</th>
          <td colspan="5">{{ formState["achievement"] }}</td>
        </tr>
        <tr>
          <th>备注</th>
          <td colspan="5">{{ formState["remark"] }}</td>
        </tr>
      </table>
    </div>

    <!-- <div>照片</div> -->
    <img v-if="imageUrl" :src="imageUrl" alt="照片" class="img" />
    <img v-else :src="require('@/assets/photo.png')" alt="照片" class="img" />
  </div>
</template>

<script>
import { auth as http } from "@zh/common-utils/http";
import dayjs from "dayjs";

export default {
  props: {
    formState: {
      type: Object,
    },
    imageUrl: {
      type: String,
    },
  },
  data() {
    return {
      dayjs,
      form: this.$form.createForm(this, { name: "council_mem" }),
      visible: false,
      labelCol: { span: 12 },
      wrapperCol: { span: 12 },
      // formState: {
      //   name: undefined,
      //   region: undefined,
      //   date1: undefined,
      //   delivery: undefined,
      //   type: undefined,
      //   resource: undefined,
      //   desc: undefined,
      // },
    };
  },
  mounted() {
    this.getDict();
  },
  methods: {
    open() {
      this.visible = true;
    },
    getDict() {
      http.get("/dictCode/getByType?codeType=gb3304");
    },
    cancel() {
      this.visible = false;
    },
    handleOk() {
      this.form.validateFields((err) => {
        if (!err) {
          console.info("success");
        }
      });
      // this.cancel();
    },
  },
};
</script>

<style scoped lang="less">
@import url("./addEdit.less");

.tit {
  border-left: 6px solid #0da1ff;
  height: 21px;
  line-height: 21px;
  color: #333;
  font-size: 18px;
  padding-left: 9px;
  margin: 30px 0px 30px 10px;
  text-align: left;
}
.page{
  position: relative;
  .img{
    position: absolute;
    right: 30px;
    top: 80px;
    width: 110px;
    height: 120px;
  }
}

</style>
