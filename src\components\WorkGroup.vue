<template>
  <div>
    <a-input placeholder="请选择人员" v-model="showNames" @click="show"></a-input>
    <a-modal
      :visible="visible"
      :destroyOnClose="true"
      title="工作组人员选择器"
      width="1060px"
      @cancel="cancel"
      @ok="onOk"
      :bodyStyle="{ overflow: 'auto', 'max-height': '68vh' }"
    >
      <div class="page">
        <div>
          <a-tree @select="treeSelect" :tree-data="treeData" :replaceFields="{ children: 'children', title: 'name', key: 'id' }" />
        </div>
        <div style="width: 100%">
          <div class="group-title">已选择人员</div>
          <a-textarea v-model="selectItemNaes" :rows="9"></a-textarea>
          <!-- <div style="height: 200px; overflow: auto; width: 100%; border: 1px solid #4290f7; border-radius: 4px">
            <a-tag class="tag" v-for="item in userList" :key="item.userId" closable>{{ item.userName }}</a-tag>
          </div> -->
          <div class="group-title">
            人员组信息
            <a-button type="primary" @click="() => this.$refs.userRef.openDialog()">人员选择</a-button>
            <z-select-user v-model="checkedList" style="display: none" placeholder="请输入" :limit="99999" @change="userChange" ref="userRef" />
          </div>

          <div :style="{ borderBottom: '1px solid #E9E9E9', 'padding-bottom': '6px', marginBottom: '6px' }">
            <a-checkbox :checked="checkAll" @change="onCheckAllChange"> 全选</a-checkbox>
          </div>
          <div style="max-height: 29vh; overflow: auto">
            <a-checkbox
              v-for="item in dataSource"
              :value="item.userId"
              :key="item.userId"
              :checked="checkedList.includes(item.userId)"
              @change="groupChange"
              class="box"
            >
              {{ item["userName"] }}
            </a-checkbox>
          </div>

        </div>
      </div>
    </a-modal>
  </div>
</template>

<script>
import { auth as http } from "@zh/common-utils/http";

export default {
  props: {
    // 弹出框参数
    attrs: {
      type: Object,
      default: () => {
        return {};
      },
    },
    value: {
      type: String,
    },
  },
  data() {
    return {
      treeData: [],
      dataSource: [],
      pagination: {},
      visible: false,
      userList: [],
      selectItem: [],
      selectItemNaes: "",
      checkAll: false,
      checkedList: [],
    };
  },
  mounted() {},
  methods: {
    show() {
      this.visible = true;
      this.getList();
      console.log(this, "tttttttttttttttt");
    },
    getList() {
      http
        .post("/personnelGroup/list", {
          bizType: this.$route.path.startsWith("/sendread") ? "1" : "2",
          pageNum: 1,
          pageSize: 9999,
        })
        .then((res) => {
          const { currPage, pageSize, list, totalCount } = res;
          this.treeData = list.map((obj) => {
            return {
              ...obj,
              disabled: obj.isEnable != "1",
            };
          });
          this.getGroup(list[0] || {});
        });
    },
    getGroup(record) {
      http.get(`/personnelGroup/findGroup?groupId=${record.id}`).then((res) => {
        this.dataSource = res;
      });
    },
    treeSelect(selectedKeys) {
      if (selectedKeys.length > 0) {
        this.checkAll = false;
        this.getGroup({
          id: selectedKeys[0],
        });
      }
    },
    groupChange(e) {
      let { checked, value } = e.target;
      let keys = [...this.checkedList];
      if (checked) {
        keys.push(value);
      } else {
        keys = keys.filter((key) => key != value);
      }
      this.checkedList = keys;
      if (keys.length == 0) {
        this.userList = [];
      }
      this.changeNames();
    },
    onCheckAllChange(e) {
      let { checked } = e.target;
      let keys = [...this.checkedList];
      let ownKey = [];
      this.dataSource.forEach((obj) => {
        ownKey.push(obj["userId"]);
      });
      if (checked) {
        keys = keys.concat(ownKey);
        keys = this.unique(keys);
      } else {
        keys = keys.filter((key) => !ownKey.includes(key));
      }
      this.checkedList = keys;
      this.checkAll = checked;
      if (keys.length == 0) {
        this.userList = [];
      }
      this.changeNames();
    },
    changeNames() {
      let names = [];
      [...this.userList].forEach((obj) => {
        if (obj["name"] || obj["userName"]) {
          names.push(obj["name"] || obj["userName"]);
        }
      });
      this.selectItemNaes = names.join("、");
    },
    userChange(e, data) {
      // console.log("🚀 ~ file: workgroup.vue:173 ~ userChange ~ data:", data);
      this.userList = data.map((obj) => {
        return {
          ...obj,
          userId: obj.id,
          userName: obj.name,
        };
      });
      this.changeNames();
    },
    onOk() {
      this.showNames = this.selectItemNaes;
      this.$emit(
        "change",
        this.userList.map((obj) => obj["userId"]),
        this.userList
      );
      this.cancel();
      console.log(this.selectItem, this.userList);
    },
    cancel() {
      this.visible = false;
    },
    unique(arr, key) {
      //数组去重 key非必传
      const res = new Map();
      if (key) {
        return arr.filter((a) => !res.has(a[key]) && res.set(a[key], 1));
      }
      return arr.filter((a) => !res.has(a) && res.set(a, 1));
    },
    getNames(attr) {
      let ids = attr;
      if (typeof attr == "string") {
        ids = JSON.parse(ids);
      }
      http
        .post(`/adapter/user/list/by/ids`, {
          userIds: ids,
        })
        .then((res) => {
          let data = [];
          let keys = [];
          res.forEach((obj) => {
            keys.push(obj.id);
            data.push({
              ...obj,
              userId: obj.id,
              userName: obj.name,
            });
          });
          this.checkedList = keys;
          this.userList = data;
          this.changeNames();
          this.showNames = this.selectItemNaes;
        });
    },
  },
  watch: {
    // 监听当前值变化，及时提交给父组件
    value: {
      deep: true,
      immediate: true,
      handler: function (newValue) {
        console.log("🚀 ~ file: dict.vue:129 ~ newValue:", newValue);
        this.getNames(newValue);
      },
    },
  },
};
</script>

<style lang="less" scoped>
.page {
  display: flex;
  & > div:first-child {
    width: 220px;
    border-right: 1px solid #f2f2f2;
    margin-right: 20px;
  }
}

.box {
  width: 120px;
  margin-left: 0;
  margin-right: 12px;
  margin-bottom: 6px;
}
.group-title {
  margin: 10px 0;
  button {
    float: right;
  }
}
.tag {
  margin-bottom: 6px;
}
</style>
