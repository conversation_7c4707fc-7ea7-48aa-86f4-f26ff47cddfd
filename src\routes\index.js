export default [
  {
    path: '/home',
    meta: { icon: 'home', title: '首页' },
    component: () => import('@/views/home'),
  },
  {
    path: '/council/mem',
    meta: { icon: 'dashboard', title: '委员信息' },
    component: () => import('@/views/council/mem'),
  },
  {
    path: '/council/session',
    meta: { icon: 'dashboard', title: '届次信息' },
    component: () => import('@/views/council/session'),
  }, {
    path: '/jobinfor/home',
    meta: { icon: 'dashboard', title: '工作信息首页' },
    component: () => import('@/views/jobinfor/home')
  }, {
    path: '/jobinfor/publish/list',
    meta: { icon: 'dashboard', title: '工作信息查询' },
    component: () => import('@/views/jobinfor/publish/list')
  }, {
    path: '/jobinfor/publish/update',
    meta: { icon: 'dashboard', title: '工作信息维护' },
    component: () => import('@/views/jobinfor/publish/update')
  }, {
    path: '/jobinfor/publish/report',
    meta: { icon: 'dashboard', title: '工作信息发布' },
    component: () => import('@/views/jobinfor/publish/report')
  },
  {
    path: '/jobinfor/audit/list',
    meta: { icon: 'dashboard', title: '工作信息审核' },
    component: () => import('@/views/jobinfor/audit/list')
  },
  {
    path: '/jobinfor/message/list',
    meta: { icon: 'dashboard', title: '待发布查询' },
    component: () => import('@/views/jobinfor/message/list')
  },
  {
    path: '/jobinfor/message/aready',
    meta: { icon: 'dashboard', title: '已发布查询' },
    component: () => import('@/views/jobinfor/message/aready')
  },
  {
    path: '/jobinfor/message/update',
    meta: { icon: 'dashboard', title: '信息发布维护' },
    component: () => import('@/views/jobinfor/message/update')
  },
  {
    path: '/jobinfor/statistics',
    meta: { icon: 'dashboard', title: '工作信息统计' },
    component: () => import('@/views/jobinfor/statistics/list')
  },
  {
    path: '/sendread/home',
    meta: { icon: 'dashboard', title: '送阅首页' },
    component: () => import('@/views/sendread/home')
  },
  {
    path: '/sendread/inform',
    meta: { icon: 'dashboard', title: '约稿登记' },
    component: () => import('@/views/sendread/inform/report')
  },
  {
    path: '/sendread/inform/list',
    meta: { icon: 'dashboard', title: '约稿登记上报列表' },
    component: () => import('@/views/sendread/inform/list')
  },
  {
    path: '/sendread/inform/update',
    meta: { icon: 'dashboard', title: '约稿登记维护' },
    component: () => import('@/views/sendread/inform/update')
  },
  {
    path: '/sendread/message/report',
    meta: { icon: 'dashboard', title: '上报登记' },
    component: () => import('@/views/sendread/message/report')
  },
  {
    path: '/sendread/message/list',
    meta: { icon: 'dashboard', title: '上报查询' },
    component: () => import('@/views/sendread/message/list')
  },
  {
    path: '/sendread/message/update',
    meta: { icon: 'dashboard', title: '上报维护' },
    component: () => import('@/views/sendread/message/update')
  },
  {
    path: '/sendread/adopt/report',
    meta: { icon: 'dashboard', title: '信息采编' },
    component: () => import('@/views/sendread/adopt/report')
  },
  {
    path: '/sendread/adopt/list',
    meta: { icon: 'dashboard', title: '采编维护' },
    component: () => import('@/views/sendread/adopt/list')
  },
  {
    path: '/sendread/adopt/mark',
    meta: { icon: 'dashboard', title: '加分因子维护' },
    component: () => import('@/views/sendread/adopt/mark')
  },
  {
    path: '/sendread/adopt/domain',
    meta: { icon: 'dashboard', title: '领域分类维护' },
    component: () => import('@/views/sendread/adopt/domain')
  },
  {
    path: '/sendread/statistics',
    meta: { icon: 'dashboard', title: '送阅统计' },
    component: () => import('@/views/sendread/statistics/list')
  },
  {
    path: '/sendread/worklist',
    meta: { icon: 'dashboard', title: '送阅工作组' },
    component: () => import('@/views/sendread/worklist')
  },
  {
    path: '/jobinfor/worklist',
    meta: { icon: 'dashboard', title: '政协工作信息工作组' },
    component: () => import('@/views/sendread/worklist')
  },
  {
    path: '/usageSituation',
    meta: { icon: 'dashboard', title: '使用情况统计' },
    component: () => import('@/views/usageSituation')
  },
  {
    path: '/council/synchronizationInfo',
    meta: { icon: 'dashboard', title: '同步信息' },
    component: () => import('@/views/council/synchronizationInfo'),
  }, 
]