<template>
  <div :id="id" class="echarts" :class="[this.nClass, 'echarts']"></div>
</template>
<script>
import * as echarts from "echarts";
function guid() {
  return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, function (c) {
    var r = (Math.random() * 16) | 0,
      v = c == "x" ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
}
export default {
  name: "v-echarts",
  props: {
    Option: {
      type: Object,
      default: {},
    },
    nClass: {
      type: String,
      default: "dPage",
    },
  },
  data() {
    return {
      id: "echarts" + guid(),
    };
  },
  // methods 是一些用来更改状态与触发更新的函数
  // 它们可以在模板中作为事件监听器绑定
  watch: {
    Option: {
      deep: true, // 深度监听
      handler(newVal, oldVal) {
        // console.log(newVal, oldVal);
        if (JSON.stringify(newVal) != JSON.stringify(oldVal)) {
          this.getEcharts();
        }
      },
    },
  },
  methods: {
    getEcharts() {
      let ele = document.getElementById(this.id);
      if (ele) {
        let _this = this;
        let chart = echarts.init(ele);
        chart.clear();
        // console.log(this.Option);
        chart.setOption(this.Option);
        chart.off("click");
        chart.on("click", function (param) {
          // console.log(param);
          _this.$emit("click", param);
        }); //X轴的值
        setTimeout(() => {
          window.onresize = function () {
            chart.resize();
          };
        }, 200);
      }
    },
  },
  // 生命周期钩子会在组件生命周期的各个不同阶段被调用
  // 例如这个函数就会在组件挂载完成后被调用
  mounted() {
    this.getEcharts();
  },
};
</script>
<style>
.echarts {
  width: 100%;
  /* min-height: 200px; */
  height: 100%;
}
</style>
