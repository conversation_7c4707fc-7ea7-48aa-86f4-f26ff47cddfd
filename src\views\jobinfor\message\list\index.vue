<template>
  <div class="cp-page">
    <a-row>
      <a-col span="8" class="sear">
        <span>信息标题：</span>
        <div>
          <a-input v-model="search.title" placeholder="请输入" style="width: 100%" />
        </div>
      </a-col>
      <a-col span="8" class="sear">
        <span>报送时间：</span>
        <div>
          <a-date-picker v-model="search.reportStartDate" style="width: 47%" />
          <span style="width: 6%; display: inline-block; text-align: center"> 至 </span>
          <a-date-picker v-model="search.reportEndDate" style="width: 47%" />
        </div>
      </a-col>
      <a-col span="3" class="sear">
        <span>年号：</span>
        <div>
          <a-input-number v-model="search.oaYear" style="width: 100%" />
        </div>
      </a-col>
      <a-col span="3" class="sear">
        <span>期号：</span>
        <div>
          <a-input-number v-model="search.oaIssue" style="width: 100%" />
        </div>
      </a-col>
    </a-row>

    <div style="text-align: center; margin-bottom: 16px">
      <a-space>
        <a-button type="primary" @click="getList()">查询</a-button>
        <a-button
          type="primary"
          @click="
            () => {
              search = { oaYear: dayjs().year() };
              getList();
            }
          "
          >重置</a-button
        >
        <a-button type="primary" @click="exports()">导出</a-button>
      </a-space>
    </div>
    <a-table
      :dataSource="dataSource"
      :columns="columns"
      :pagination="pagination"
      @change="
        (obj) => {
          getList(obj.current, obj.pageSize);
        }
      "
    >
      <a slot="name" slot-scope="text, record" @click="addOpen(record)" :title="text">
        {{ text }}
      </a>
      <span slot="signIssueTime" slot-scope="text">
        {{ text ? dayjs(text).format("YYYY-MM-DD") : text }}
      </span>
      <span slot="createTime" slot-scope="text">
        {{ text ? dayjs(text).format("YYYY-MM-DD") : text }}
      </span>
      <span slot="action" slot-scope="text, record">
        <a @click="addOpen(record)">详情</a>
      </span>
    </a-table>
    <AddEdit ref="addEditRef" @callBack="getList" :isPublish="true" />
  </div>
</template>

<script>
import { auth as http } from "@zh/common-utils/http";
import AddEdit from "../update/components/addEdit.vue";
import dayjs from "dayjs";
export default {
  components: { AddEdit },
  data() {
    return {
      dayjs,
      dataSource: [{ id: "123456", name: 1231 }],
      columns: [
        {
          title: "信息标题",
          dataIndex: "oaTitle",
          width: 500,
          scopedSlots: { customRender: "name" },
        },
        {
          title: "报送时间",
          dataIndex: "year",
          key: "year",
        },
        {
          title: "报送单位",
          dataIndex: "year",
          key: "year",
        },
        {
          title: "采用情况",
          dataIndex: "year",
          key: "year",
        },
        {
          title: "采编状态",
          dataIndex: "year",
          key: "year",
        },
        {
          title: "操作",
          title: "操作",
          key: "action",
          scopedSlots: { customRender: "action" },
        },
      ],
      search: { oaYear: dayjs().year() },
      keyword: undefined,
      pageNum: 1,
      pageSize: 10,
      labelCol: { span: 3 },
      wrapperCol: { span: 20 },
      pagination: {},
    };
  },
  // mounted() {
  //   this.getList();
  // },
  activated() {
    this.getList();
  },
  methods: {
    getList(pageNum = 1, pageSize = 10) {
      let temp = { ...this.search };
      Object.keys(temp).forEach((key) => {
        if (key.includes("Date") && temp[key]) {
          temp[key] = dayjs(temp[key]).valueOf();
        }
      });
      http
        .post("/workOa/list", {
          ...temp,
          pageType: 1,
          pageNum: pageNum || this.pageNum,
          pageSize: pageSize || this.pageSize,
        })
        .then((res) => {
          const { currPage, pageSize, list, totalCount } = res;
          this.dataSource = list;
          this.pageNum = currPage;
          this.pageSize = pageSize;
          this.pagination = {
            current: currPage,
            total: totalCount,
          };
        });
    },
    addOpen(record) {
      this.$refs.addEditRef.open(record["id"]);
    },
    exports() {
      let temp = { ...this.search };
      Object.keys(temp).forEach((key) => {
        if (key.includes("Date") && temp[key]) {
          temp[key] = dayjs(temp[key]).valueOf();
        }
      });
      http
        .post(
          "/workOa/noPublishExport",
          {
            ...temp,
            pageType: 1,
            pageNum: this.pageNum,
            pageSize: this.pageSize,
          },
          {
            responseType: "blob",
          }
        )
        .then((res) => {
          console.log("🚀 ~ file: index.vue:183 ~ exports ~ res:", res);
          let contentDisposition = decodeURI(res.headers["content-disposition"]) || "";
          const blob = res.data;
          const url = window.URL.createObjectURL(blob);

          let link = document.createElement("a");

          link.style.display = "none";

          link.href = url;

          link.setAttribute("download", contentDisposition.substring(20)); //文件名后缀记得添加，我写的zip

          document.body.appendChild(link);

          link.click();

          document.body.removeChild(link); //下载完成移除元素

          window.URL.revokeObjectURL(url); //释放掉blob对象
        });
    },
  },
};
</script>

<style scoped lang="less">
.sear {
  display: flex;
  align-items: center;
  font-size: 14px;
  & > span:first-child {
    width: 80px;
    text-align: right;
  }
  & > :last-child {
    flex: 1;
  }
}
.ant-row {
  margin-bottom: 16px;
}
</style>
