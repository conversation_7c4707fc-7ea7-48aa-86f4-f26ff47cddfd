{"name": "cppcc", "version": "1.0.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "themes:rebuild": "node src/themes/build.js", "themes:rollback": "node src/themes/rollback.js", "themes:replace": "node src/themes/replace.js", "build-send": "vue-cli-service build --mode send", "build-sendins": "vue-cli-service build --mode sendins", "build-work": "vue-cli-service build --mode work", "build-workins": "vue-cli-service build --mode workins", "build-mem": "vue-cli-service build --mode mem", "build-memins": "vue-cli-service build --mode memins", "build-intranet": "vue-cli-service build --mode intranet", "build-outernet": "vue-cli-service build --mode outernet", "build-formal": "vue-cli-service build --mode formal", "build-formalouter": "vue-cli-service build --mode formalouter", "build-formaluser": "vue-cli-service build --mode formaluser", "build-formaluserouter": "vue-cli-service build --mode formaluserouter"}, "dependencies": {"@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^1.0.2", "@zh/base-ant": "5.1.0", "add": "^2.0.6", "dayjs": "^1.11.10", "echarts": "^5.4.3", "yarn": "^1.22.19"}, "devDependencies": {"@vue/cli-plugin-babel": "4.5.19", "babel-plugin-import": "1.13.5", "babel-plugin-transform-remove-console": "6.9.4", "core-js": "^3.1.2", "git-revision-webpack-plugin": "^3.0.6"}, "browserslist": ["> 1%", "last 2 versions"]}