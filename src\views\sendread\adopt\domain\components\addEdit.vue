<template>
  <div>
    <a-modal
      :visible="visible"
      :destroyOnClose="true"
      :title="formState.id ? '领域分类维护' : '领域分类新增'"
      width="800px"
      @ok="handleOk"
      @cancel="() => cancel()"
      :bodyStyle="{ overflow: 'auto' }"
      :footer="false"
    >
      <a-form :form="form" :labelCol="labelCol" :wrapperCol="wrapperCol" @submit="handleOk">
        <div>
          <a-form-item label="领域分类">
            <a-input
              v-decorator="[
                'name',
                {
                  rules: [{ required: true, message: '请输入' }],
                },
              ]"
              placeholder="请输入"
            />
          </a-form-item>

          <a-form-item label="排序号">
            <a-input-number
              v-decorator="[
                'sort',
                {
                  rules: [{ required: true, message: '请输入' }],
                },
              ]"
              placeholder="请输入"
              style="width: 100%"
            />
          </a-form-item>

          <a-form-item label="说明">
            <a-input
              v-decorator="[
                'remark',
                {
                  rules: [{ required: false, message: '请输入' }],
                },
              ]"
              placeholder="请输入"
            />
          </a-form-item>
        </div>

        <a-form-item label="是否启用" v-if="formState.id">
          <a-select
            v-decorator="[
              'isEnable',
              {
                rules: [{ required: true, message: '请输入' }],
              },
            ]"
            allowClear
            style="width: 100%"
            placeholder="请选择"
          >
            <a-select-option :value="1"> 启用 </a-select-option>
            <a-select-option :value="0"> 停用 </a-select-option>
          </a-select>
        </a-form-item>

        <div style="text-align: center">
          <a-space>
            <a-button type="primary" style="margin-right: 36px" @click="handleOk"> 保存 </a-button>
            <a-button type="primary" @click="cancel"> 取消 </a-button>
          </a-space>
        </div>
      </a-form>
    </a-modal>
  </div>
</template>

<script>
import { auth as http } from "@zh/common-utils/http";

export default {
  components: {},
  props: {},
  data() {
    return {
      isDetail: false,
      form: this.$form.createForm(this, { name: "report_update" }),
      visible: false,
      labelCol: { span: 5 },
      wrapperCol: { span: 16 },
      formState: {
        isEnable: 1,
      },
    };
  },
  methods: {
    open(id) {
      if (id) {
        this.getDetail(id);
      }
      this.visible = true;
    },
    getDetail(id) {
      if (id) {
        http.get(`/syDict/find?id=${id}`).then((res) => {
          this.visible = true;
          this.formState = { isEnable: 1, ...res };
          setTimeout(() => {
            this.form.setFieldsValue({ ...this.formState });
          }, 200);
        });
      } else {
        this.formState = { isEnable: 1 };
        this.form.resetFields();
      }
    },
    cancel() {
      this.visible = false;
      this.formState = {};
      this.form.resetFields();
    },
    handleOk() {
      this.form.validateFields((err, value) => {
        if (!err) {
          console.info("success", this.img);
          let url = "/syDict/save";
          if (this.formState["id"]) {
            url = "/syDict/update";
          }
          http
            .post(url, {
              ...this.formState,
              ...value,
            })
            .then((res) => {
              this.cancel();
              this.$emit("callBack");
            });
        }
      });
      // this.cancel();
    },
  },
};
</script>
<style src="@wangeditor/editor/dist/css/style.css"></style>

<style scoped lang="less">
@import url("./addEdit.less");
</style>
