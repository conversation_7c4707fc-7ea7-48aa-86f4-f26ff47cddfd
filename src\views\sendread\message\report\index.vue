<template>
  <div class="cp-page">
    <a-form :form="form" :labelCol="labelCol" :wrapperCol="wrapperCol" @submit="handleOk">
      <a-form-item label="报送标题">
        <a-input
          v-decorator="[
            'title',
            {
              rules: [{ required: true, message: '请输入' }],
            },
          ]"
          placeholder="请输入"
        />
      </a-form-item>
      <a-row>
        <a-col :span="12">
          <a-form-item label="报送时间" :labelCol="{ span: 8 }" :wrapperCol="{ span: 12 }">
            <a-date-picker
              v-model="formState.reportTime"
              v-decorator="[
                'reportTime',
                {
                  rules: [{ required: true, message: '请输入' }],
                },
              ]"
              style="width: 100%"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="报送单位（委员）" :labelCol="{ span: 6 }" :wrapperCol="{ span: 10 }">
            <zh-dict @change="bsChange" codeType="CPPCC_BSLX" v-decorator="['reportType', { rules: [{ required: true, message: '请选择' }] }]" />
          </a-form-item>
        </a-col>
      </a-row>
      <a-row>
        <a-col :span="12" v-if="formState.reportType == 3">
          <a-form-item label="参加单位名称" :labelCol="{ span: 8 }" :wrapperCol="{ span: 12 }">
            <a-input
              v-decorator="[
                'joinUnitName',
                {
                  rules: [{ required: true, message: '请输入' }],
                },
              ]"
              placeholder="请输入"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12" v-if="formState.reportType == 2">
          <a-form-item label="报送单位" :labelCol="{ span: 8 }" :wrapperCol="{ span: 12 }">
            <z-select-unit
              v-decorator="[
                'reportUnitId',
                {
                  rules: [{ required: true, message: '请输入' }],
                },
              ]"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item
            label="接收单位"
            :labelCol="{
              span: formState.reportType == 2 || formState.reportType == 3 ? 4 : 8,
            }"
            :wrapperCol="{ span: 12 }"
          >
            <z-select-unit
              v-decorator="[
                'receiverUnit',
                {
                  rules: [{ required: true, message: '请输入' }],
                },
              ]"
            />
          </a-form-item>
        </a-col>
      </a-row>
      <a-row>
        <a-col :span="8">
          <a-form-item label="撰稿人" :labelCol="{ span: 12 }" :wrapperCol="{ span: 10 }">
            <a-input
              v-decorator="[
                'copywriter',
                {
                  rules: [{ required: true, message: '请输入' }],
                },
              ]"
              style="width: 100%"
            />
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item label="撰稿电话" :labelCol="{ span: 8 }" :wrapperCol="{ span: 12 }">
            <a-input
              v-decorator="[
                'copywriterPhone',
                {
                  rules: [{ required: true, message: '请输入' }],
                },
              ]"
              placeholder="请输入"
            />
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item label="审核人" :labelCol="{ span: 6 }" :wrapperCol="{ span: 12 }" v-if="formState.reportType != 1 && formState.reportType != 4">
            <a-input
              v-decorator="[
                'auditorId',
                {
                  rules: [{ required: true, message: '请输入' }],
                },
              ]"
              placeholder="请输入"
            />
          </a-form-item>
        </a-col>
      </a-row>
      <a-form-item label="上报内容">
        <div
          style="border: 1px solid #ccc"
          v-decorator="[
            'reportContent',
            {
              rules: [{ required: false, message: '请输入' }],
            },
          ]"
        >
          <Toolbar style="border-bottom: 1px solid #ccc" :editor="editor" :defaultConfig="toolbarConfig" :mode="mode" />
          <Editor style="height: 300px; overflow-y: auto" v-model="html" :defaultConfig="editorConfig" :mode="mode" @onCreated="onCreated" />
        </div>
      </a-form-item>
      <a-form-item label="文件上传">
        <z-file-uploader
          v-decorator="[
            'fileList',
            {
              rules: [{ required: true, message: '请上传' }],
            },
          ]"
          :limit="1"
          accept=".doc,.docx,.wps,.Doc,.Docx,.Wps,.DOC,.DOCX,.WPS"
          configGroup="zx"
          :showUploadList="false"
          :parentId="formState.fileId"
          @input="handleChange"
          @FileBeforeUpload="FileBeforeUpload"
          @FileSuccessUpload="FileSuccessUpload"
          :handleRemove="handleRemove"
          v-model="formState.fileList"
          ref="fileUploader"
        />
      </a-form-item>

      <a-form-item label=" " :colon="false">
        <zh-progress :loading="loading">
          <a-table :dataSource="formState.fileList" :columns="columns">
            <span slot="type" slot-scope="text, record">
              {{ (record["name"] || record["fileName"]).split(".").pop() }}
            </span>
            <span slot="configGroup"> 原稿件 </span>
            <span slot="action" slot-scope="text, record">
              <a @click="down(record)">查看</a>
              <a-divider type="vertical" />
              <a style="color: red" @click="del(record)">删除</a>
            </span>
          </a-table>
        </zh-progress>
      </a-form-item>

      <div style="text-align: center">
        <a-space>
          <a-button type="primary" style="margin-right: 36px" @click="handleOk"> 直接上报 </a-button>
          <a-button type="primary" @click="cancel"> 取消 </a-button>
        </a-space>
      </div>
    </a-form>
  </div>
</template>

<script>
import { auth as http } from "@zh/common-utils/http";
import dayjs from "dayjs";
import { Editor, Toolbar } from "@wangeditor/editor-for-vue";
// 文件、图片类型
import MimeType from "@zh/common-utils/file/mime-type";

import { toolbarConfig, editorConfig } from "@/utils/common";

export default {
  components: { Editor, Toolbar },

  props: {
    pId: String,
    callBack: Function,
    session: Object,
  },
  data() {
    const user = this.$store.getLoginUser();
    console.log("🚀 ~ file: index.vue:241 ~ data ~ user:", user);
    return {
      user,
      isDetail: false,
      form: this.$form.createForm(this, { name: "report" }),
      visible: false,
      mimeType: MimeType,
      labelCol: { span: 4 },
      wrapperCol: { span: 16 },
      formState: {
        fileId: new Date().valueOf().toString(),
        copywriter: user.userName,
        copywriterPhone: user.tel,
        reportTime: dayjs(),
        receiverUnit: ["5"],
      },
      columns: [
        {
          title: "文件名称",
          dataIndex: "name",
          key: "name",
        },
        {
          title: "文件类型",
          scopedSlots: { customRender: "type" },
        },
        {
          title: "文稿类型",
          scopedSlots: { customRender: "configGroup" },
        },
        {
          title: "操作",
          width: 120,
          scopedSlots: { customRender: "action" },
        },
      ],

      editor: null,
      html: "",
      toolbarConfig: toolbarConfig,
      editorConfig: editorConfig,
      mode: "default", // or 'simple'
      unitName: "",
    };
  },
  beforeDestroy() {
    const editor = this.editor;
    if (editor == null) return;
    editor.destroy(); // 组件销毁时，及时销毁编辑器
  },
  mounted() {
    this.getUnit();
    this.form.setFieldsValue({ ...this.formState });
  },
  methods: {
    onCreated(editor) {
      this.editor = Object.seal(editor); // 一定要用 Object.seal() ，否则会报错
    },
    open(id) {
      this.visible = true;
    },
    cancel() {
      this.visible = false;
      this.html = "";
      const user = this.$store.getLoginUser();
      this.formState = {
        fileId: new Date().valueOf().toString(),
        copywriter: user.userName,
        copywriterPhone: user.tel,
        reportTime: dayjs(),
        receiverUnit: ["5"],
      };
      this.form.resetFields();
      this.form.setFieldsValue({ ...this.formState });
    },
    valueChange(list) {
      console.log(list, "lllllllllllllll");
    },
    handleOk() {
      this.form.setFieldsValue({ reportContent: this.editor.getHtml() });
      this.form.validateFields((err, value) => {
        if (!err) {
          console.info("success");
          Object.keys(value).forEach((key) => {
            if (key.includes("Date")) {
              value[key] = dayjs(value[key]).valueOf();
            }
          });
          let url = "/syReport/save";
          if (this.formState["id"]) {
            url = "/syReport/update";
          }
          let bool = true;
          if (value.fileList) {
            value.fileList.forEach((obj) => {
              if (obj["status"] != "done") {
                bool = false;
                return this.$notify.warn("文件正在上传中，请稍后进行保存");
              }
            });
          }
          if (bool) {
            http
              .post(url, {
                ...this.formState,
                ...value,
              })
              .then((res) => {
                this.html = "";
                this.cancel();
                this.$emit("callBack");
              });
          }
        }
      });
      // this.cancel();
    },
    // 修改附件
    handleChange(event, status) {
      console.log("🚀 ~ file: index.vue:275 ~ handleChange ~ event:", event);
      this.form.setFieldsValue({ fileList: event });
    },
    FileBeforeUpload(event, status) {
      this.loading = true;
    },
    FileSuccessUpload(event) {
      this.loading = false;
    },
    // 删除附件
    handleRemove(info) {
      console.log("🚀 ~ file: index.vue:280 ~ handleRemove ~ info:", info);
      this.form.setFieldsValue({
        fileList: undefined,
      });
    },
    down(record) {
      window.open(
        process.env.VUE_APP_READ +
          `?file=` +
          process.env.VUE_APP_READ_BASE_URL +
          `/sk/file/getJson?fileId=${record["fileId"] || record["uid"]}` +
          `%26serviceUrl=${process.env.VUE_APP_READ_BASE_URL}`
      );
    },
    del(record) {
      if (record["status"] != "done") {
        this.formState.fileId = new Date().valueOf();
      } else {
        this.$refs.fileUploader.handleFileRemoveDefault({
          ...record,
        });
      }
    },
    bsChange(val) {
      this.formState = {
        ...this.formState,
        reportType: val,
        copywriter: undefined,
        copywriterPhone: undefined,
        reportUnitId: undefined,
      };
      if (val == 4) {
        this.formState.copywriter = this.user.userName;
        this.formState.copywriterPhone = this.user.tel;
      } else if (val == 2) {
        this.formState.reportUnitId = [this.user.depts[0].unitId];
        setTimeout(() => {
          this.form.setFieldsValue({ ...this.formState });
        }, 200);
      } else if (val == 3) {
        this.formState.joinUnitName = this.unitName;
        setTimeout(() => {
          this.form.setFieldsValue({ ...this.formState });
        }, 200);
      }
      this.form.setFieldsValue({ ...this.formState });
    },
    getUnit() {
      http.get(`/syReport/getUserJoinUnit?userId=${this.user.id}`).then((res) => {
        this.unitName = res;
      });
    },
  },
};
</script>
<style src="@wangeditor/editor/dist/css/style.css"></style>

<style scoped lang="less">
@import url("./index.less");
</style>
