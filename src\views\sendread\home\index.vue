<template>
  <div class="page">
    <div class="b-card" style="width: 52%; height: 500px">
      <div class="title">
        采用情况

        <div class="t-search">
          <a-space>
            <a-select v-model="search2.cyYear" placeholder="请输入" style="width: 100px">
              <a-select-option v-for="(item, index) in yearData" :key="index" :value="item" :dataRef="item">
                {{ item }}
              </a-select-option>
            </a-select>
          </a-space>
        </div>
      </div>
      <div style="position: relative">
        <v-echarts style="height: 450px" :Option="mapJson" @click="mapClick" />
        <div style="position: absolute; left: 140px; top: 0px">
          <v-echarts style="padding: 0; height: 200px; width: 180px" :Option="getMap2()" @click="mapClick" />
        </div>
      </div>
    </div>
    <div class="b-card" style="width: 47%; margin-left: 1%; height: 500px">
      <div class="title">
        统计表
        <div class="t-search">
          <a-space>
            <a-select allowClear v-model="search.statisticsType" style="width: 100px" placeholder="请选择">
              <a-select-option value="1"> 市政协 </a-select-option>
              <a-select-option value="2"> 区县政协 </a-select-option>
            </a-select>
            <a-select v-model="search.year" placeholder="请输入" style="width: 100px">
              <a-select-option v-for="(item, index) in yearData" :key="index" :value="item" :dataRef="item">
                {{ item }}
              </a-select-option>
            </a-select>
          </a-space>
        </div>
      </div>
      <div style="margin-top: 16px">
        <div style="overflow-y: scroll; width: 100%">
          <table class="table">
            <colgroup width="100"></colgroup>
            <colgroup width="90"></colgroup>
            <colgroup width="90"></colgroup>
            <colgroup width="90"></colgroup>
            <colgroup width="100"></colgroup>
            <colgroup width="90"></colgroup>
            <thead>
              <tr style="font-size: 15px; font-weight: bold">
                <td>报送单位（委员）</td>
                <td>报送数量</td>
                <td>采编数量</td>
                <td>得分情况</td>
                <td>领导批示（人次）</td>
                <td>排名</td>
              </tr>
            </thead>
          </table>
        </div>
        <div style="height: 375px; width: 100%; overflow-y: scroll">
          <table class="table">
            <colgroup width="100"></colgroup>
            <colgroup width="90"></colgroup>
            <colgroup width="90"></colgroup>
            <colgroup width="90"></colgroup>
            <colgroup width="100"></colgroup>
            <colgroup width="90"></colgroup>
            <tbody>
              <tr v-for="(item, index) in dataSource" :key="index">
                <td>{{ item["unitName"] }}</td>
                <td @click="addOpen({ unitId: item['unitId'], reportType: item['reportType'], ...search })" style="cursor: pointer">
                  {{ item["bsTotal"] }}
                </td>
                <td @click="addOpen({ unitId: item['unitId'], reportType: item['reportType'], ...search, detailType: 1 })" style="cursor: pointer">
                  {{ item["cbTotal"] }}
                </td>
                <td>{{ item["dfTotal"] }}</td>
                <td>{{ item["ldpsCount"] }}</td>
                <td>{{ item["ranking"] }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <div class="b-card" style="width: 52%">
      <div class="title">
        <!-- 报送情况 -->
        报送及采编情况
        <div class="t-search">
          <a-space>
            <a-select v-model="search2.bsYear" placeholder="请输入" style="width: 100px">
              <a-select-option v-for="(item, index) in yearData" :key="index" :value="item" :dataRef="item">
                {{ item }}
              </a-select-option>
            </a-select>
          </a-space>
        </div>
      </div>
      <div>
        <v-echarts style="padding: 0; height: 250px" :Option="getBSQK()" />
      </div>
    </div>
    <div class="b-card" style="width: 47%; margin-left: 1%">
      <div class="title">
        领域分布

        <div class="t-search">
          <a-space>
            <!-- <a-select allowClear v-model="search2.statisticsType" style="width: 100px" placeholder="请选择">
              <a-select-option value="1"> 市政协 </a-select-option>
              <a-select-option value="2"> 区县政协 </a-select-option>
            </a-select> -->
            <a-select v-model="search2.year" placeholder="请输入" style="width: 100px">
              <a-select-option v-for="(item, index) in yearData" :key="index" :value="item" :dataRef="item">
                {{ item }}
              </a-select-option>
            </a-select>
          </a-space>
        </div>
      </div>
      <div>
        <v-echarts style="padding: 0; height: 250px" :Option="getLYFB()" />
      </div>
    </div>
    <AddEdit ref="addEditRef" @callBack="getList" />
  </div>
</template>

<script>
import * as echarts from "echarts";
import cq from "@/assets/json/cqmap.json";
import cqzc from "@/assets/json/cqzc.json";
import dayjs from "dayjs";
import { auth as http } from "@zh/common-utils/http";
import AddEdit from "../statistics/list/components/addEdit.vue";
export default {
  components: { AddEdit },
  data() {
    const user = this.$store.getLoginUser();
    echarts.registerMap("chinacq", cq); //注册可用的地图
    cq.features.forEach((item) => {
      echarts.registerMap(item.properties.name, {
        type: "FeatureCollection",
        features: [item],
      });
    });
    let year = dayjs().year();

    let data = new Array(5).fill("").map((obj, index) => year - index);
    return {
      data: {},
      mapJson: {},
      mapJson2: {},
      yearData: data,
      dataSource: [],
      qxData: [],
      portData: [],
      current: 1,
      total: 0,
      search: {
        statisticsType: "2",
        year,
      },
      search2: {
        // statisticsType: "2",
        year,
        cyYear: year,
        bsYear: year,
      },
    };
  },
  activated() {
    this.getList();
    this.getTyb();
    this.mapJson = this.getMap();
    this.mapJson2 = this.getMap2();
  },
  methods: {
    getList(pageNum = 1) {
      // 采用情况
      http
        .post("/syReport/collectCount", {
          pageNum: pageNum,
          pageSize: 20,
          year: this.search2.cyYear,
        })
        .then((res) => {
          this.qxData = res;
          this.mapJson = this.getMap();
          this.mapJson2 = this.getMap2();
        });
      // 报送情况
      http
        .post("/syReport/reportCount", {
          pageNum: pageNum,
          pageSize: 20,
          year: this.search2.bsYear,
        })
        .then((res) => {
          this.portData = res;
        });
      //领域分布
      http
        .post("/syReport/fieldTypeCount", {
          pageNum: pageNum,
          pageSize: 20,
          ...this.search2,
        })
        .then((res) => {
          console.log("🚀 ~ file: index.vue:165 ~ .then ~ res:", res);
          this.lyData = res;
        });
    },
    getTyb(pageNum = 1) {
      //统计表
      http
        .post("/syReport/collectEditStatistics", {
          pageNum: pageNum,
          pageSize: 20,
          ...this.search,
        })
        .then((res) => {
          this.dataSource = res["list"];
          this.current = res["currPage"] || 1;
          this.total = res["totalCount"];
        });
    },
    getBSQK() {
      let dataTemp = this.portData || [];
      let data = [];
      let data2 = [];
      let dataKey = [];
      if (dataTemp.length > 0) {
        dataTemp.forEach((obj) => {
          data.push(obj["count"]);
          data2.push(obj["count2"]);
          dataKey.push(obj["value"]);
        });
      } else {
        //        data = [120, 200, 150, 80, 70, 110, 130, 80, 70, 110, 130, 110, 130];
        //        dataKey = ["1月", "2月", "3月", "4月", "5月", "6月", "7月", "8月", "9月", "10月", "11月", "12月"];
      }
      return {
        grid: {
          top: 40,
          bottom: 40,
          // left: 40,
          // right: 20,
        },
        color: ["#0075FF"],
        tooltip: {
          trigger: "axis",
        },
        legend: {
          data: ["报送数", "采编数"],
        },
        xAxis: {
          type: "category",
          name: "(月份)",
          data: dataKey,
          axisTick: {
            //x轴刻度线
            show: false,
          },
          axisLabel: {
            show: true,
            // rotate: -25, // 字体倾斜
            // align: "center", //标识中间显示
            // margin: 15, //距离X轴线的距离
            fontSize: 12,
          },
        },
        yAxis: {
          type: "value",
          name: "(篇)",
          splitLine: {
            lineStyle: {
              type: "dashed", //虚线
            },
            show: true, //隐藏
          },
        },
        series: [
          {
            name: "报送数",
            data: data,
            type: "bar",
            barWidth: 16,
            label: {
              show: true,
              position: "top",
            },
          },
          {
            name: "采编数",
            data: data2,
            type: "bar",
            barWidth: 16,
            label: {
              show: true,
              position: "top",
            },
          },
        ],
      };
    },
    getMap(mapName = "chinacq") {
      let aspectScale = 1;
      let zoom = 1.2;
      if (mapName != "chinacq") {
        zoom = 0.75;
      }
      return {
        tooltip: {
          formatter: (params, ticket, callback) => {
            let { name, value } = params;
            return name + "<br/>" + "采用情况：" + (value || 0);
          },
        },
        series: [
          {
            name: "重庆",
            type: "map", //配置显示方式为用户自定义
            map: mapName,
            aspectScale,
            zoom,
            layoutCenter: ["50%", "50%"],
            // 如果宽高比大于 1 则宽度为 450，如果小于 1 则高度为 450，保证了不超过 450x450 的区域
            layoutSize: 450,
            itemStyle: {
              color: "#B7D9FC",
              areaColor: "#B7D9FC",
              borderWidth: 1, //设置外层边框
              // borderColor: 'rgba(255,255,255,0.6)',
              borderColor: "white",
            },
            label: {
              show: true,
            },
            select: {
              disabled: true,
            },
            data: cq.features.map((item) => {
              let name = item.properties.name;
              let id = item.properties.id;
              let show = true;
              if (["渝中区", "大渡口区", "江北区", "沙坪坝区", "九龙坡区", "南岸区", "北碚区", "渝北区", "巴南区", "两江新区", "高新区"].includes(name)) {
                show = false;
              }
              if (mapName != "chinacq") {
                show = true;
              }
              let color = "#0075FF";
              let find = {};
              if (this.qxData) {
                find = this.qxData.find((obj) => obj["key"] == id) || {};
              }
              // let index = 50;
              // let color = "#B7D9FC";
              // if (index > -1) {
              //   if (index < 10) {
              //     color = "#0075FF";
              //   } else if (index < 20) {
              //     color = "#48A4FF";
              //   }
              // }
              return {
                name: item.properties.name,
                id: (item.properties && item.properties.id) || item.id,
                itemStyle: {
                  color: color,
                  areaColor: color,
                },
                label: {
                  show,
                  color: "white",
                  fontSize: mapName == "chinacq" ? 12 : 20,
                },
                value: find["count"] || 0,
                serverId: find["id"],
              };
            }),
          },
        ],
      };
    },
    mapClick(para) {
      console.log(para.data, "poooooooooooooo");
      if (!para.data["serverId"]) {
        const h = this.$createElement;

        return this.$info({
          class: "infos",
          icon: "",
          closable: true,
          title: "提示",
          content: h("div", {}, [h("p", "该区县暂无采用情况。")]),
          onOk() {},
        });
      }
      this.addOpen({ unitId: para.data["serverId"], reportType: "2", year: this.search2.cyYear, detailType: 1 });
      // this.mapJson = this.getMap(para.name);
      // this.isArea = true;
      // this.unitLevelCode = para.data.id;
      // this.getData();
      // this.getData7();
    },
    mapReset() {
      this.mapJson = this.getMap();
      this.isArea = false;
      this.unitLevelCode = undefined;
      this.getData(true);
      this.getData7();
    },
    getMap2() {
      echarts.registerMap("cqzc", cqzc); //注册可用的地图
      let zoom = 1.2;
      let map = "cqzc",
        aspectScale = 1;

      let findszx = {};
      if (this.qxData) {
        findszx = this.qxData.find((obj) => obj["key"] == "00010001") || {};
      }
      return {
        tooltip: {
          formatter: (params, ticket, callback) => {
            let { name, value, data = {} } = params;
            if (!name) {
              return "市政协" + "<br/>" + "采用情况：" + data["tValue"];
            }
            return name + "<br/>" + "采用情况：" + value;
          },
        },
        series: [
          {
            name: "重庆",
            type: "map", //配置显示方式为用户自定义
            map,
            zoom, // 地图比例
            aspectScale,
            itemStyle: {
              color: "#B7D9FC",
              areaColor: "#B7D9FC",
              borderWidth: 1, //设置外层边框
              // borderColor: 'rgba(255,255,255,0.6)',
              borderColor: "white",
            },
            label: {
              show: true,
            },
            select: {
              disabled: true,
            },
            data: cqzc.features.map((item) => {
              let name = item.properties.name;
              let id = item.properties.id;
              let offset = [0, 0];
              if (["沙坪坝区"].includes(name)) {
                offset = [-10, 0];
              }
              if (["九龙坡区"].includes(name)) {
                offset = [-10, -10];
              }
              if (["大渡口区"].includes(name)) {
                offset = [0, 5];
              }
              if (["江北区"].includes(name)) {
                offset = [10, 0];
              }
              let color = "#0075FF";
              let find = {};
              if (this.qxData) {
                find = this.qxData.find((obj) => obj["key"] == id) || {};
              }
              // let index = 50;
              // if (this.data.chart_quxian) {
              //   index = this.data.chart_quxian.findIndex((obj) => obj["value"] == name);
              // }
              // let color = "#B7D9FC";
              // if (index > -1) {
              //   if (index < 10) {
              //     color = "#0075FF";
              //   } else if (index < 20) {
              //     color = "#48A4FF";
              //   }
              // }
              return {
                name: item.properties.name,
                id: (item.properties && item.properties.id) || item.id,
                itemStyle: {
                  color: color,
                  areaColor: color,
                },
                label: {
                  offset, //是否对文字进行偏移。默认不偏移。例如：[30, 40] 表示文字在横向上偏移 30，纵向上偏移 40。
                  show: true,
                  color: "white",
                },
                value: find["count"] || 0,
                serverId: find["id"],
              };
            }),
            markPoint: {
              symbol: "circle",
              symbolSize: 8,
              itemStyle: {
                color: "red",
              },
              data: [
                {
                  coord: [106.712851, 29.731451],
                  tValue: findszx["count"] || 0,
                },
              ],
            },
          },
        ],
      };
    },
    getLYFB() {
      let dataTemp = this.lyData || [];
      let data = [];
      if (dataTemp.length > 0) {
        dataTemp.forEach((obj) => {
          data.push({
            value: obj["count"],
            name: obj["name"],
            children: obj["childList"],
          });
        });
      } else {
        // data = [
        //   {
        //     value: 1048,
        //     name: "经济",
        //   },
        //   { value: 735, name: "政治" },
        //   { value: 580, name: "社会" },
        //   { value: 484, name: "文化" },
        //   { value: 300, name: "生态文明" },
        // ];
      }
      return {
        // color: ["#FF844D", "#0075FF", "#10E7E4", "#15CE89", "#FFBB00"],
        tooltip: {
          trigger: "item",
          formatter(param) {
            const { children = [] } = param.data;
            // console.log("🚀 ~ formatter ~ param:", param);
            let name = "";
            if (children.length > 0) {
              children.forEach((val, index) => {
                name += `${index + 1}` + val + "<br/>";
              });
            }
            // correct the percentage
            return `${param.seriesName}` + "<br/>" + param.marker + param.name + "  " + param.data.value + "<br/>" + name;
          },
        },
        legend: {
          // orient: "vertical",
          y: "bottom",
          textStyle: {
            fontSize: 16,
          },
          data: data.map((item) => {
            return {
              icon: "circle",
              name: item.name,
            };
          }),
        },
        series: [
          {
            name: "领域分布",
            type: "pie",
            center: ["50%", "42%"],
            // radius: ["55%", "80%"],
            // avoidLabelOverlap: false,
            left: 0,
            itemStyle: {
              borderColor: "#fff",
              borderWidth: 3,
            },
            label: {
              show: true,
              formatter(param) {
                // correct the percentage
                return param.name + "：" + param.percent + "%";
              },
              fontSize: 14,
            },
            data: data,
          },
        ],
      };
    },
    addOpen(record) {
      let temp = { ...record };
      Object.keys(temp).forEach((key) => {
        if (key.includes("Time") && temp[key]) {
          temp[key] = dayjs(temp[key]).valueOf();
        }
      });
      this.$refs.addEditRef.open(temp);
    },
  },
  watch: {
    search: {
      deep: true, // 深度监听
      //   immediate: true,
      handler: function (newValue, old) {
        console.log("🚀 ~ file: index.vue:378 ~ newValue:", newValue);
        this.getTyb();
      },
    },
    search2: {
      deep: true, // 深度监听
      //   immediate: true,
      handler: function (newValue, old) {
        console.log("🚀 ~ file: index.vue:378 ~ newValue:", newValue);
        this.getList();
      },
    },
  },
};
</script>

<style lang="less" scoped>
v-deep .ant-spin-container {
  background: #f1f5f8;
}
.b-card {
  width: 100%;
  height: 300px;
  background: #ffffff;
  border-radius: 6px;
  padding: 18px;
  margin-bottom: 1%;
  .title {
    font-size: 18px;
    font-family: Microsoft YaHei;
    font-weight: bold;
    color: #0f2757;
  }
}

.page {
  display: flex;
  background: #f1f5f8;
  justify-content: center;
  padding-top: 1%;
  flex-direction: row;
  flex-wrap: wrap;
}

.table {
  width: 100%;
  margin: auto;
  table {
    table-layout: fixed;
  }

  td {
    border: 1px solid #b7b7b7;
    text-align: center;
    height: 40px;
  }
  thead > tr > td {
    height: 50px;
  }
}

.t-search {
  float: right;
  display: inline-block;
}
</style>
