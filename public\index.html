<!DOCTYPE html>
<html lang="zh-cmn-<PERSON>">

<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width,initial-scale=1.0">
  <link rel="icon" href="./logo.png">
  <title></title>
  <script>
    function getBrowserInfo() {
      var agent = navigator.userAgent.toLowerCase()
      var regStr_ie = /msie [\d.]+/gi
      var regStr_ie11 = /trident\/[\d.]+/gi
      var regStr_ff = /firefox\/[\d.]+/gi
      var regStr_chrome = /chrome\/[\d.]+/gi
      var regStr_saf = /safari\/[\d.]+/gi
      var match = null
      // IE
      if (agent.indexOf('msie') > 0) {
        match = agent.match(regStr_ie)[0]
        match = match.split(' ')
        return {
          kernel: 'ie',
          version: match[1]
        }
      }
      if (agent.indexOf('windows nt') > 0 && agent.indexOf('trident') > 0) {
        match = agent.match(regStr_ie11)[0]
        match = match.split('/')
        return {
          kernel: 'ie',
          version: 11
        }
      }
      // firefox
      if (agent.indexOf('firefox') > 0) {
        match = agent.match(regStr_ff)[0].split('/')
        return {
          kernel: match[0],
          version: match[1]
        }
      }
      // Chrome
      if (agent.indexOf('chrome') > 0) {
        match = agent.match(regStr_chrome)[0].split('/')
        return {
          kernel: match[0],
          version: match[1]
        }
      }
      // Safari
      if (agent.indexOf('safari') > 0 && agent.indexOf('chrome') < 0) {
        match = agent.match(regStr_saf)[0].split('/')
        return {
          kernel: match[0],
          version: match[1]
        }
      }
    }

    var browser = getBrowserInfo()
    if (!browser) {
      location.href = "/download.html"
    } else if (browser.kernel == 'ie' && parseInt(browser.version) < 11) {
      location.href = "/download.html"
    } else if (browser.kernel == 'chrome' && parseInt(browser.version) < 40) {
      location.href = "/download.html"
    }
  </script>
  <style>
    .first-loading-wrp {
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      /* min-height: 420px; */
      height: 98vh
    }

    .first-loading-wrp>h1 {
      font-size: 128px
    }

    .first-loading-wrp .loading-wrp {
      padding: 98px;
      display: flex;
      justify-content: center;
      align-items: center
    }

    .dot {
      animation: antRotate 1.2s infinite linear;
      transform: rotate(45deg);
      position: relative;
      display: inline-block;
      font-size: 32px;
      width: 32px;
      height: 32px;
      box-sizing: border-box
    }

    .dot i {
      width: 14px;
      height: 14px;
      position: absolute;
      display: block;
      background-color: #1890ff;
      border-radius: 100%;
      transform: scale(.75);
      transform-origin: 50% 50%;
      opacity: .3;
      animation: antSpinMove 1s infinite linear alternate
    }

    .dot i:nth-child(1) {
      top: 0;
      left: 0
    }

    .dot i:nth-child(2) {
      top: 0;
      right: 0;
      -webkit-animation-delay: .4s;
      animation-delay: .4s
    }

    .dot i:nth-child(3) {
      right: 0;
      bottom: 0;
      -webkit-animation-delay: .8s;
      animation-delay: .8s
    }

    .dot i:nth-child(4) {
      bottom: 0;
      left: 0;
      -webkit-animation-delay: 1.2s;
      animation-delay: 1.2s
    }

    @keyframes antRotate {
      to {
        -webkit-transform: rotate(405deg);
        transform: rotate(405deg)
      }
    }

    @-webkit-keyframes antRotate {
      to {
        -webkit-transform: rotate(405deg);
        transform: rotate(405deg)
      }
    }

    @keyframes antSpinMove {
      to {
        opacity: 1
      }
    }

    @-webkit-keyframes antSpinMove {
      to {
        opacity: 1
      }
    }
  </style>
  <!-- require cdn assets css -->
  <% for (var i in htmlWebpackPlugin.options.cdn && htmlWebpackPlugin.options.cdn.css) { %>
    <link rel="stylesheet" href="<%= htmlWebpackPlugin.options.cdn.css[i] %>" />
    <% } %>

      <script src='https://zd-wpkgate-emas.bigdatacq.com:32383/static/wpk-jssdk.1.0.2/wpkReporter.js'
        crossorigin='true'></script>
      <script>
        function getQueryString(name) {
          let reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
          let r = window.location.search.substr(1).match(reg);
          if (r != null) {
            return unescape(r[2]);
          };
          return null;
        }
        try {
          let bind = '';
          let path = window.location.pathname;
          // network=1 为政务外网环境访问
          let network = getQueryString('network');
          if (network != undefined) {
            sessionStorage.setItem('network', network)
          } else {
            network = sessionStorage.getItem('network')
          }
          console.log('network', network)
          if (path.startsWith("/sendread")) {
            // 送阅（互联网）
            bind = "sy-cqszxwyh_kmift2ec_orm4loju";
            if (network == '1') {
              // 送阅（政务外网）
              bind = 'syz-szx_kmift2ec_orm4loju'
            }
          } else if (path.startsWith("/jobinfor")) {
            // 政协工作信息（互联网）
            bind = "zxgzxx-cqszxwyh_kmift2ec_orm4loju";
            if (network == '1') {
              // 政协工作信息（政务外网） 
              bind = 'zxgzxxz-szx_kmift2ec_orm4loju'
            }
          }
          const config = {
            bid: bind,
            signkey: '1234567890abcdef',
            gateway: 'https://zd-wpkgate-emas.bigdatacq.com:32383'
          };
          const wpk = new wpkReporter(config);
          wpk.installAll();
          window._wpk = wpk;
        } catch (err) {
          console.error('WpkReporter init fail', err);
        }
      </script>

      <% for (var i in htmlWebpackPlugin.options.cdn && htmlWebpackPlugin.options.cdn.css) { %>
        <link rel="stylesheet" href="<%= htmlWebpackPlugin.options.cdn.css[i] %>" />
        <% } %>
</head>

<body>
  <% for (var i in htmlWebpackPlugin.options.cdn && htmlWebpackPlugin.options.cdn.less) { %>
    <link rel="stylesheet/less" type="text/css" href="<%= htmlWebpackPlugin.options.cdn.less[i] %>" />
    <% } %>
      <div id="app">
        <div class="first-loading-wrp">
          <div class="loading-wrp" style="flex-direction: column">
            <div>
              <img src="/logo.png" style="width: 90px; height: 90px" />
            </div>
            <div style="margin: 20px">
              <span class="dot dot-spin">
                <i></i>
                <i></i>
                <i></i>
                <i></i>
              </span>
            </div>
            <div style="font-size: 24px; color: #333">重庆数字政协</div>

          </div>
        </div>
      </div>
      <!-- require cdn assets js -->
      <% for (var i in htmlWebpackPlugin.options.cdn && htmlWebpackPlugin.options.cdn.js) { %>
        <script type="text/javascript" src="<%= htmlWebpackPlugin.options.cdn.js[i] %>"></script>
        <% } %>
          <!-- built files will be auto injected -->
</body>

</html>