<template>
  <div class="cp-page">
    <div class="sear">
      <div>
        <span>报送标题：</span>
        <a-space>
          <a-input v-model="search.title" placeholder="请输入" style="width: 300px" />
        </a-space>
      </div>

      <div>
        <span>报送时间：</span>
        <a-space>
          <a-date-picker v-model="search.startTime" style="width: 120px" />
          <span>至</span>
          <a-date-picker v-model="search.endTime" style="width: 120px" />
        </a-space>
      </div>
      <div>
        <span>状态：</span>
        <a-space>
          <zh-dict codeType="CPPCC_SYZT" v-model="search.status" style="width: 100px" />
        </a-space>
      </div>
      <div>
        <a-space>
          <a-button type="primary" @click="getList(1)">查询</a-button>
          <a-button
            type="primary"
            @click="
              () => {
                search = {};
                getList(1);
              }
            "
            >重置</a-button
          >
        </a-space>
      </div>
    </div>
    <a-table
      :dataSource="dataSource"
      :columns="columns"
      :pagination="pagination"
      @change="
        (obj) => {
          getList(obj.current, obj.pageSize);
        }
      "
    >
      <a slot="name" slot-scope="text, record" @click="addOpen(record, record.status == '3')" :title="text">
        {{ record ? record["title"] : "" }}
      </a>
      <span slot="reportTime" slot-scope="text">
        {{ text ? dayjs(text).format("YYYY-MM-DD") : text }}
      </span>
      <span slot="action" slot-scope="text, record">
        <a-space>
        <a v-if="record.status != '3'" @click="addOpen(record)">修改</a>
        <a-popconfirm title="是否确认删除？" @confirm="del(record)">
            <a style="color: red">删除</a>
        </a-popconfirm>
     
        </a-space>
      </span>
    </a-table>
    <AddEdit ref="addEditRef" @callBack="getList" />
  </div>
</template>

<script>
import { auth as http } from "@zh/common-utils/http";
import AddEdit from "./components/addEdit.vue";
import dayjs from "dayjs";
export default {
  components: { AddEdit },
  data() {
    return {
      dayjs,
      dataSource: [],
      columns: [
        {
          title: "报送标题",
          dataIndex: "title",
          width: 600,
          scopedSlots: { customRender: "name" },
        },
        {
          title: "报送时间",
          dataIndex: "reportTime",
          key: "reportTime",
          scopedSlots: { customRender: "reportTime" },
        },
        {
          title: "报送单位/委员",
          dataIndex: "reportUnitName",
          key: "reportUnitName",
        },
        {
          title: "状态",
          dataIndex: "statusName",
          key: "statusName",
        },
        {
          title: "操作",
          key: "action",
          width: 120,
          scopedSlots: { customRender: "action" },
        },
      ],
      search: {
        title: undefined,
        status: undefined,
        startTime: undefined,
        endTime: undefined,
      },
      keyword: undefined,
      pageNum: 1,
      pageSize: 10,
      labelCol: { span: 3 },
      wrapperCol: { span: 20 },
      pagination: {},
    };
  },
  // mounted() {
  //   this.getList();
  // },
  activated() {
    this.getList();
  },
  methods: {
    getList(pageNum = 1, pageSize = 10) {
      let temp = { ...this.search };
      Object.keys(temp).forEach((key) => {
        if (key.includes("Time") && temp[key]) {
          temp[key] = dayjs(temp[key]).valueOf();
        }
      });
      http
        .post("/syReport/list", {
          ...temp,
          type: 2,
          pageNum: pageNum || this.pageNum,
          pageSize: pageSize || this.pageSize,
        })
        .then((res) => {
          const { currPage, pageSize, list, totalCount } = res;
          this.dataSource = list;
          this.pageNum = currPage;
          this.pageSize = pageSize;
          this.pagination = {
            current: currPage,
            total: totalCount,
          };
        });
    },
    addOpen(record, isDetail = false) {
      this.$refs.addEditRef.open(record["id"], isDetail);
    },
    del(record){
      http.get(`/syReport/delete?id=${record["id"]}`).then(() => {
        this.getList();
      });
    }
  },
};
</script>

<style scoped lang="less">
.sear {
  & > div {
    display: inline-block;
    margin-right: 36px;
    margin-bottom: 18px;
  }
}
</style>
