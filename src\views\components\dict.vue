<template>
  <a-tree-select
    :allowClear="true"
    v-model="valuePro"
    style="width: 100%"
    :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
    :tree-data="treeData"
    placeholder="请点击选择"
    tree-default-expand-all
    @change="valChange"
    :replaceFields="{
      children: 'children',
      title: 'codeName',
      key: 'code',
      value: 'code',
    }"
  >
  </a-tree-select>
</template>

<script>
import { auth as http } from "@zh/common-utils/http";
import storage from "@zh/common-utils/utils/storage";

export default {
   beforeDestroy() {
    this.valuePro = undefined
    this.$emit('change', undefined)
  },
  model: {
    prop: "value",
    event: "change",
  },
  props: {
    // 当前组件值
    // 默认是给 v-model 绑定值使用的
    codeType: {
      type: String,
    },
    value: {
      type: String,
      // 当通过 a-from 标签包裹时：
      // 这个参数是 v-decorator 给子组件传值用的
      // 这里不要给默认值， 在 form 下使用会爆警告 Warning: SquareUpload `default value` can not collect,  please use `option.initialValue` to set default value.
    },
    // 占位符
    placeholder: {
      type: String,
      default: () => undefined,
    },
    // 父节点禁用
    parentDisabled: {
      type: Boolean,
      default: () => false,
    },
    // 是否可修改
    disabled:{
      type: Boolean,
      default: () => false,
    },
    // 过滤
    filter: {
      type: Function,
      default: undefined,
    }
  },
  data() {
    return {
      treeExpandedKeys: [],
      valuePro: undefined,
      treeData: storage.get(this.codeType) || [],
      treeList: [],
    };
  },
  mounted() {
    this.getDict();
    // 如果父节点禁用
    if (this.parentDisabled) {
      if(storage.get(this.codeType) ){
        let currentTreeData = storage.get(this.codeType);
        // 遍历所有节点，禁用父节点
        this.disableParentNode(currentTreeData);
        this.treeData = currentTreeData;
      }
    }
    if(this.disabled) {
      if(storage.get(this.codeType) ){
        let currentTreeData = storage.get(this.codeType);
        // this.disableNode(currentTreeData);
        this.treeData = currentTreeData.map(item => {
          return {
            ...item,
            disabled: true // 禁用所有节点
          };
        });
      }
    }
    // 如果传了过滤函数
    if (this.filter) {
      this.treeData = this.filter(this.treeData);
    }
  },
  methods: {
    disableParentNode(data) {
      data.forEach((item) => {
        if (item.children && item.children.length > 0) {
          item.disabled = true;
          this.disableParentNode(item.children);
        }
      });
    },
    getDict() {
      if (this.codeType) {
        const data = JSON.parse(sessionStorage.getItem("list") || "[]");
        let find = data.find((obj) => obj == this.codeType);
        if (this.treeData.length == 0 || !find) {
          data.push(this.codeType);
          sessionStorage.setItem("list", JSON.stringify(data));
          http
            .get(`/dictCode/getByType?codeType=${this.codeType}`)
            .then((res) => {
              if (res) {
                this.treeList = res;
                storage.set(`${this.codeType}_list`, res);
                const result = this.jsonToTree(res);
                this.treeData = result;
                storage.set(this.codeType, result);
              } else {
                let data = JSON.parse(sessionStorage.getItem("list") || "[]");
                data = data.filter((obj) => obj != this.codeType);
                sessionStorage.setItem("list", JSON.stringify(data));
              }
            });
        }
      }
    },
    handleOk() {
      // this.cancel();
    },
    valChange(value, label, extra) {
      console.log(
        "🚀 ~ file: dict.vue:89 ~ valChange ~ value:",
        value,
        label,
        extra
      );
      if (!value) {
        this.$emit("change", value);
      }
    },
    jsonToTree(
      data = [],
      parent = "parentCode",
      code = "code",
      parentId = "-1"
    ) {
      let result = [],
        temp,
        lastData = data;
      for (let i = 0; i < data.length; i++) {
        if (lastData[i][parent] === parentId) {
          let nextData = [...lastData].filter(
            (obj) => obj[parent] !== lastData[i][parent]
          );
          let obj = Object.assign({}, { ...lastData[i], isLeaf: true });
          temp = this.jsonToTree(nextData, parent, code, lastData[i][code]);
          if (temp.length > 0) {
            obj.children = temp;
            obj.isLeaf = false;
          }
          result.push(obj);
        }
      }
      return result;
    },
    treeToList(data = [], delChild = false) {
      let resData = [];
      for (let obj of data) {
        let child = obj.children;
        if (delChild) {
          delete obj.children;
        }
        resData.push({ ...obj });
        if (child && child.length > 0) {
          resData = [...resData, ...treeToList(child, delChild)];
        }
      }
      return resData;
    },
  },
  watch: {
    // 监听当前值变化，及时提交给父组件
    valuePro: {
      immediate: true,
      handler: function (newValue) {
        // console.log("🚀 ~ file: dict.vue:90 ~ newValue:", newValue);
        // 记录最新的值
        this.valuePro = newValue;
        // 判断当前的 value 值是否为 undefined， 如果是的话不用抛出去，要不然 form 标签就会走效验，并提示对应的错误了
        // this.$emit("change", newValue);
        if (newValue !== undefined) {
          let list = storage.get(`${this.codeType}_list`);
          let find = list.find((obj) => obj["code"] == newValue);
          // v-decorator 会通过 change 事件接受新值
          this.$emit("change", newValue, find);
        }
      },
    },
    value: {
      deep: true,
      immediate: true,
      handler: function (newValue) {
        console.log("🚀 ~ file: dict.vue:129 ~ newValue:", newValue);
        this.valuePro = newValue;
      },
    },
  },
};
</script>

<style></style>
