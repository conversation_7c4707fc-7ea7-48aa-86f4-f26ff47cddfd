<template>
  <div>
    <a-modal
      :visible="visible"
      :destroyOnClose="true"
      title="工作组"
      width="800px"
      @ok="handleOk"
      @cancel="() => cancel()"
      :bodyStyle="{ overflow: 'auto' }"
      :footer="false"
    >
      <a-form :form="form" :labelCol="labelCol" :wrapperCol="wrapperCol" @submit="handleOk">
        <a-form-item label="名称">
          <a-input
            v-decorator="[
              'name',
              {
                rules: [{ required: true, message: '请输入' }],
              },
            ]"
            placeholder="请输入"
          />
        </a-form-item>
        <a-form-item label="说明">
          <a-input
            v-decorator="[
              'remark',
              {
                rules: [{ required: true, message: '请输入' }],
              },
            ]"
            style="width: 100%"
            placeholder="请输入"
          />
        </a-form-item>
        <a-form-item label="是否启用">
          <a-select
            v-decorator="[
              'isEnable',
              {
                rules: [{ required: true, message: '请输入' }],
              },
            ]"
            allowClear
            style="width: 100%"
            placeholder="请选择"
          >
            <a-select-option :value="1"> 启用 </a-select-option>
            <a-select-option :value="0"> 停用 </a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="排序">
          <a-input-number
            style="width: 100%"
            v-decorator="[
              'sort',
              {
                rules: [{ required: true, message: '请输入' }],
              },
            ]"
            placeholder="请输入"
          />
        </a-form-item>

        <div style="text-align: center">
          <a-space>
            <a-button style="margin-right: 36px" @click="cancel"> 取消 </a-button>
            <a-button type="primary" @click="handleOk"> 确定 </a-button>
          </a-space>
        </div>
      </a-form>
    </a-modal>
  </div>
</template>

<script>
import { auth as http } from "@zh/common-utils/http";
import dayjs from "dayjs";

export default {
  components: {},
  props: {},
  data() {
    const user = this.$store.getLoginUser();
    console.log("🚀 ~ file: addEdit.vue:250 ~ data ~ user:", user);
    return {
      user,
      isDetail: false,
      form: this.$form.createForm(this, { name: "report_update" }),
      visible: false,
      labelCol: { span: 4 },
      wrapperCol: { span: 16 },
      formState: {},
      editObj: {},
      dataSource: [],
      unitId: user?.depts[0].unitId,
      unitName: user?.depts[0].unitName,
      levelCode: user?.depts[0].levelCode,
    };
  },
  mounted() {},
  methods: {
    open(id) {
      if (id) {
        this.getDetail(id);
      }
      this.visible = true;
    },
    getDetail(id) {
      if (id) {
        http.get(`/personnelGroup/find?id=${id}`).then((res) => {
          this.visible = true;
          this.editObj = res;
          this.formState = res;
          setTimeout(() => {
            this.form.setFieldsValue({
              ...this.formState,
            });
          }, 200);
        });
      } else {
        this.formState = {
          copywriter: this.user.userName,
          copywriterPhone: this.user.tel,
          reportTime: dayjs(),
          receiverUnit: ["5"],
        };
        this.form.resetFields();
      }
    },
    cancel() {
      this.visible = false;
      this.formState = {
        fileId: new Date().valueOf().toString(),
        copywriter: this.user.userName,
        copywriterPhone: this.user.tel,
        reportTime: dayjs(),
        receiverUnit: ["5"],
      };
      this.editObj = {};
      this.form.resetFields();
    },
    handleOk() {
      this.form.validateFields((err, value) => {
        if (!err) {
          console.info("success");
          Object.keys(value).forEach((key) => {
            if (key.includes("Date")) {
              value[key] = dayjs(value[key]).valueOf();
            }
          });
          let url = "/personnelGroup/save";
          let para = {
            reportUserId: this.user?.id,
            reportUserName: this.user?.userName,
            reportUnitId: this.unitId,
            reportUnitLevelCode: this.levelCode,
            reportUnitName: this.unitName,
          };
          if (this.formState.id) {
            url = "/personnelGroup/update";
            para = {};
          }

          http
            .post(url, {
              ...para,
              bizType: this.$route.path.startsWith("/sendread") ? "1" : "2",
              id: this.editObj.id,
              ...this.formState,
              ...value,
            })
            .then((res) => {
              this.cancel();
              this.$emit("callBack");
            });
        }
      });
      // this.cancel();
    },
  },
};
</script>
<style src="@wangeditor/editor/dist/css/style.css"></style>

<style scoped lang="less">
@import url("./addEdit.less");
</style>
