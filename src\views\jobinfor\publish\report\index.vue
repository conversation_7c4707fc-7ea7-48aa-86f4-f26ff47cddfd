<template>
  <div class="cp-page">
    <a-form :form="form" :labelCol="labelCol" :wrapperCol="wrapperCol" @submit="handleOk">
      <a-form-item label="报送标题">
        <a-input
          v-decorator="[
            'titel',
            {
              rules: [{ required: true, message: '请输入' }],
            },
          ]"
          placeholder="请输入"
        />
      </a-form-item>
      <a-row>
        <a-col :span="8">
          <a-form-item label="报送时间" :labelCol="{ span: 12 }" :wrapperCol="{ span: 12 }">
            <a-date-picker
              v-decorator="[
                'reportDate',
                {
                  rules: [{ required: true, message: '请输入' }],
                },
              ]"
              style="width: 100%"
            />
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item label="报送单位" :labelCol="{ span: 10 }" :wrapperCol="{ span: 10 }">
            <z-select-unit
              :limit="1"
              :key="formState.fileId"
              v-if="isSel"
              @change="rpChange"
              v-decorator="[
                'reportUnitId',
                {
                  rules: [{ required: true, message: '请输入' }],
                },
              ]"
            />
            <a-input
              v-else
              v-decorator="[
                'reportUnitName',
                {
                  rules: [{ required: true, message: '请输入' }],
                },
              ]"
            />
          </a-form-item>
          <a-button type="primary" class="cut" @click="selChange">
            {{ isSel ? "填写" : "选择" }}
          </a-button>
        </a-col>
        <a-col :span="8">
          <a-form-item label="接收单位" :labelCol="{ span: 6 }" :wrapperCol="{ span: 12 }">
            <z-select-unit
              @change="acChange"
              v-decorator="[
                'acceptUnitId',
                {
                  rules: [{ required: true, message: '请输入' }],
                },
              ]"
            />
          </a-form-item>
        </a-col>
      </a-row>
      <a-row>
        <a-col :span="8">
          <a-form-item label="撰稿人" :labelCol="{ span: 12 }" :wrapperCol="{ span: 12 }">
            <a-input
              v-decorator="[
                'writerName',
                {
                  rules: [{ required: true, message: '请输入' }],
                },
              ]"
              style="width: 100%"
            />
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item label="撰稿电话" :labelCol="{ span: 10 }" :wrapperCol="{ span: 14 }">
            <a-input
              v-decorator="[
                'writerPhone',
                {
                  rules: [{ required: true, message: '请输入' }],
                },
              ]"
              placeholder="请输入"
            />
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item label="审核人" :labelCol="{ span: 6 }" :wrapperCol="{ span: 12 }">
            <a-input
              v-decorator="[
                'checkName',
                {
                  rules: [{ required: true, message: '请输入' }],
                },
              ]"
              placeholder="请输入"
            />
          </a-form-item>
        </a-col>
      </a-row>
      <a-form-item label="上报内容">
        <div
          style="border: 1px solid #ccc"
          v-decorator="[
            'content',
            {
              rules: [{ required: true, message: '请输入' }],
            },
          ]"
        >
          <Toolbar style="border-bottom: 1px solid #ccc" :editor="editor" :defaultConfig="toolbarConfig" :mode="mode" />
          <Editor style="height: 300px; overflow-y: auto" v-model="html" :defaultConfig="editorConfig" :mode="mode" @onCreated="onCreated" />
        </div>
      </a-form-item>
      <a-form-item label="文件上传">
        <z-file-uploader
          v-decorator="[
            'fileList',
            {
              rules: [{ required: true, message: '请上传' }],
            },
          ]"
          :limit="1"
          accept=".doc,.docx,.wps,.Doc,.Docx,.Wps,.DOC,.DOCX,.WPS"
          configGroup="zx"
          :showUploadList="false"
          :parentId="formState.fileId"
          @input="handleChange"
          @FileBeforeUpload="FileBeforeUpload"
          @FileSuccessUpload="FileSuccessUpload"
          :handleRemove="handleRemove"
          v-model="formState.fileList"
          ref="fileUploader"
        />
      </a-form-item>

      <a-form-item label=" " :colon="false">
        <zh-progress :loading="loading">
          <a-table :dataSource="formState.fileList" :columns="columns">
            <span slot="type" slot-scope="text, record">
              {{ (record["name"] || record["fileName"]).split(".").pop() }}
            </span>
            <span slot="configGroup"> 原稿件 </span>
            <span slot="action" slot-scope="text, record">
              <a @click="down(record)">查看</a>
              <a-divider type="vertical" />
              <a style="color: red" @click="del(record)">删除</a>
            </span>
          </a-table>
        </zh-progress>
      </a-form-item>

      <div style="text-align: center">
        <a-space>
          <a-button type="primary" style="margin-right: 36px" @click="handleOk"> 直接上报 </a-button>
          <a-button type="primary" @click="cancel"> 重置 </a-button>
        </a-space>
      </div>
    </a-form>
  </div>
</template>

<script>
import { auth as http } from "@zh/common-utils/http";
import dayjs from "dayjs";
import { Editor, Toolbar } from "@wangeditor/editor-for-vue";
// 文件、图片类型
import MimeType from "@zh/common-utils/file/mime-type";

import { toolbarConfig, editorConfig } from "@/utils/common";

export default {
  components: { Editor, Toolbar },

  props: {
    pId: String,
    callBack: Function,
    session: Object,
  },
  data() {
    const user = this.$store.getLoginUser();
    return {
      user,
      isDetail: false,
      isSel: true,
      form: this.$form.createForm(this, { name: "report" }),
      visible: false,
      mimeType: MimeType,
      labelCol: { span: 4 },
      wrapperCol: { span: 18 },
      tempState: {},
      formState: {
        fileId: new Date().valueOf().toString(),
        writerName: user.userName,
        writerPhone: user.tel,
        reportDate: dayjs(),
        acceptUnitId: ["5"],
      },
      columns: [
        {
          title: "文件名称",
          dataIndex: "name",
          key: "name",
        },
        {
          title: "文件类型",
          scopedSlots: { customRender: "type" },
        },
        {
          title: "操作",
          width: 120,
          scopedSlots: { customRender: "action" },
        },
      ],

      editor: null,
      html: "",
      toolbarConfig: toolbarConfig,
      editorConfig: editorConfig,
      mode: "default", // or 'simple'
      unitName: "",
    };
  },
  beforeDestroy() {
    const editor = this.editor;
    if (editor == null) return;
    editor.destroy(); // 组件销毁时，及时销毁编辑器
  },
  mounted() {
    this.form.setFieldsValue({ ...this.formState });
  },
  methods: {
    onCreated(editor) {
      this.editor = Object.seal(editor); // 一定要用 Object.seal() ，否则会报错
    },
    open(id) {
      this.visible = true;
    },
    cancel() {
      this.visible = false;
      const user = this.$store.getLoginUser();
      this.html = "";
      this.formState = {
        fileId: new Date().valueOf().toString(),
        writerName: user.userName,
        writerPhone: user.tel,
        reportDate: dayjs(),
        acceptUnitId: ["5"],
      };
      this.form.resetFields();
      this.form.setFieldsValue({ ...this.formState });
    },
    valueChange(list) {},
    selChange() {
      this.isSel = !this.isSel;
      if (this.isSel) {
        this.form.setFieldsValue({
          reportUnitName: undefined,
        });
      } else {
        this.form.setFieldsValue({
          reportUnitId: undefined,
        });
      }
    },
    acChange(ids, units) {
      this.tempState = {
        ...this.tempState,
        acceptUnitName: units.map((obj) => obj["name"]),
      };
    },
    rpChange(ids, units) {
      this.tempState = {
        ...this.tempState,
        reportUnitName: units.map((obj) => obj["name"]),
      };
    },
    handleOk() {
      this.form.setFieldsValue({ content: this.editor.getHtml() });
      this.form.validateFields((err, value) => {
        if (!err) {
          console.info("success", value);
          Object.keys(value).forEach((key) => {
            if (key.includes("Date")) {
              value[key] = dayjs(value[key]).valueOf();
            }
          });
          ["acceptUnitId", "reportUnitId"].forEach((key) => {
            if (value[key]) {
              value[key] = value[key].join(",");
            }
          });
          value["isSelect"] = this.isSel ? 1 : 0;
          let url = "/workReport/save";
          if (this.formState["id"]) {
            url = "/workReport/update";
          }
          let bool = true;
          if (value.fileList) {
            value.fileList.forEach((obj) => {
              if (obj["status"] != "done") {
                bool = false;
                return this.$notify.warn("文件正在上传中，请稍后进行保存");
              }
            });
          }
          if (bool) {
            http
              .post(url, {
                ...this.formState,
                ...value,
              })
              .then((res) => {
                this.html = "";
                this.cancel();
                this.$emit("callBack");
              });
          }
        }
      });
      // this.cancel();
    },
    // 修改附件
    handleChange(event, status) {
      console.log("🚀 ~ file: index.vue:275 ~ handleChange ~ event:", event, status);
      if (status == "done") {
        this.form.setFieldsValue({ fileList: event });
      }
      this.form.setFieldsValue({ fileList: event });
    },
    FileBeforeUpload(event, status) {
      this.loading = true;
    },
    FileSuccessUpload(event) {
      this.loading = false;
    },
    // 删除附件
    handleRemove(info) {
      console.log("🚀 ~ file: index.vue:280 ~ handleRemove ~ info:", info);
      this.form.setFieldsValue({
        fileList: undefined,
      });
    },
    down(record) {
      window.open(
        process.env.VUE_APP_READ +
          `?file=` +
          process.env.VUE_APP_READ_BASE_URL +
          `/sk/file/getJson?fileId=${record["fileId"] || record["uid"]}` +
          `%26serviceUrl=${process.env.VUE_APP_READ_BASE_URL}`
      );
    },
    del(record) {
      if (record["status"] != "done") {
        this.formState.fileId = new Date().valueOf();
      } else {
        this.$refs.fileUploader.handleFileRemoveDefault({
          ...record,
        });
      }
    },
    bsChange(val) {
      this.formState.reportType = val;
      this.formState.writerName = undefined;
      this.formState.writerPhone = undefined;
      this.formState.reportUnitId = undefined;
      if (val == 4) {
        this.formState.writerName = this.user.userName;
        this.formState.writerPhone = this.user.tel;
      } else if (val == 2) {
        this.formState.reportUnitId = [this.user.depts[0].unitId];
        setTimeout(() => {
          this.form.setFieldsValue({ ...this.formState });
        }, 200);
      } else if (val == 3) {
        this.formState.joinUnitName = this.unitName;
        setTimeout(() => {
          this.form.setFieldsValue({ ...this.formState });
        }, 200);
      }
      this.form.setFieldsValue({ ...this.formState });
    },
  },
};
</script>
<style src="@wangeditor/editor/dist/css/style.css"></style>

<style scoped lang="less">
@import url("./index.less");
.cut {
  position: absolute;
  right: 0;
  top: 4px;
}
</style>
