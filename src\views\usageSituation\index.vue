<template>
  <div class="log">
    <div style="padding: 20px">
      <a-space>
        <div>
          开始时间：
          <a-date-picker
            v-model="startTime"
            @change="
              (date) => {
                // console.log(e)
                startTime = date;
              }
            "
          />
        </div>
        <div>
          结束时间：
          <a-date-picker
            v-model="endTime"
            @change="
              (date) => {
                endTime = date;
              }
            "
          />
        </div>
        <div>
          区县：
          <a-select
            allowClear
            placeholder="请选择"
            v-model="geoCode"
            style="width: 100px"
            @change="
              (val) => {
                geoCode = val;
                getList();
              }
            "
          >
            <a-select-option
              v-for="(item, index) in treeData"
              :key="index"
              :value="item.code"
              :dataRef="item"
            >
              {{ item.name }}
            </a-select-option>
          </a-select>
        </div>
        <a-button type="primary" @click="onSearch">搜索</a-button>
        <a-button type="primary">重置</a-button>
      </a-space>
    </div>
    <!-- <table border="1" class="tables">
    
    </table> -->
    <table border="1" class="tables">
      <thead>
        <tr>
          <th colspan="2" rowspan="2">业务系统名称</th>
          <th colspan="2" rowspan="2">有效用户总数</th>
          <th colspan="2">pc端登录情况</th>
          <th colspan="2">移动端登录情况</th>
          <!-- // 选中重庆市时，表格才有 市政协委员登录情况 -->
          <th colspan="3" v-if="!geoCode || geoCode === '500000000000'">
            <span
              @click="
                openmo({
                  key: '1',
                  tit: '市政协委员按专委会登录情况',
                  type: 'cityLoginTitle',
                })
              "
              ><a>市政协委员登录情况</a></span
            >
          </th>
          <th colspan="3">
            <span
              @click="
                openmo({
                  key: '2',
                  tit: '区县政协登录情况',
                  type: 'districtLoginTitle',
                })
              "
              ><a>区县政协委员登录情况</a></span
            >
          </th>
          <th colspan="3">机关用户登录情况</th>
        </tr>
        <tr>
          <th>登录人数</th>
          <th>登录人次</th>
          <th>登录人数</th>
          <th>登录人次</th>
          <!-- =========================== -->
          <th v-if="geoCode === '500000000000'">登录人数</th>
          <th v-if="geoCode === '500000000000'">登录人次</th>
          <th v-if="geoCode === '500000000000'">未登录人数</th>
          <!-- =========================== -->
          <th>登录人数</th>
          <th>登录人次</th>
          <th>未登录人数</th>

          <th>登录人数</th>
          <th>登录人次</th>
          <th>未登录人数</th>
        </tr>
      </thead>
      <tbody>
        <tr v-for="item in data" :key="item.id">
          <td colspan="2">{{ item.systemName }}</td>
          <td colspan="2">
            <a
              @click="
                item.hasUsers > 0 &&
                  openmo({
                    key: '5',
                    type: 'hasUsers',
                    systemCode: item.systemCode,
                    tit: '有效用户总数',
                  })
              "
              >{{ item.hasUsers }}</a
            >
          </td>
          <td>
            <a
              @click="
              item.pcLoginUserCount > 0 &&
                openmo({
                  key: '5',
                  type: 'pcLoginUserCount',
                  systemCode: item.systemCode,
                  tit: 'pc端登录情况',
                })
              "
              >{{ item.pcLoginUserCount }}</a
            >
          </td>
          <td>
            <a
              @click="
              item.pcLoginTimes > 0 &&
                openmo({
                  key: '5',
                  type: 'pcLoginTimes',
                  systemCode: item.systemCode,
                  tit: 'pc端登录情况',
                })
              "
              >{{ item.pcLoginTimes }}</a
            >
          </td>
          <td>
            <a
              @click="
              item.mobileLoginUserCount > 0 &&
                openmo({
                  key: '5',
                  type: 'mobileLoginUserCount',
                  systemCode: item.systemCode,
                  tit: '移动端登录情况',
                })
              "
              >{{ item.mobileLoginUserCount }}</a
            >
          </td>
          <td>
            <a
              @click="
              item.mobileLoginTimes > 0 &&
                openmo({
                  key: '5',
                  type: 'mobileLoginTimes',
                  systemCode: item.systemCode,
                  tit: '移动端登录情况',
                })
              "
              >{{ item.mobileLoginTimes }}</a
            >
          </td>
          <!-- ============= -->
          <td v-if="geoCode === '500000000000'">
            <a
              @click="
              item.cityLoginUserCount > 0 &&
                openmo({
                  key: '3',
                  type: 'cityLoginUserCount',
                  systemCode: item.systemCode,
                  tit: '市政协委员登录情况',
                })
              "
              >{{ item.cityLoginUserCount }}</a
            >
          </td>
          <td v-if="geoCode === '500000000000'">
            <a
              @click="
              item.cityLoginTimes > 0 &&
                openmo({
                  key: '3',
                  type: 'cityLoginTimes',
                  systemCode: item.systemCode,
                  tit: '市政协委员登录情况',
                })
              "
              >{{ item.cityLoginTimes }}</a
            >
          </td>
          <td v-if="geoCode === '500000000000'">
            <a
              @click="
              item.cityNotLoginUserCount > 0 &&
                openmo({
                  key: '3',
                  type: 'cityNotLoginUserCount',
                  systemCode: item.systemCode,
                  tit: '市政协委员登录情况',
                })
              "
              >{{ item.cityNotLoginUserCount }}</a
            >
          </td>
          <!-- =================================== -->
          <td>
            <a
              @click="
              item.districtLoginUserCount > 0 &&
                openmo({
                  key: '4',
                  type: 'districtLoginUserCount',
                  systemCode: item.systemCode,
                  tit: '区县政协委员登录情况',
                })
              "
              >{{ item.districtLoginUserCount }}</a
            >
          </td>
          <td>
            <a
              @click="
              item.districtLoginTimes > 0 &&
                openmo({
                  key: '4',
                  type: 'districtLoginTimes',
                  systemCode: item.systemCode,
                  tit: '区县政协委员登录情况',
                })
              "
              >{{ item.districtLoginTimes }}</a
            >
          </td>
          <td>
            <a
              @click="
              item.districtNotLoginUserCount > 0 &&
                openmo({
                  key: '4',
                  type: 'districtNotLoginUserCount',
                  systemCode: item.systemCode,
                  tit: '区县政协委员登录情况',
                })
              "
              >{{ item.districtNotLoginUserCount }}</a
            >
          </td>

          <td>
            <a
              @click="
              item.agencyLoginUserCount > 0 &&
                openmo({
                  key: '5',
                  type: 'agencyLoginUserCount',
                  systemCode: item.systemCode,
                  tit: '机关用户登录情况',
                })
              "
              >{{ item.agencyLoginUserCount }}</a
            >
          </td>
          <td>
            <a
              @click="
              item.agencyLoginTimes > 0 &&
                openmo({
                  key: '5',
                  type: 'agencyLoginTimes',
                  systemCode: item.systemCode,
                  tit: '机关用户登录情况',
                })
              "
              >{{ item.agencyLoginTimes }}</a
            >
          </td>
          <td>
            <a
              @click="
              item.agencyNotLoginUserCount > 0 &&
                openmo({
                  key: '5',
                  type: 'agencyNotLoginUserCount',
                  systemCode: item.systemCode,
                  tit: '机关用户登录情况',
                })
              "
              >{{ item.agencyNotLoginUserCount }}</a
            >
          </td>
        </tr>
      </tbody>
    </table>
    <!-- <div style="text-align: right; padding-top: 20px">
      <a-pagination
        v-model="pagination.current"
        :total="pagination.total"
        show-less-items
        @change="changePage"
      />
    </div> -->

    <a-modal
      :title="title"
      :visible="visible"
      :destroyOnClose="true"
      width="1060px"
      @cancel="cancel"
      @ok="onOk"
      :bodyStyle="{ overflow: 'auto', 'max-height': '68vh',height: '68vh' }"
    >
      <a-table
        :scroll="{ y: 460 }"
        :columns="columns"
        :data-source="list"
        :pagination="pagination1"
        @change="changePage1"
        :loading="modalloading"
      >
        <span
          v-if="
            modalType == 'cityLoginTitle' || modalType == 'districtLoginTitle'
          "
          slot="loginMemCount"
          slot-scope="text, record"
        >
          <a @click="text>0&&addOpen(record, true, 'loginMemCount')">{{ text }}</a>
        </span>

        <span
          v-if="
            modalType == 'cityLoginTitle' || modalType == 'districtLoginTitle'
          "
          slot="notLoginMemCount"
          slot-scope="text, record"
        >
          <a @click="text>0&&addOpen(record, true, 'notLoginMemCount')">{{
            record["notLoginMemCount"]
          }}</a>
        </span>
      </a-table>
    </a-modal>
    <a-modal
      :title="title"
      :visible="visible1"
      :destroyOnClose="true"
      width="1060px"
      @cancel="cancel1"
      @ok="onOk1"
      :bodyStyle="{ overflow: 'auto', 'max-height': '68vh',height: '68vh' }"
    >
      <a-table
        :scroll="{ y: 460 }"
        :columns="columns1"
        :data-source="list2"
        :pagination="pagination2"
        @change="changePage1"
        :loading="modalloading"
      >
        <span
          v-if="
            modalType == 'cityLoginTitle' || modalType == 'districtLoginTitle'
          "
          slot="loginMemCount"
          slot-scope="text, record"
        >
          <a @click="text>0&&addOpen(record, true, 'loginMemCount')">{{ text }}</a>
        </span>

        <span
          v-if="
            modalType == 'cityLoginTitle' || modalType == 'districtLoginTitle'
          "
          slot="notLoginMemCount"
          slot-scope="text, record"
        >
          <a @click="text>0&&addOpen(record, true, 'notLoginMemCount')">{{
            record["notLoginMemCount"]
          }}</a>
        </span>
      </a-table>
    </a-modal>
  </div>
</template>

<script>
// import { getURLParam } from 'commons/src/utils'
import {
  login,
  getSmsCode,
  loginBySmsCode,
  loginKey,
} from "@zh/base-ant/framework/api/auth/index";
import storage from "@zh/common-utils/utils/storage";

import CaptchaImage from "@zh/base-ant/framework/components/CaptchaImage.vue";
import JSEncrypt from "jsencrypt";
import { auth as http } from "@zh/common-utils/http";
import moment from "moment";
export default {
  components: { CaptchaImage, JSEncrypt },
  data() {
    return {
      data: [
        {
          name: "1",
          nums: "222222",
        },
        {
          name: "1",
        },
        {
          name: "1",
        },
        {
          name: "1",
        },
        {
          name: "1",
        },
        {
          name: "1",
        },
        {
          name: "1",
        },
        {
          name: "1",
        },
        {
          name: "1",
        },
        {
          name: "1",
        },
        {
          name: "1",
        },
        {
          name: "1",
        },
        {
          name: "1",
        },
      ],
      title: "弹窗标题",
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0,
      },
      pagination1: {
        current: 1,
        pageSize: 10,
        total: 0,
      },
      pagination2: {
        current: 1,
        pageSize: 10,
        total: 0,
      },
      list: [],
      list2: [],
      columns: [],
      search: "",
      treeData: [],
      geoCode: "",
      startTime: moment().format("YYYY-MM-01"),
      endTime: moment().format("YYYY-MM-DD"),
      visible: false,
      visible1: false,
      pageNum: 1,
      pageSize: 10,
      modalType: "",
      modalTypeData: {},
      modalTypeData2: {}, // 点登录人数、未登录人数进入的弹窗，翻页需要的参数
      modalType2: "", // 点登录人数、未登录人数进入的弹窗，翻页需要的参数
      modalloading: false,
    };
  },
  created() {},
  mounted() {
    this.getList();
    http.get("/cppccSession/getUnitInfo").then((res) => {
      let data = res.filter((obj) => obj["code"] != "1" && obj["code"] != "3");
      this.treeData = data;
      this.geoCode = data[0]["code"];
    });
  },
  // computed: {
  //   columns(){

  //   }
  // },
  methods: {
    addOpen(obj, trueOrfalse, type) {
      this.visible1 = true
      this.modalTypeData2 = obj;
      this.modalType2 = type;
      this.pagination = {
        current: 1,
        pageSize: 10,
        total: 0,
      };
      this.columns1 = [
        {
          title: "类别",
          key: "category",
          dataIndex: "category",
        },
        {
          title: "姓名",
          key: "name",
          dataIndex: "name",
        },
        {
          title: "登录次数",
          key: "loginCount",
          dataIndex: "loginCount",
        },
        {
          title: "最后一次登录时间",
          key: "lastLoginTime",
          dataIndex: "lastLoginTime",
        },
      ];
      this.getmodallist2(obj);
    },
    getList() {
      http
        .post("/UserLoginStatistics/allUserLoginStatistics", {
          startTime: moment(this.startTime).valueOf(),
          endTime: moment(this.endTime).valueOf(),
          districtCode: this.geoCode == "500000000000" ? "" : this.geoCode,
        })
        .then((res) => {
          console.log(res, "rrrr");
          this.data = res;
        });
    },
    changePage(page, pageSize) {
      this.pagination.current = page;
      this.pagination.pageSize = pageSize;
    },
    changePage1({ current, pageSize, total }) {
      // 判断是在一级弹窗还是二级弹窗翻页
      console.log("this.modalType2====", current, this.modalType);
      this.pagination1 = {
        current: Number(current),
        pageSize: Number(pageSize),
        total: Number(total),
      };
      if (
        this.modalType == "cityLoginTitle" ||
        this.modalType == "districtLoginTitle"
      ) {
        if (
          this.modalType2 == "loginMemCount" ||
          this.modalType2 == "notLoginMemCount"
        ) {
          this.pagination = {
            current: Number(current),
            pageSize: Number(pageSize),
            total: Number(total),
          };
          this.getmodallist2({
            ...this.modalTypeData2,
            pageNum: current,
            pageSize: pageSize,
          });
        } else {
          this.getmodallist1({
            ...this.modalTypeData,
            pageNum: current,
            pageSize: pageSize,
          });
        }
      } else {
        this.getmodallist({
          ...this.modalTypeData,
          pageNum: current,
          pageSize: pageSize,
        });
      }
    },
    onSearch(e) {
      this.getList();
    },
    openmo(obj) {
      console.log(obj.type, "obj");
      this.title = `${this.startTime}~${this.endTime}${obj.tit}`;
      this.modalType = obj.type;
      this.modalTypeData = obj;
      this.systemCode = obj.systemCode;
      this.pagination1 = {
        current: 1,
        pageSize: 10,
        total: 0,
      };
      switch (obj.key) {
        case "1":
          this.columns = [
            {
              title: "类别",
              key: "category",
              dataIndex: "category",
            },
            {
              title: "登录人数",
              key: "loginMemCount",
              dataIndex: "loginMemCount",
              scopedSlots: { customRender: "loginMemCount" },
            },
            {
              title: "登录人次",
              key: "loginCount",
              dataIndex: "loginCount",
            },
            {
              title: "未登录人数",
              key: "notLoginMemCount",
              dataIndex: "notLoginMemCount",
              scopedSlots: { customRender: "notLoginMemCount" },
            },
          ];
          this.getmodallist1(obj);
          break;
        case "2":
          this.columns = [
            {
              title: "类别",
              key: "category",
              dataIndex: "category",
            },
            {
              title: "登录人数",
              key: "loginMemCount",
              dataIndex: "loginMemCount",
              scopedSlots: { customRender: "loginMemCount" },
            },
            {
              title: "登录人次",
              key: "loginCount",
              dataIndex: "loginCount",
            },
            {
              title: "未登录人数",
              key: "notLoginMemCount",
              dataIndex: "notLoginMemCount",
              scopedSlots: { customRender: "notLoginMemCount" },
            },
          ];
          this.getmodallist1(obj);
          break;
        case "3":
          this.columns = [
            {
              title: "类别",
              key: "category",
              dataIndex: "category",
            },
            {
              title: "姓名",
              key: "name",
              dataIndex: "name",
            },
            {
              title: "登录次数",
              key: "loginCount",
              dataIndex: "loginCount",
            },
            {
              title: "最后一次登录时间",
              key: "lastLoginTime",
              dataIndex: "lastLoginTime",
            },
          ];
          this.getmodallist(obj);
          break;
        case "4":
          this.columns = [
            {
              title: "类别",
              key: "category",
              dataIndex: "category",
            },
            {
              title: "姓名",
              key: "name",
              dataIndex: "name",
            },
            {
              title: "登录次数",
              key: "loginCount",
              dataIndex: "loginCount",
            },
            {
              title: "最后一次登录时间",
              key: "lastLoginTime",
              dataIndex: "lastLoginTime",
            },
          ];
          this.getmodallist(obj);
          break;
        case "5":
          this.columns = [
            {
              title: "类别",
              key: "category",
              dataIndex: "category",
            },
            {
              title: "姓名",
              key: "name",
              dataIndex: "name",
            },
            {
              title: "登录次数",
              key: "loginCount",
              dataIndex: "loginCount",
            },
            {
              title: "最后一次登录时间",
              key: "lastLoginTime",
              dataIndex: "lastLoginTime",
            },
          ];
          this.getmodallist(obj);
          break;
      }
      console.log(this.columns, "columns");
      this.visible = true;
    },
    getmodallist2(obj) {
      this.modalloading = true;
      http
        .post("/UserLoginStatistics/userCenterTitlePegging", {
          startTime: moment(this.startTime).valueOf(),
          endTime: moment(this.endTime).valueOf(),
          districtCode: this.geoCode == "500000000000" ? "" : this.geoCode,
          // pageNum: this.pageNum,
          // pageSize: this.pageSize,
          pageNum: this.pagination.current,
          pageSize: this.pagination.pageSize,
          // type: obj.type,
          type: this.modalType,
          categoryCode: obj.categoryCode,
          titleLoginType: this.modalType2, // 点的"登录人数"还是"未登录人数"
        })
        .then((res) => {
          this.modalloading = false;
          console.log(res, "rrrr");
          const { current, size, total, records } = res;
          this.list2 = records;
          // this.pageNum = Number(current);
          // this.pageSize = Number(size);
          this.pagination2 = {
            current: Number(current),
            pageSize: Number(size),
            total: Number(total),
          };
        });
    },
    getmodallist1(obj) {
      this.modalloading = true;
      http
        .post("/UserLoginStatistics/userCenterPegging", {
          startTime: moment(this.startTime).valueOf(),
          endTime: moment(this.endTime).valueOf(),
          districtCode: this.geoCode == "500000000000" ? "" : this.geoCode,
          pageNum: obj.pageNum || this.pagination1.current,
          pageSize: obj.pageSize || this.pagination1.pageSize,
          type: obj.type,
          systemCode: this.systemCode,
        })
        .then((res) => {
          this.modalloading = false;
          const { current, size, total, records } = res;
          this.list = records;
          // this.pageNum = current;
          // this.pageSize = size;
          this.pagination1 = {
            current: Number(current),
            total: Number(total),
            pageSize: Number(size),
          };
        });
    },
    getmodallist(obj) {
      this.modalloading = true;
      http
        .post("/UserLoginStatistics/allUserLoginPegging", {
          startTime: moment(this.startTime).valueOf(),
          endTime: moment(this.endTime).valueOf(),
          districtCode: this.geoCode == "500000000000" ? "" : this.geoCode,
          pageNum: obj.current || this.pagination1.current,
          pageSize: obj.pageSize || this.pagination1.pageSize,
          systemCode: this.systemCode,
          type: obj.type,
        })
        .then((res) => {
          console.log(res, "rrrr1111111111111111111");
          this.modalloading = false;
          const { current, size, total, records } = res;
          this.list = records;
          this.pageNum = current;
          this.pageSize = size;
          this.pagination1 = {
            current: Number(current),
            pageSize: Number(size),
            total: Number(total),
          };
        });
    },
    onOk() {
      this.cancel();
    },
    cancel() {
      console.log("取消");
      this.visible = false;
      this.pageNum = 1;
      this.pageSize = 10;
      this.pagination1 = {
        current: 1,
        pageSize: 10,
        total: 0,
      };
      this.list = [];
      this.modalTypeData2 = {};
      this.modalType2 = "";
    },
    onOk1() {
      this.cancel1();
    },
    cancel1() {
      console.log("取消");
      this.visible1 = false;
      this.pageNum = 1;
      this.pageSize = 10;
      this.pagination2 = {
        current: 1,
        pageSize: 10,
        total: 0,
      };
      this.list2 = [];
      this.modalTypeData2 = {};
      this.modalType2 = "";
  
    },
  },
  // watch: {
  //   startTime: {
  //     handler() {
  //       this.getList();
  //     },
  //     immediate: true,
  //     deep: true,
  //   },
  // },
};
</script>

<style lang="less" scoped>
.tables {
  width: 100%;
  border-collapse: collapse; /* 合并边框 */
  td,
  th {
    width: 100px;
    text-align: center;
    padding: 8px;
  }
}
</style>
