/**
 * ##################################
 * 配置路由
 * ##################################
 */
import routerConfigure from './configures/router'
/**
 * ##################################
 * 全局变量配置
 * ##################################
 */
import storeConfigure from './configures/store'

import { router, store } from '@zh/base-ant'

if (process.env.NODE_ENV !== 'production') {
  process.env.VUE_APP_ENABLE_MOCK == 'true' && require('./mock/index')
}

const install = (Vue) => {
  Vue.use(routerConfigure)
  Vue.use(storeConfigure)

  
}

export default {
  install
}
