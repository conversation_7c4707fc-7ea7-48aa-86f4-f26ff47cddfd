<template>
  <div class="cp-page">
    <div class="sear">
      <a-space>
        <a-input v-model="keyword" placeholder="关键字" />

        <div>
          是否当前届次：
          <a-select
            allowClear
            v-model="hasCurrTerm"
            placeholder="请输入"
            style="width: 100px"
          >
            <a-select-option value="1"> 是 </a-select-option>
            <a-select-option value="0"> 否 </a-select-option>
          </a-select>
        </div>
        <a-button type="primary" @click="() => getList()">查询</a-button>

        <a-button type="primary" @click="() => (visible = true)">新增</a-button>
      </a-space>
    </div>
    <a-table
      :rowKey="(record) => record.id"
      :dataSource="dataSource"
      :columns="columns"
      :pagination="pagination"
      @change="
        (obj) => {
          getList(obj.current, obj.pageSize);
        }
      "
    >
      <span slot="currTerm" slot-scope="text, record">
        <a v-if="record.currTerm == 1" style="color: red">是</a>
        <span v-else>否</span>
      </span>
      <span slot="startDate" slot-scope="text">
        {{ text ? dayjs(text).format("YYYY-MM-DD") : text }}
      </span>
      <span slot="endDate" slot-scope="text">
        {{ text ? dayjs(text).format("YYYY-MM-DD") : text }}
      </span>
      <span slot="action" slot-scope="text, record">
        <!-- <template v-if="record.currTerm != 1">
          <a @click="jh(record)">激活</a>
          <a-divider type="vertical" />
        </template> -->
        <template v-if="record.currTerm == 1 && record.isParentSessionMem == 0">
          <a @click="tb(record)">同步上届人员</a>
          <a-divider type="vertical" />
        </template>
        <a @click="update(record)">修改</a>
        <a-divider type="vertical" />
        <a-popconfirm
          title="是否确认删除？"
          @confirm="del(record)"
          @cancel="cancel"
        >
          <a style="color: red">删除</a>
        </a-popconfirm>
      </span>
    </a-table>
    <a-modal
      :visible="visible"
      title="届次信息"
      width="800px"
      @ok="handleOk"
      @cancel="cancel"
    >
      <a-form :form="form" :labelCol="labelCol" :wrapperCol="wrapperCol">
        <a-row>
          <a-col :span="8">
            <a-form-item
              label="年份"
              :labelCol="{ span: 9 }"
              :wrapperCol="{ span: 12 }"
            >
              <a-input
                v-decorator="[
                  'year',
                  {
                    rules: [{ required: true, message: '请输入' }],
                  },
                ]"
                placeholder="请输入"
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item
              label="届次"
              :labelCol="{ span: 9 }"
              :wrapperCol="{ span: 12 }"
            >
              <a-input
                v-decorator="[
                  'sessionNum',
                  {
                    rules: [{ required: true, message: '请输入' }],
                  },
                ]"
                placeholder="请输入"
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item
              label="是否当前届次"
              :labelCol="{ span: 11 }"
              :wrapperCol="{ span: 10 }"
            >
              <a-select
                :disabled="formState.id"
                v-decorator="[
                  'currTerm',
                  {
                    rules: [{ required: true, message: '请输入' }],
                  },
                ]"
              >
                <a-select-option :value="1"> 是 </a-select-option>
                <a-select-option :value="0"> 否 </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item
              label="会议序号"
              :labelCol="{ span: 9 }"
              :wrapperCol="{ span: 12 }"
            >
              <a-input
                v-decorator="[
                  'timeNum',
                  {
                    rules: [{ required: true, message: '请输入' }],
                  },
                ]"
                placeholder="请输入"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="12">
            <a-form-item label="开始时间">
              <a-date-picker
                v-decorator="[
                  'startDate',
                  {
                    rules: [{ required: true, message: '请输入' }],
                  },
                ]"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="结束时间">
              <a-date-picker
                v-decorator="[
                  'endDate',
                  {
                    rules: [{ required: true, message: '请输入' }],
                  },
                ]"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-form-item
          label="区划"
          :labelCol="{ span: 3 }"
          :wrapperCol="{ span: 20 }"
        >
          <a-select
            allowClear
            v-decorator="[
              'geocode',
              {
                rules: [{ required: true, message: '请输入' }],
              },
            ]"
            placeholder="请输入"
            style="width: 100%"
          >
            <a-select-option
              v-for="(item, index) in treeData"
              :key="index"
              :value="item.code"
              :dataRef="item"
            >
              {{ item.name }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item
          label="届次名称"
          :labelCol="{ span: 3 }"
          :wrapperCol="{ span: 20 }"
        >
          <a-input
            v-decorator="[
              'sortName',
              {
                rules: [{ required: true, message: '请输入' }],
              },
            ]"
            placeholder="请输入"
          />
        </a-form-item>
        <a-form-item
          label="届次全称"
          :labelCol="{ span: 3 }"
          :wrapperCol="{ span: 20 }"
        >
          <a-input
            v-decorator="[
              'name',
              {
                rules: [{ required: true, message: '请输入' }],
              },
            ]"
            placeholder="请输入"
          />
        </a-form-item>
      </a-form>
    </a-modal>
    <a-modal
      :confirmLoading="confirmLoadingCheckTerm"
      :visible="visibleCheckTerm"
      title="提示"
      width="400px"
      @ok="handleOkCheckTerm"
      @cancel="cancelCheckTerm"
    >
      <div>
        当前最新届次为：<span style="font-weight: bold">{{
          saveData.oldjcName
        }}</span
        >，是否替换当前最新届次为：<span style="font-weight: bold">{{
          saveData.newjcName
        }}</span>
      </div>
    </a-modal>
    <a-modal
      destroyOnClose
      :confirmLoading="confirmLoadingTb"
      :visible="visibleTb"
      title="同步上届人员"
      width="1400px"
      @ok="handleOkTb"
      @cancel="cancelTb"
    >
      <a-table
        :dataSource="tbList"
        :columns="columnsTb"
        :pagination="false"
        :rowKey="(record) => record.id"
        :row-selection="rowSelectionTb"
      >
        <span slot="currTerm" slot-scope="text, record">
          <a v-if="record.currTerm == 1" style="color: red">是</a>
          <span v-else>否</span>
        </span>
        <span slot="startDate" slot-scope="text">
          {{ text ? dayjs(text).format("YYYY-MM-DD") : text }}
        </span>
        <span slot="endDate" slot-scope="text">
          {{ text ? dayjs(text).format("YYYY-MM-DD") : text }}
        </span>
      </a-table>
    </a-modal>
  </div>
</template>

<script>
import { auth as http } from "@zh/common-utils/http";
import dayjs from "dayjs";

export default {
  data() {
    return {
      dayjs,
      hasCurrTerm: "1",
      dataSource: [],
      columns: [
        {
          title: "年份",
          dataIndex: "year",
          key: "year",
          width: 70,
        },
        {
          title: "届次",
          dataIndex: "sessionNum",
          key: "sessionNum",
          width: 70,
        },
        {
          title: "会议序号",
          dataIndex: "timeNum",
          key: "timeNum",
          width: 120,
        },
        {
          title: "届次全称",
          dataIndex: "name",
          key: "name",
          width: 400,
          ellipsis: { showTitle: true },
        },
        {
          title: "届次简称",
          dataIndex: "sortName",
          key: "sortName",
          width: 130,
          ellipsis: { showTitle: true },
        },
        {
          title: "是否当前届次",
          dataIndex: "currTerm",
          scopedSlots: { customRender: "currTerm" },
          width: 120,
        },
        {
          title: "开始时间",
          dataIndex: "startDate",
          scopedSlots: { customRender: "startDate" },
          width: 120,
        },
        {
          title: "结束时间",
          dataIndex: "endDate",
          scopedSlots: { customRender: "endDate" },
          width: 120,
        },
        {
          title: "操作",
          key: "action",
          scopedSlots: { customRender: "action" },
          width: 300,
        },
      ],
      form: this.$form.createForm(this, { name: "dynamic_rule" }),
      visible: false,
      labelCol: { span: 6 },
      wrapperCol: { span: 16 },
      formState: {},
      keyword: undefined,
      pageNum: 1,
      pageSize: 10,
      pagination: {},
      treeData: [],
      visibleCheckTerm: false,
      confirmLoadingCheckTerm: false,
      saveData: {
        url: "/cppccSession/save",
        newjcName: "",
        oldjcName: "",
      },
      visibleTb: false,
      confirmLoadingTb: false,
      tbList: [],
      columnsTb: [
        {
          title: "年份",
          dataIndex: "year",
          key: "year",
          width: 70,
        },
        {
          title: "届次",
          dataIndex: "sessionNum",
          key: "sessionNum",
          width: 70,
        },
        {
          title: "会议序号",
          dataIndex: "timeNum",
          key: "timeNum",
          width: 120,
        },
        {
          title: "届次全称",
          dataIndex: "name",
          key: "name",
          width: 400,
          ellipsis: { showTitle: true },
        },
        {
          title: "届次简称",
          dataIndex: "sortName",
          key: "sortName",
          width: 130,
          ellipsis: { showTitle: true },
        },
        {
          title: "是否当前届次",
          dataIndex: "currTerm",
          scopedSlots: { customRender: "currTerm" },
          width: 120,
        },
        {
          title: "开始时间",
          dataIndex: "startDate",
          scopedSlots: { customRender: "startDate" },
          width: 120,
        },
        {
          title: "结束时间",
          dataIndex: "endDate",
          scopedSlots: { customRender: "endDate" },
          width: 120,
        },
        // {
        //   title: "操作",
        //   key: "action",
        //   scopedSlots: { customRender: "action" },
        //   width: 300,
        // },
      ],
      selectedRowKeysTb: [],
      rowSelectionTb: {
        // 单选
        type: "radio",
        onChange: (selectedRowKeys, selectedRows) => {
          console.log(
            `selectedRowKeys: ${selectedRowKeys}`,
            "selectedRows: ",
            selectedRows
          );
          this.selectedRowKeysTb = selectedRowKeys;
        },
      },
      recordTb: {},
    };
  },
  mounted() {
    this.getQx();
    this.getList();
  },
  activated() {
    this.getList();
  },
  methods: {
    getList(pageNum = 1, pageSize = 10) {
      http
        .post("/cppccSession/list", {
          keyword: this.keyword,
          hasCurrTerm: this.hasCurrTerm,
          pageNum: pageNum || this.pageNum,
          pageSize: pageSize || this.pageSize,
        })
        .then((res) => {
          const { currPage, pageSize, list, totalCount } = res;
          this.dataSource = list;
          this.pageNum = currPage;
          this.pageSize = pageSize;
          this.pagination = {
            current: currPage,
            total: totalCount,
          };
        });
    },
    getQx() {
      http.get("/cppccSession/getUnitInfo?needQx=2").then((res) => {
        this.treeData = res;
      });
    },
    jh(record) {
      http.get(`/cppccSession/setCurrTerm?id=${record["id"]}`).then((res) => {
        this.getList();
      });
    },
    del(record) {
      http.get(`/cppccSession/delete?id=${record["id"]}`).then((res) => {
        this.getList();
      });
    },
    cancel() {
      this.visible = false;
      this.formState = {};
      this.form.resetFields();
    },
    update(record) {
      http.get(`/cppccSession/find?id=${record["id"]}`).then((res) => {
        this.visible = true;
        this.formState = res;
        setTimeout(() => {
          this.form.setFieldsValue({ ...res });
        }, 100);
      });
    },
    handleOk() {
      this.form.validateFields((err, value) => {
        if (!err) {
          console.log("Received values of form: ", value);
          this.saveData.newjcName = value.name;
          console.info("success");
          ["startDate", "endDate"].forEach((key) => {
            if (value[key]) {
              value[key] = dayjs(value[key]).valueOf();
            }
          });
          this.saveData.url = "/cppccSession/save";
          if (this.formState["id"]) {
            this.saveData.url = "/cppccSession/update";
          }
          if (!this.formState["id"]) {
            // 检查当前届次是否已存在
            http
              .get(`/cppccSession/checkCurrTerm?geocode=${value["geocode"]}`)
              .then((res) => {
                if (res.id) {
                  this.saveData.oldjcName = res.name;
                  this.visibleCheckTerm = true;
                }
              });
          }
          this.handleOkCheckTerm();
          // http
          //   .post(url, {
          //     ...this.formState,
          //     ...value,
          //   })
          //   .then((res) => {
          //     this.getList();
          //     this.cancel();
          //   });
        }
      });
      // this.cancel();
    },
    handleOkCheckTerm() {
      this.confirmLoadingCheckTerm = true;
      http
        .post(this.saveData.url, {
          ...this.formState,
          ...this.form.getFieldsValue(),
        })
        .then((res) => {
          this.confirmLoadingCheckTerm = false;
          this.getList();
          this.cancel();
          this.cancelCheckTerm();
        });
      this.confirmLoadingCheckTerm = false;
    },
    cancelCheckTerm() {
      this.visibleCheckTerm = false;
    },
    tb(record) {
      this.recordTb = record;
      http
        .get(`/cppccSession/getSessionListByGeoCode?id=${record["id"]}`)
        .then((res) => {
          this.tbList = res;
          this.visibleTb = true;
        });
    },
    handleOkTb() {
      console.log("selectedRowKeysTb===", this.selectedRowKeysTb);
      if (this.selectedRowKeysTb.length > 0) {
        this.confirmLoadingTb = true;
        http
          .get(
            `/cppccSession/extendPreviousMem?newSessionId=${this.recordTb["id"]}&oldSessionId=${this.selectedRowKeysTb[0]}`
          )
          .then((res) => {
            this.confirmLoadingTb = false;
            this.cancelTb();
            this.getList();
          });
        this.confirmLoadingTb = false;
      } else {
        this.$notify.error("请先选择届次");
      }
    },
    cancelTb() {
      this.visibleTb = false;
      this.selectedRowKeysTb = [];
      this.recordTb = {};
    },
  },
};
</script>

<style lang="less" scoped>
.sear {
  margin-bottom: 18px;
}
</style>
