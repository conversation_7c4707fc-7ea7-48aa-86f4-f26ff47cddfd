<template>
  <div class="cp-page">
    <div class="sear">
      <div>
        <span>约稿主题：</span>
        <a-space>
          <a-input v-model="search.ygTheme" placeholder="请输入" style="width: 300px" />
        </a-space>
      </div>

      <div>
        <span>约稿时间：</span>
        <a-space>
          <a-date-picker v-model="search.startTime" style="width: 120px" />
          <span>至</span>
          <a-date-picker v-model="search.endTime" style="width: 120px" />
        </a-space>
      </div>
      <div>
        <a-space>
          <a-button type="primary" @click="getList(1)">查询</a-button>
          <a-button
            type="primary"
            @click="
              () => {
                search = {};
                getList(1);
              }
            "
            >重置</a-button
          >
        </a-space>
      </div>
    </div>
    <a-table
      :dataSource="dataSource"
      :columns="columns"
      :pagination="pagination"
      @change="
        (obj) => {
          getList(obj.current, obj.pageSize);
        }
      "
    >
      <a slot="ygTheme" slot-scope="text, record" @click="addOpen2(record)" :title="text">
        {{ record ? record["ygTheme"] : "" }}
      </a>
      <span slot="ygTime" slot-scope="text">
        {{ text ? dayjs(text).format("YYYY-MM-DD") : text }}
      </span>
      <span slot="publishUnit"> 市政协 </span>
      <span slot="action" slot-scope="text, record">
        <a v-if="record['status'] == 0" @click="addOpen(record)">文稿上报</a>
      </span></a-table
    >
    <AddEdit ref="addEditRef" @callBack="getList" />
    <AddEditUpadte ref="addEditRefUpdate" :isDetail="true" />
  </div>
</template>

<script>
import { auth as http } from "@zh/common-utils/http";
import dayjs from "dayjs";
import AddEdit from "./components/addEdit.vue";
import AddEditUpadte from "../update/components/addEdit.vue";
export default {
  components: { AddEdit, AddEditUpadte },
  data() {
    return {
      dayjs: dayjs,
      dataSource: [],
      columns: [
        {
          title: "约稿主题",
          dataIndex: "ygTheme",
          width: 400,
          scopedSlots: { customRender: "ygTheme" },
        },
        {
          title: "约稿时间",
          dataIndex: "ygTime",
          key: "ygTime",
          scopedSlots: { customRender: "ygTime" },
        },
        {
          title: "发布单位",
          dataIndex: "publishUnit",
          key: "publishUnit",
          scopedSlots: { customRender: "publishUnit" },
        },
        {
          title: "状态",
          dataIndex: "statusName",
          key: "statusName",
        },
        {
          title: "操作",
          key: "action",
          width: 120,
          scopedSlots: { customRender: "action" },
        },
      ],
      search: {
        ygTheme: undefined,
        startTime: undefined,
        endTime: undefined,
      },
      pageNum: 1,
      pageSize: 10,
      labelCol: { span: 3 },
      wrapperCol: { span: 20 },
      pagination: {},
    };
  },
  // mounted() {
  //   this.getList();
  // },
  activated() {
    this.getList();
    this.yetRead();
  },
  methods: {
    getList(pageNum = 1, pageSize = 10) {
      let temp = { ...this.search };
      Object.keys(temp).forEach((key) => {
        if (key.includes("Time") && temp[key]) {
          temp[key] = dayjs(temp[key]).valueOf();
        }
      });
      http
        .post("/ygNotice/list", {
          ...temp,
          type: 1,
          pageNum: pageNum || this.pageNum,
          pageSize: pageSize || this.pageSize,
        })
        .then((res) => {
          const { currPage, pageSize, list, totalCount } = res;
          this.dataSource = list;
          this.pageNum = currPage;
          this.pageSize = pageSize;
          this.pagination = {
            current: currPage,
            total: totalCount,
          };
        });
    },
    yetRead() {
      const { taskUuid, urlType = "" } = this.$route.query;
      const user = this.$store.getLoginUser();
      let bool = false;
      if (Array.isArray(urlType)) {
        let find = urlType.find((key) => key == "todo");
        if (find) {
          bool = true;
        }
      }
      if (urlType == "todo" || taskUuid) {
        bool = true;
      }
      if (bool) {
        http
          .post("/zh/msg/finishTodo", {
            userId: user?.openId,
            taskUuid: taskUuid,
          })
          .then((res) => {
            const { currPage, pageSize, list, totalCount } = res;
            this.dataSource = list;
            this.pageNum = currPage;
            this.pageSize = pageSize;
            this.pagination = {
              current: currPage,
              total: totalCount,
            };
          });
      }
    },
    addOpen(record) {
      this.$refs.addEditRef.open(record["id"]);
    },
    addOpen2(record) {
      this.$refs.addEditRefUpdate.open(record["id"]);
    },
  },
};
</script>

<style scoped lang="less">
.sear {
  & > div {
    display: inline-block;
    margin-right: 36px;
    margin-bottom: 18px;
  }
}
</style>
