<!DOCTYPE html>
<html>

<head>
  <meta charset='utf-8'>
  <meta http-equiv='X-UA-Compatible' content='IE=edge'>
  <title>登录中...</title>
  <script src="./index.js"></script>
</head>

<body>
  <!-- 单点登录页面 -->
  <!-- http://***************:8089/ssologin.html?app_code=dee9976ecd2cdea4b338&currentAppList=dee9976ecd2cdea4b338&client=zwdd&zwdd_id=295034&timestamp=1699411948762&itemType=2&redirect=http%253A%252F%252F23.210.52.41%253A80%252Fssologin.html%252Fsendread%252Finform%252Flist
       http://localhost:6080/ssologin.html?app_code=1724361174697779201&ssoLoginUrl=%2Fssologin.html&zwdd_id=10086&timestamp=1699411948762&itemType=2&redirect=%2Fhome -->
  <!-- <div class="loading-screen" id="loadging">
    <div class="loading">
      <span></span>
      <span></span>
      <span></span>
      <span></span>
    </div>
  </div> -->
  <div id="app">
    <div id="first-loading-wrp" class="first-loading-wrp">
      <div class="loading-wrp" style="flex-direction: column">
        <div>
          <img src="/logo.png" style="width: 90px; height: 90px" />
        </div>
        <div style="margin: 20px">
          <span class="dot dot-spin">
            <i></i>
            <i></i>
            <i></i>
            <i></i>
          </span>
        </div>
        <div style="font-size: 24px; color: #333">重庆数字政协</div>

      </div>
    </div>
  </div>



  <div id="notifyNoPermissions" class="notify-no-permissions" style="display: none;">
    <div style=" display: flex; justify-content: center; ">
      <div style="max-width: 50%;text-align: center;">
        尊敬的用户，根据应用权限设置，您没有本应用访问权限。
      </div>
    </div>
  </div>
  <div id="showUnitsModal" class="showUnitsModal" style="display: none;">
    <div style="justify-content: center;vertical-align: middle; ">
      <div style="margin-bottom: 20px;">
        <p>尊敬的<span id="userName"></span>用户，您好！</p>
        <p></p>
        <p>请选择要登录的机构：</p>
      </div>
      <div id="radio-group" class="radio-group"></div>
    </div>
  </div>
</body>

</html>
<style>
  .showUnitsModal {
      display: none;
      position: fixed;
      top: 50%;
      left: 50%;
      width: 400px;
      height: 180px;
      transform: translate(-50%, -50%);
      padding: 20px;
      background-color: #fff;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
      border-radius: 8px;
    }

    /* 单选按钮样式，改变颜色为蓝色 */
    .radio-group {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      vertical-align: middle;
    }

    .radio-group label {
      margin-bottom: 10px;
    }

    .radio-group input[type="radio"] {
      appearance: none;
      -webkit-appearance: none;
      -moz-appearance: none;
      border: 2px solid #1677ff;
      border-radius: 50%;
      width: 16px;
      height: 16px;
      margin-right: 5px;
      position: relative;
      top: 3px;
      outline: none;
    }

    .radio-group input[type="radio"]:checked {
      background-color: #1677ff;
      border: 1px solid #1677ff;
      border-radius: 50%;
    }

    .radio-group label {
      display: inline-block;
      cursor: pointer;
      color: #333;
    }
</style>

<style>
  
  .first-loading-wrp {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    /* min-height: 420px; */
    height: 98vh
  }

  .first-loading-wrp>h1 {
    font-size: 128px
  }

  .first-loading-wrp .loading-wrp {
    padding: 98px;
    display: flex;
    justify-content: center;
    align-items: center
  }

  .dot {
    animation: antRotate 1.2s infinite linear;
    transform: rotate(45deg);
    position: relative;
    display: inline-block;
    font-size: 32px;
    width: 32px;
    height: 32px;
    box-sizing: border-box
  }

  .dot i {
    width: 14px;
    height: 14px;
    position: absolute;
    display: block;
    background-color: #1890ff;
    border-radius: 100%;
    transform: scale(.75);
    transform-origin: 50% 50%;
    opacity: .3;
    animation: antSpinMove 1s infinite linear alternate
  }

  .dot i:nth-child(1) {
    top: 0;
    left: 0
  }

  .dot i:nth-child(2) {
    top: 0;
    right: 0;
    -webkit-animation-delay: .4s;
    animation-delay: .4s
  }

  .dot i:nth-child(3) {
    right: 0;
    bottom: 0;
    -webkit-animation-delay: .8s;
    animation-delay: .8s
  }

  .dot i:nth-child(4) {
    bottom: 0;
    left: 0;
    -webkit-animation-delay: 1.2s;
    animation-delay: 1.2s
  }

  @keyframes antRotate {
    to {
      -webkit-transform: rotate(405deg);
      transform: rotate(405deg)
    }
  }

  @-webkit-keyframes antRotate {
    to {
      -webkit-transform: rotate(405deg);
      transform: rotate(405deg)
    }
  }

  @keyframes antSpinMove {
    to {
      opacity: 1
    }
  }

  @-webkit-keyframes antSpinMove {
    to {
      opacity: 1
    }
  }
</style>
<style>
  .button {
    color: #fff;
    background-color: #1677ff;
    border: none;
    font-size: 20px;
    padding: 10px;
    width: 120px;
    border-radius: 10px;
  }

  .notify-no-permissions {
    width: 100%;
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 30px;
    align-items: center;
    flex-direction: row;
    flex-wrap: wrap;
    align-content: center;
  }

  .notify-back {
    margin-top: 30px;
    display: flex;
    justify-content: center;
  }

  * {
    margin: 0;
    padding: 0;
  }

  .loading-screen {
    width: 100%;
    height: 100vh;
    background: white;
    position: fixed;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .loading {
    width: 80px;
    display: flex;
    flex-wrap: wrap;
    animation: rotate 3s linear infinite;
  }

  @keyframes rotate {
    to {
      transform: rotate(360deg);
    }
  }

  .loading span {
    width: 32px;
    height: 32px;
    background: red;
    margin: 4px;
    animation: scale 1.5s linear infinite;
  }

  @keyframes scale {
    50% {
      transform: scale(1.2);
    }
  }

  .loading span:nth-child(1) {
    border-radius: 50% 50% 0 50%;
    background: #e77f67;
    transform-origin: bottom right;
  }

  .loading span:nth-child(2) {
    border-radius: 50% 50% 50% 0;
    background: #778beb;
    transform-origin: bottom left;
    animation-delay: 0.5s;
  }

  .loading span:nth-child(3) {
    border-radius: 50% 0 50% 50%;
    background: #f8a5c2;
    transform-origin: top right;
    animation-delay: 1.5s;
  }

  .loading span:nth-child(4) {
    border-radius: 0 50% 50% 50%;
    background: #f5cd79;
    transform-origin: top left;
    animation: 1s;
  }
</style>
<script>
  
  

</script>

<script>

 

  // function getQueryString(name) {
  //   let reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
  //   let r = window.location.search.substr(1).match(reg);
  //   if (r != null) {
  //     return unescape(r[2]);
  //   };
  //   return null;
  // }

  let urlParam = window.location.search + window.location.hash;
  function getQueryString(name) {
    let reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
    let r = urlParam.substr(1).match(reg);
    if (r != null) {
      return unescape(r[2]);
    };
    return null;
  }
  let auth_code = getQueryString('auth_code')
  const app_code = getQueryString('app_code')
  const zwdd_code = getQueryString('zwdd_code')
  const zwdd_id = getQueryString('zwdd_id')
  const client = getQueryString('client')
  const ssoLoginUrl = getQueryString('ssoLoginUrl')
  const token = getQueryString('token')
  const redirect = getQueryString('redirect')
  const targetUrl = getQueryString('targetUrl')
  const currentAppList = getQueryString('currentAppList')|| ''
  const network = getQueryString('network')
  // 此处修改接口IP地址 互联网（http://***************:8096/api） 政务外网（http://***********:8090/api）
  let ip = 'http://127.0.0.1:18080/moli';
  let type = '3'; // 1 pc 登录；3 移动端登录
  function setToken(token, validTime) {
    const acctoken = JSON.stringify({
      expire: validTime,
      value: token
    })
    localStorage.setItem('access-token', acctoken)
  }

  function getApplicationURLByCode() {
    return get(ip + '/app/sysApplication/appCode/' + app_code, true)
  }

  function notifyNoPermissions() {
    document.getElementById("first-loading-wrp").style.display = 'none'
    document.getElementById("notifyNoPermissions").style.display = 'flex'
  }

  function get(url, needtoken = false) {
    function ajaxFunction() {
      var xmlHttp;
      try { // Firefox, Opera 8.0+, Safari
        xmlHttp = new XMLHttpRequest();
      } catch (e) {
        try { // Internet Explorer
          xmlHttp = new ActiveXObject("Msxml2.XMLHTTP");
        } catch (e) {
          try {
            xmlHttp = new ActiveXObject("Microsoft.XMLHTTP");
          } catch (e) { }
        }
      }
      return xmlHttp;
    }
    const xhr = ajaxFunction();
    const promise = new Promise((resolve, reject) => {
      xhr.onreadystatechange = function () {
        if (xhr.readyState == 4) {
          if (xhr.status == 200 || xhr.status == 304) {
            try {
              resolve(JSON.parse(xhr.response))
            } catch (e) {
              notifyNoPermissions()
            }
          } else {
            notifyNoPermissions()
          }
        }
      }
    })
    xhr.open("GET", url, true);
    if (needtoken) {
      xhr.setRequestHeader('token', JSON.parse(localStorage.getItem('access-token')).value)
    }
    xhr.send(null)
    return promise
  }

  function loginSuccess(resp) {
    setToken(resp.token, resp.validTime);
    if (client || ssoLoginUrl) {
      let loginUrl = decodeURIComponent(ssoLoginUrl);
      // window.location.href = loginUrl + (loginUrl.indexOf("?") > -1 ? "&token=" : "?token=") + resp.token +
      //   "&redirect=" + decodeURIComponent(redirect) + "&currentAppList=" + currentAppList + "&app_code=" + app_code + "&network=" + network;
      let url = loginUrl + (loginUrl.indexOf("?") > -1 ? "&token=" : "?token=") + resp.token +
        "&redirect=" + decodeURIComponent(redirect) + "&currentAppList=" + currentAppList + "&app_code=" + app_code + "&network=" + network;  
      openModal(url);
    } else if (app_code) {
      getApplicationURLByCode(app_code).then((res) => {
        if (res && res.data) {
          let newRedirect = redirect ? redirect : res.data.targetUrl;
          if (res.data.ssoLoginUrl) {
            window.location.href = res.data.ssoLoginUrl + (res.data.ssoLoginUrl.indexOf("?") > -1 ? "&token=" :
              "?token=") +
              resp.token + "&targetUrl=" + decodeURIComponent(newRedirect) + "&currentAppList=" + currentAppList + "&app_code=" + app_code + "&network=" + network;
          } else {
            window.location.href = decodeURIComponent(targetUrl) ? decodeURIComponent(newRedirect) : '/home'
          }
        } else {
          notifyNoPermissions()
        }
      });
    } else {
      if (resp.token) {
        // get(ip + `/custom/sso/login?loginCode=${resp.token}&currentAppList=${currentAppList}`).then((response) => {
        //   if (response.code != 0) {
        //     notifyNoPermissions()
        //     return;
        //   }
        //   debugger
        // });
        // 缓存 token
        // setToken(resp.token, resp.validTime);
        //window.location.href = decodeURIComponent(targetUrl) ? decodeURIComponent(redirect) : '/home'
        let url = redirect ? decodeURIComponent(redirect) : targetUrl ? decodeURIComponent(targetUrl) : '/home'
        openModal(url);
      }
    }
  }

  function zwloginById() {
    get(ip + "/zwdd/loginById?zwddId=" + zwdd_id + "&currentAppList=" + currentAppList + "&type="+type,).then((response) => {
      if (response.code != 0) {
        notifyNoPermissions()
        return;
      }
      this.loginSuccess(response.data);
    })
  }

  function loginByAuthCode() {
    get(ip + "/zwdd/zwddLogin?auth_code=" + auth_code + "&currentAppList=" + currentAppList + "&type="+type,).then((response) => {
      if (response.code != 0) {
        notifyNoPermissions()
        return;
      }
      this.loginSuccess(response.data);
    })
  }

  function redirectLogin() {
    // window.location.href = '/login'
  }
  if (token) {
    // loginSuccess({
    //   token: token,
    //   validTime: 3600
    // })
    get(ip + `/custom/sso/login?loginCode=${token}&currentAppList=${currentAppList || app_code}&type=${type}`).then((response) => {
      if (response.code != 0) {
        notifyNoPermissions()
        return;
      }
      // 缓存 token
      setToken(response.data.token, response.data.validTime);
      // window.location.href = redirect ? decodeURIComponent(redirect) : targetUrl ? decodeURIComponent(targetUrl) : '/home'
      let url = redirect ? decodeURIComponent(redirect) : targetUrl ? decodeURIComponent(targetUrl) : '/home'
      openModal(url);
    });
  } else if (zwdd_id) {
    zwloginById();
  } else if (auth_code) {
    loginByAuthCode();
  } else if (app_code) {
    dd.getAuthCode({
      corpId: "",
    }).then((res) => {
      auth_code = res.code;
      loginByAuthCode();
    })
  } else {
    redirectLogin();
  }


  // 打开弹框
  function openModal(url) {
    getUserInfo().then(res=>{
      if (res && res.data) {
        if(res.data.depts && res.data.depts.length > 1){
          document.getElementById('showUnitsModal').style.display = 'block';
          document.getElementById('first-loading-wrp').style.background = '#ddd';
          document.getElementById('userName').innerText = res.data.userName;
          document.getElementById('radio-group').innerHTML = res.data.depts.map(item => `<label><input type="radio" onClick="openUrl('${item.unitUserId}','${url}')" name="unit" value="${item.unitUserId}">${item.unitName}-${item.deptName}</label>`).join('');  
        }else if(res.data.depts && res.data.depts.length == 1){
          window.location.href = url
        }else{
          notifyNoPermissions()
        }
      }
    })
  }

  // 关闭弹框
  function closeModal() {
    document.getElementById('showUnitsModal').style.display = 'none';
    document.getElementById('first-loading-wrp').style.background = '#FFF';
  }

  function getUserInfo(){
    return get(ip + '/sys/sso/getUserSimleInfo?checkToken=true', true)
  }
  function changeLoginUnit (unitUserId) {
    return get(ip + `/sys/sso/changeUnit/${unitUserId}`,true)
  }

  function openUrl(unitUserId,url){
    closeModal()
    changeLoginUnit(unitUserId).then(response => {
      if (response.code != 0) {
        notifyNoPermissions()
        return;
      }
      // 缓存 token
      setToken(response.data);
      window.location.href = url
    })
  }


</script>