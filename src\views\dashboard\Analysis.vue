<template>
  <div>
    Analysis
    <div>
      <a-button @click="$router.push('/app-user/index')">子应用-首页</a-button>
      <a-button @click="$router.push('/app-user/dashboard')">子应用-工作台</a-button>
    </div>
  </div>
</template>

<script>

import 'ant-design-vue/es/tree-select/style/index.less'

export default {
  data () {
    return {
      selectSetting6: {
        mode: 'multiple',
        url: 'system/SysDict/queryList',
        param: { code: 'HJCD' }, // code表的workid
        optionValue: 'dictKey',
        optionKey: 'dictValue',
        method: 'post'
      }
    }
  }
}
</script>

<style lang="less" scoped>
// @import url("~ant-design-vue/es/tree-select/style/index.less");
</style>
