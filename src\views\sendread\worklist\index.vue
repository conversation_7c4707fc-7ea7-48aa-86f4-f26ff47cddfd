<template>
  <div class="cp-page">
    <div class="sear">
      <div>
        <span>名称：</span>
        <a-space>
          <a-input v-model="search.keyword" placeholder="请输入" style="width: 300px" />
        </a-space>
      </div>

      <div>
        <span>是否启用：</span>
        <a-space>
          <a-select v-model="search.isEnable" allowClear style="width: 120px" placeholder="请选择">
            <a-select-option :value="1"> 启用 </a-select-option>
            <a-select-option :value="0"> 停用 </a-select-option>
          </a-select>
        </a-space>
      </div>
      <div>
        <a-space>
          <a-button type="primary" @click="getList(1)">查询</a-button>
          <a-button type="primary" @click="addOpen({})">新增</a-button>
          <a-button
            type="primary"
            @click="
              () => {
                search = {};
                getList(1);
              }
            "
            >重置</a-button
          >
        </a-space>
      </div>
    </div>
    <a-table
      :dataSource="dataSource"
      :columns="columns"
      :pagination="pagination"
      @change="
        (obj) => {
          getList(obj.current, obj.pageSize);
        }
      "
    >
      <span slot="isEnable" slot-scope="text" :title="text">
        {{ text == "1" ? "是" : "否" }}
      </span>

      <span slot="action" slot-scope="text, record">
        <a @click="bind(record)">绑定</a>
        <a-divider type="vertical" />
        <a @click="addOpen(record)">修改</a>
        <a-divider type="vertical" />
        <a-popconfirm title="是否确认删除？" @confirm="del(record)">
          <a style="color: red">删除</a>
        </a-popconfirm>
      </span></a-table
    >
    <AddEdit ref="addEditRef" @callBack="getList" />

    <a-modal :visible="visible" :destroyOnClose="true" title="工作组人员信息" width="1000px" @cancel="cancel" @ok="ubindsure" :bodyStyle="{ overflow: 'auto' }">
      <!-- <div slot="footer">
        <a-button @click="cancel"> 取消 </a-button>
        <a-popconfirm title="是否确认解除绑定？" @confirm="ubindsure"> <a-button type="primary"> 确认 </a-button> </a-popconfirm>
      </div> -->
      <div>
        <a-form :form="form" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-row>
            <a-col :span="12">
              <a-form-item label="工作组名称">
                <a-input
                  v-decorator="[
                    'name',
                    {
                      rules: [{ required: false, message: '请输入' }],
                    },
                  ]"
                  placeholder="请输入"
                />
              </a-form-item>
            </a-col>
          </a-row>

          <a-form-item label="工作组人员" :labelCol="{ span: 3 }" :wrapperCol="{ span: 19 }">
            <a-textarea v-model="userNames" @click="() => this.$refs.userRef.openDialog()" :autosize="{ minRows: 6, maxRows: 24 }"> </a-textarea>
            <z-select-user style="display: none" v-model="userIds" placeholder="请输入" :limit="9999" @change="userChange" ref="userRef" />
          </a-form-item>
        </a-form>
      </div>
    </a-modal>
  </div>
</template>

<script>
import { auth as http } from "@zh/common-utils/http";
import dayjs from "dayjs";
import AddEdit from "./components/addEdit.vue";
export default {
  components: { AddEdit },
  data() {
    return {
      dayjs: dayjs,
      dataSource: [],
      columns: [
        {
          title: "名称",
          dataIndex: "name",
          width: 200,
        },
        {
          title: "说明",
          dataIndex: "remark",
          width: 400,
        },
        {
          title: "是否启用",
          dataIndex: "isEnable",
          scopedSlots: { customRender: "isEnable" },
        },
        {
          title: "排序",
          dataIndex: "sort",
          key: "sort",
        },
        {
          title: "操作",
          key: "action",
          width: 160,
          scopedSlots: { customRender: "action" },
        },
      ],
      search: {
        keyword: undefined,
        isEnable: undefined,
      },
      pageNum: 1,
      pageSize: 10,
      pagination: {},
      visible: false,
      labelCol: { span: 6 },
      wrapperCol: { span: 18 },
      form: this.$form.createForm(this, { name: "work_list" }),
      userList: [],
      userIds: [],
      userNames: "",
      bindObj: {},
    };
  },
  // mounted() {
  //   this.getList();
  // },
  activated() {
    this.getList();
  },
  methods: {
    getList(pageNum = 1, pageSize = 10) {
      let temp = { ...this.search };
      Object.keys(temp).forEach((key) => {
        if (key.includes("Time") && temp[key]) {
          temp[key] = dayjs(temp[key]).valueOf();
        }
      });
      http
        .post("/personnelGroup/list", {
          ...temp,
          type: 1,
          bizType: this.$route.path.startsWith("/sendread") ? "1" : "2",
          pageNum: pageNum || this.pageNum,
          pageSize: pageSize || this.pageSize,
        })
        .then((res) => {
          const { currPage, pageSize, list, totalCount } = res;
          this.dataSource = list;
          this.pageNum = currPage;
          this.pageSize = pageSize;
          this.pagination = {
            current: currPage,
            total: totalCount,
          };
        });
    },
    del(record) {
      http
        .get(`/personnelGroup/delete?id=${record["id"]}`)
        .then((res) => {
          this.getList();
        });
    },
    bind(record) {
      this.visible = true;
      this.bindObj = record;
      setTimeout(() => {
        this.form.setFieldsValue({
          ...this.bindObj,
        });
      });
      http.get(`/personnelGroup/findGroup?groupId=${record.id}`).then((res) => {
        this.userList = res;
        this.userIds = res.map((obj) => obj["userId"]);
        this.userNames = res.map((obj) => obj["userName"]).join("、");
      });
    },
    ubindsure() {
      this.cancel();
      http
        .post(`/personnelGroup/saveGroup`, {
          ...this.bindObj,
          groupDetailList: this.userList.map((obj) => {
            return {
              groupId: this.bindObj.id,
              userId: obj.id,
              userName: obj.name,
            };
          }),
        })
        .then((res) => {
          this.getList();
        });
    },
    cancel() {
      this.visible = false;
      this.userNames = "";
      this.userIds = [];
    },
    userChange(e, data) {
      this.userList = data;
      this.userNames = data.map((obj) => obj["name"]).join("、");
    },
    addOpen(record) {
      this.$refs.addEditRef.open(record["id"]);
    },
  },
};
</script>

<style scoped lang="less">
.sear {
  & > div {
    display: inline-block;
    margin-right: 36px;
    margin-bottom: 18px;
  }
}
</style>
