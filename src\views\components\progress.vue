<template>
  <div class="gress">
    <div class="pro" :style="{ ...style }">
      <p>文件上传中...</p>
      <Progress :percent="percent">文件上传中</Progress>
    </div>
    <slot></slot>
  </div>
</template>

<script>
import { Progress } from "ant-design-vue";

export default {
  components: {
    Progress,
  },
  model: {
    prop: "loading",
    event: "change",
  },
  props: {
    // 当前组件值
    // 默认是给 v-model 绑定值使用的
    loading: {
      type: Boolean,
    },
  },
  data() {
    return {
      load: false,
      percent: 0,
      interId: "",
      style: {
        "z-index": "9",
        display: "none",
      },
    };
  },
  methods: {
    diff() {
      this.style = {
        "z-index": "2",
        display: "block",
      };
      this.interId = setInterval(() => {
        if (this.percent < 95) {
          let num = Math.random();
          // 2. 0~10 之间的随机数
          let num1 = num * 10;
          // 3. 0~10 之间的 随机整数
          let num2 = Math.round(num1);

          this.percent += num2;
        }
      }, 1000);
    },
    close() {
      this.percent = 100;
      if (this.interId) {
        window.clearInterval(this.interId);
        setTimeout(() => {
          this.percent = 0;
          this.style = {
            "z-index": "-1",
            display: "none",
          };
        }, 400);
      }
    },
  },
  watch: {
    loading: {
      deep: true,
      immediate: true,
      handler: function (newValue, oldValue) {
        console.log("🚀 ~ file: loading.vue:129 ~ newValue:", newValue, oldValue);
        if (newValue == true) {
          this.diff();
        }
        if (newValue == false) {
          this.close();
        }
      },
    },
  },
};
</script>

<style lang="less" scoped>
.gress {
  position: relative;
  &::after {
    content: "."; /*内容为小点，尽量不要为空，防止旧版本丢弃*/
    display: block;
    height: 0;
    visibility: hidden; /*隐藏盒子*/
    clear: both;
  }
  .pro {
    width: 100%;
    height: 100%;
    position: absolute;
    background: white;
    p {
      text-align: center;
      position: absolute;
      top: 24%;
      width: 100%;
    }
    & > div {
      position: absolute;
      top: 40%;
      width: 100%;
    }
  }
}
</style>
