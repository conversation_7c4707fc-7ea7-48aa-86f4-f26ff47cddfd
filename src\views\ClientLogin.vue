<template>
  <div class="first-loading-wrp">
    <div class="loading-wrp" style="flex-direction: column">
      <div>
        <img src="../assets/logo.png" style="width: 90px; height: 90px" />
      </div>
      <div style="margin: 20px">
        <span class="dot dot-spin">
          <i></i>
          <i></i>
          <i></i>
          <i></i>
        </span>
      </div>
      <div style="font-size: 24px; color: #333">重庆数字政协</div>
    </div>
  </div>
</template>

<script>
import storage from "@zh/common-utils/utils/storage";
import UrlUtil from "@zh/common-utils/utils/url";
import { system, auth as http } from "@zh/common-utils/http";
import { getApplicationURLByCode } from "@zh/base-ant/framework/api/application/index";

export default {
  data() {
    return {
      auth_code: this.$route.query.auth_code,
      app_code: this.$route.query.app_code,
      zwdd_id: this.$route.query.zwdd_id,
      client: this.$route.query.client,
      loading: null,
    };
  },
  created() {
    const token = UrlUtil.getURLParam("token");
    if (token) {
      this.loginSuccess({ token: token, validTime: 3600 });
    } else if (this.zwdd_id) {
      this.zwloginById();
    } else if (this.auth_code) {
      this.loginByAuthCode();
    } else {
      this.$router.redirectLogin();
    }
  },
  methods: {
    loginByAuthCode() {
      system
        .get("/zwdd/zwddLogin?auth_code=" + this.auth_code, {
          needToken: false,
        })
        .then((response) => {
          this.loginSuccess(response);
        })
        .catch((e) => {
          this.$notify.destroy();
          const h = this.$createElement;
          this.$info({
            class: "infos",
            icon: "",
            closable: true,
            title: "提示",
            content: h("div", {}, [
              h(
                "p",
                "尊敬的用户，根据应用权限设置，您没有本应用访问权限。本应用责任部门为市政协办公厅信息化工作处，若您需使用本应用，请拨打联系电话023-67176183。"
              ),
            ]),
            onOk() {},
          });
          // this.$router.redirectLogin();
        });
    },
    zwloginById() {
      http
        .get("/zwdd/loginById?zwddId=" + this.zwdd_id, {
          needToken: false,
        })
        .then((response) => {
          this.loginSuccess(response);
        })
        .catch((e) => {
          this.$notify.destroy();
          const h = this.$createElement;
          this.$info({
            class: "infos",
            icon: "",
            closable: true,
            title: "提示",
            content: h("div", {}, [
              h(
                "p",
                "尊敬的用户，根据应用权限设置，您没有本应用访问权限。本应用责任部门为市政协办公厅信息化工作处，若您需使用本应用，请拨打联系电话023-67176183。"
              ),
            ]),
            onOk() {},
          });
          // this.$router.redirectLogin();
        });
    },
    loginSuccess(response) {
      storage.setToken(response.token, response.validTime);
      let redirect = UrlUtil.getURLParam("redirect");
      if (this.client) {
        let ssoLoginUrl = this.$route.query.ssoLoginUrl;
        let redirect = this.$route.query.redirect;
        window.location.href =
          ssoLoginUrl +
          (ssoLoginUrl.indexOf("?") > -1 ? "&token=" : "?token=") +
          response.token +
          "&redirect=" +
          decodeURIComponent(redirect);
      } else if (this.app_code) {
        getApplicationURLByCode(this.app_code).then((res) => {
          if (res) {
            redirect = redirect ? redirect : res.targetUrl;
            if (res.ssoLoginUrl) {
              window.location.href =
                res.ssoLoginUrl +
                (res.ssoLoginUrl.indexOf("?") > -1 ? "&token=" : "?token=") +
                response.token +
                "&targetUrl=" +
                decodeURIComponent(redirect);
            } else {
              this.$router.replace(decodeURIComponent(redirect));
            }
          }
        });
      } else {
        if (response.token) {
          let targetUrl = UrlUtil.getURLParam("targetUrl");
          http
            .get(`/custom/sso/login?loginCode=${response.token}`)
            .then((res) => {
              debugger;
              // 缓存 token
              storage.setToken(res.token, res.validTime);
              this.$router.replace(decodeURIComponent(targetUrl));
            });
        }
      }
    },
  },
};
</script>

<style lang="less" scoped></style>
