<template>
  <div>
    <a-modal
      :visible="visible"
      :destroyOnClose="true"
      title="委员基本信息"
      width="1300px"
      @ok="handleOk"
      @cancel="cancel"
      :bodyStyle="{ overflow: 'auto', height: '68vh' }"
    >
      <Detail v-if="isDetail" :formState="formState" :imageUrl="imageUrl" />
      <div v-else class="page">
        <div>
          <a-form :form="form" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <div>
              <h3 class="tit" style="font-size: 18px; font-weight: bold">
                基本信息
              </h3>
            </div>
            <a-row>
              <a-col :span="8">
                <a-form-item label="姓名">
                  <a-input
                    v-decorator="[
                      'name',
                      {
                        rules: [{ required: false, message: '请输入' }],
                      },
                    ]"
                    placeholder="请输入"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="用户名">
                  <a-input
                    v-decorator="[
                      'userName',
                      {
                        rules: [{ required: false, message: '请输入' }],
                      },
                    ]"
                    placeholder="请输入"
                  />
                </a-form-item>
              </a-col>

              <a-col :span="8">
                <a-form-item label="性别">
                  <zh-dict
                    codeType="SEXCODE"
                    v-decorator="[
                      'sexCode',
                      { rules: [{ required: false, message: '请输入' }] },
                    ]"
                    placeholder="请输入"
                  />
                </a-form-item>
              </a-col>
            </a-row>

            <a-row>
              <a-col :span="8">
                <a-form-item label="出生日期">
                  <a-date-picker
                    v-decorator="[
                      'birthdate',
                      {
                        rules: [{ required: false, message: '请输入' }],
                      },
                    ]"
                    placeholder="请输入"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="流水号">
                  <a-input
                    v-decorator="[
                      'serialNum',
                      {
                        rules: [{ required: false, message: '请输入' }],
                      },
                    ]"
                    placeholder="请输入"
                  />
                </a-form-item>
              </a-col>

              <a-col :span="8">
                <a-form-item label="身份证号码">
                  <a-input
                    v-decorator="[
                      'idCard',
                      {
                        rules: [
                          {
                            required: false,
                            message: '请输入正确号码',
                            pattern:
                              /^([1-9]\d{5})(\d{4})(\d{2})(\d{2})(\d{3})(\d|X)$/,
                          },
                        ],
                      },
                    ]"
                    placeholder="请输入"
                  />
                </a-form-item>
              </a-col>
            </a-row>

            <a-row>
              <a-col :span="8">
                <a-form-item label="委员编号">
                  <a-input
                    v-decorator="[
                      'memNum',
                      {
                        rules: [{ required: false, message: '请输入' }],
                      },
                    ]"
                    placeholder="请输入"
                  />
                </a-form-item>
              </a-col>

              <a-col :span="8">
                <a-form-item label="民族">
                  <zh-dict
                    codeType="gb3304"
                    v-decorator="[
                      'nationCode',
                      {
                        rules: [{ required: false, message: '请点击选择' }],
                      },
                    ]"
                    @change="
                      (val, obj) => {
                        formState.nationName = obj['codeName'];
                      }
                    "
                  >
                  </zh-dict>
                </a-form-item>
              </a-col>

              <a-col :span="8">
                <a-form-item label="籍贯">
                  <a-input
                    v-decorator="[
                      'nativePlace',
                      {
                        rules: [{ required: false, message: '请输入' }],
                      },
                    ]"
                    placeholder="请输入"
                  />
                </a-form-item>
              </a-col>
            </a-row>

            <a-row>
              <a-col :span="8">
                <a-form-item label="界别">
                  <zh-dict
                    codeType="tb02"
                    v-decorator="[
                      'jbCode',
                      {
                        rules: [{ required: false, message: '请输入' }],
                      },
                    ]"
                    @change="
                      (val, obj) => {
                        formState.jbName = obj['codeName'];
                      }
                    "
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="党派">
                  <zh-dict
                    codeType="gb4762"
                    v-decorator="[
                      'politicalCode',
                      {
                        rules: [{ required: false, message: '请点击选择' }],
                      },
                    ]"
                    @change="
                      (val, obj) => {
                        formState.politicalName = obj['codeName'];
                      }
                    "
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="第二党派">
                  <zh-dict
                    codeType="gb4762"
                    v-decorator="[
                      'politicalTwoCode',
                      {
                        rules: [{ required: false, message: '请输入' }],
                      },
                    ]"
                    placeholder="请输入"
                    @change="
                      (val, obj) => {
                        formState.politicalTwoName = obj['codeName'];
                      }
                    "
                  />
                </a-form-item>
              </a-col>
            </a-row>

            <a-row>
              <a-col :span="8">
                <a-form-item label="全日制学历">
                  <zh-dict
                    codeType="qrz_xl"
                    v-decorator="[
                      'fullEducationCode',
                      {
                        rules: [{ required: false, message: '请输入' }],
                      },
                    ]"
                    placeholder="请输入"
                    @change="
                      (val, obj) => {
                        formState.fullEducationName = obj['codeName'];
                      }
                    "
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="全日制学位">
                  <a-input
                    v-decorator="[
                      'fullEducationDegreeCode',
                      {
                        rules: [{ required: false, message: '请输入' }],
                      },
                    ]"
                    placeholder="请输入"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="全日制专业">
                  <a-input
                    v-decorator="[
                      'fullEducationSpecialty',
                      {
                        rules: [{ required: false, message: '请输入' }],
                      },
                    ]"
                    placeholder="请输入"
                  />
                </a-form-item>
              </a-col>
            </a-row>

            <a-row>
              <a-col :span="8">
                <a-form-item label="推荐单位">
                  <a-input
                    v-decorator="[
                      'recommendUnitName',
                      {
                        rules: [{ required: false, message: '请输入' }],
                      },
                    ]"
                    placeholder="请输入"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="提名单位">
                  <a-input
                    v-decorator="[
                      'nominateUnitName',
                      {
                        rules: [{ required: false, message: '请输入' }],
                      },
                    ]"
                    placeholder="请输入"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="加入时间">
                  <a-date-picker
                    v-decorator="[
                      'unitJoinDate',
                      {
                        rules: [{ required: false, message: '请输入' }],
                      },
                    ]"
                    placeholder="请输入"
                  />
                </a-form-item>
              </a-col>
            </a-row>

            <a-row>
              <a-col :span="8">
                <a-form-item label="委员类型">
                  <zh-dict
                    codeType="SJZXWYLX"
                    v-decorator="[
                      'oldMemType',
                      {
                        rules: [{ required: false, message: '请输入' }],
                      },
                    ]"
                    placeholder="请输入"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="特约监督员">
                  <z-select-user
                    :limit="1"
                    v-decorator="[
                      'oldSupervisionUnitCode',
                      {
                        rules: [{ required: false, message: '请输入' }],
                      },
                    ]"
                    placeholder="请输入"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="委员进社区">
                  <a-date-picker
                    v-decorator="[
                      'oldCommunityUnitCode',
                      {
                        rules: [{ required: false, message: '请输入' }],
                      },
                    ]"
                    placeholder="请输入"
                    @change="
                      (val, obj) => {
                        formState.oldCommunityUnitName = obj['codeName'];
                      }
                    "
                  />
                </a-form-item>
              </a-col>
            </a-row>

            <a-row>
              <a-col :span="16">
                <a-form-item
                  label="单位及职务"
                  :labelCol="{ span: 6 }"
                  :wrapperCol="{ span: 18 }"
                >
                  <a-input
                    v-decorator="[
                      'workAndPost',
                      {
                        rules: [{ required: false, message: '请输入' }],
                      },
                    ]"
                    placeholder="请输入"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="单位电话">
                  <a-input
                    v-decorator="[
                      'workUnitPhone',
                      {
                        rules: [
                          {
                            required: false,
                            message: '请输入',
                            pattern: /^[0-9]*$/,
                          },
                        ],
                      },
                    ]"
                    placeholder="请输入"
                  />
                </a-form-item>
              </a-col>
            </a-row>

            <a-form-item
              label="是否违纪违法"
              :labelCol="{ span: 4 }"
              :wrapperCol="{ span: 20 }"
            >
              <a-input
                v-decorator="[
                  'oldIllegalRecord',
                  {
                    rules: [{ required: false, message: '请输入' }],
                  },
                ]"
                placeholder="请输入"
              />
            </a-form-item>

            <div>
              <h3 class="tit" style="font-size: 18px; font-weight: bold">
                届内情况
              </h3>
            </div>

            <a-row>
              <a-col :span="8">
                <a-form-item label="入选时间">
                  <a-date-picker
                    v-decorator="[
                      'memJoinDate',
                      {
                        rules: [{ required: false, message: '请输入' }],
                      },
                    ]"
                  />
                </a-form-item>
              </a-col>

              <a-col :span="8">
                <a-form-item label="离任时间">
                  <a-date-picker
                    v-decorator="[
                      'memLeaveDate',
                      {
                        rules: [{ required: false, message: '请输入' }],
                      },
                    ]"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="所属组别">
                  <zh-dict
                    codeType="WYSSZB"
                    v-decorator="[
                      'memGroupPartCode',
                      {
                        rules: [{ required: false, message: '请输入' }],
                      },
                    ]"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
            </a-row>

            <a-row>
              <a-col :span="8">
                <a-form-item label="是否连任">
                  <a-select
                    allowClear
                    placeholder="请选择"
                    v-decorator="[
                      'isTermLimits',
                      {
                        rules: [{ required: false, message: '请输入' }],
                      },
                    ]"
                  >
                    <a-select-option :value="1"> 是 </a-select-option>
                    <a-select-option :value="0"> 否 </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>

              <a-col :span="8">
                <a-form-item label="常委会组成人员">
                  <a-select
                    allowClear
                    placeholder="请选择"
                    v-decorator="[
                      'isOftenMem',
                      {
                        rules: [{ required: false, message: '请输入' }],
                      },
                    ]"
                  >
                    <a-select-option :value="1"> 是 </a-select-option>
                    <a-select-option :value="0"> 否 </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="全国政协委员">
                  <a-select
                    allowClear
                    placeholder="请选择"
                    v-decorator="[
                      'isNationwideMem',
                      {
                        rules: [{ required: false, message: '请输入' }],
                      },
                    ]"
                  >
                    <a-select-option :value="1"> 是 </a-select-option>
                    <a-select-option :value="0"> 否 </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>

            <a-row>
              <a-col :span="8">
                <a-form-item label="委员编组">
                  <zh-dict
                    codeType="WYSSZB"
                    v-decorator="[
                      'memGroupBzCode',
                      {
                        rules: [{ required: false, message: '请输入' }],
                      },
                    ]"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>

              <a-col :span="8">
                <a-form-item label="召集人">
                  <a-select
                    allowClear
                    placeholder="请选择"
                    v-decorator="[
                      'isConveneMem',
                      {
                        rules: [{ required: false, message: '请输入' }],
                      },
                    ]"
                  >
                    <a-select-option :value="1"> 是 </a-select-option>
                    <a-select-option :value="0"> 否 </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="企业家联谊会">
                  <a-select
                    allowClear
                    placeholder="请选择"
                    v-decorator="[
                      'isEntrepreneurMem',
                      {
                        rules: [{ required: false, message: '请输入' }],
                      },
                    ]"
                  >
                    <a-select-option :value="1"> 是 </a-select-option>
                    <a-select-option :value="0"> 否 </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>

            <div>
              <h3 class="tit" style="font-size: 18px; font-weight: bold">
                工作情况
              </h3>
            </div>

            <a-row>
              <a-col :span="8">
                <a-form-item label="所属单位">
                  <zh-dict
                    codeType="ZB01"
                    v-decorator="[
                      'affiliatedUnitCity',
                      {
                        rules: [{ required: false, message: '请输入' }],
                      },
                    ]"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="所属单位详情">
                  <a-input
                    v-decorator="[
                      'affiliatedUnitDetail',
                      {
                        rules: [{ required: false, message: '请输入' }],
                      },
                    ]"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>

              <a-col :span="8">
                <a-form-item label="技术职称">
                  <zh-dict
                    codeType="PROPOST_TYPE"
                    v-decorator="[
                      'technologyCode',
                      {
                        rules: [{ required: false, message: '请输入' }],
                      },
                    ]"
                    style="width: 100%"
                    @change="
                      (val, obj) => {
                        formState.technologyName = obj['codeName'];
                      }
                    "
                  />
                </a-form-item>
              </a-col>
            </a-row>

            <a-row>
              <a-col :span="8">
                <a-form-item label="参加工作时间">
                  <a-date-picker
                    v-decorator="[
                      'workDate',
                      {
                        rules: [{ required: false, message: '请输入' }],
                      },
                    ]"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="专业职务">
                  <a-input
                    v-decorator="[
                      'technologyPost',
                      {
                        rules: [{ required: false, message: '请输入' }],
                      },
                    ]"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="职务级别">
                  <zh-dict
                    codeType="ZB09_1"
                    v-decorator="[
                      'oldGwyPostCode',
                      {
                        rules: [{ required: false, message: '请输入' }],
                      },
                    ]"
                    style="width: 100%"
                    @change="
                      (val, obj) => {
                        formState.oldGwyPostName = obj['codeName'];
                      }
                    "
                  />
                </a-form-item>
              </a-col>
            </a-row>

            <div>
              <h3 class="tit" style="font-size: 18px; font-weight: bold">
                联系信息
              </h3>
            </div>

            <a-row>
              <a-col :span="8">
                <a-form-item label="单位通讯地址">
                  <a-input
                    v-decorator="[
                      'workAddress',
                      {
                        rules: [{ required: false, message: '请输入' }],
                      },
                    ]"
                    placeholder="请输入"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="邮政编码(办公)">
                  <a-input
                    v-decorator="[
                      'workPostcode',
                      {
                        rules: [{ required: false, message: '请输入' }],
                      },
                    ]"
                    placeholder="请输入"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="工作电话">
                  <a-input
                    v-decorator="[
                      'workPhoneNum',
                      {
                        rules: [
                          {
                            required: false,
                            message: '请输入',
                            pattern: /^[0-9]*$/,
                          },
                        ],
                      },
                    ]"
                    placeholder="请输入"
                  />
                </a-form-item>
              </a-col>
            </a-row>

            <a-row>
              <a-col :span="8">
                <a-form-item label="家庭住址">
                  <a-input
                    v-decorator="[
                      'homeAddress',
                      {
                        rules: [{ required: false, message: '请输入' }],
                      },
                    ]"
                    placeholder="请输入"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="邮政编码(住宅)">
                  <a-input
                    v-decorator="[
                      'homePostcode',
                      {
                        rules: [{ required: false, message: '请输入' }],
                      },
                    ]"
                    placeholder="请输入"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="住宅电话">
                  <a-input
                    v-decorator="[
                      'homePhoneNum',
                      {
                        rules: [
                          {
                            required: false,
                            message: '请输入',
                            pattern: /^[0-9]*$/,
                          },
                        ],
                      },
                    ]"
                    placeholder="请输入"
                  />
                </a-form-item>
              </a-col>
            </a-row>

            <a-row>
              <a-col :span="8">
                <a-form-item label="电子邮箱">
                  <a-input
                    v-decorator="[
                      'email',
                      {
                        rules: [{ required: false, message: '请输入' }],
                      },
                    ]"
                    placeholder="请输入"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="传真">
                  <a-input
                    v-decorator="[
                      'faxNum',
                      {
                        rules: [{ required: false, message: '请输入' }],
                      },
                    ]"
                    placeholder="请输入"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="手机号码">
                  <a-input
                    v-decorator="[
                      'phoneNum',
                      {
                        rules: [
                          {
                            required: false,
                            message: '请输入正确手机号',
                            pattern: /^[0-9]*$/,
                          },
                        ],
                      },
                    ]"
                    placeholder="请输入"
                  />
                </a-form-item>
              </a-col>

              <a-col :span="8">
                <a-form-item label="是否邮寄">
                  <a-select
                    allowClear
                    placeholder="请选择"
                    v-decorator="[
                      'isMail',
                      {
                        rules: [{ required: false, message: '请输入' }],
                      },
                    ]"
                  >
                    <a-select-option :value="1"> 是 </a-select-option>
                    <a-select-option :value="0"> 否 </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>

            <div>
              <h3 class="tit" style="font-size: 18px; font-weight: bold">
                扩展信息
              </h3>
            </div>

            <a-row>
              <a-col :span="8">
                <a-form-item label="继任委员证号">
                  <a-input
                    v-decorator="[
                      'lastMemNum',
                      {
                        rules: [{ required: false, message: '请输入' }],
                      },
                    ]"
                    placeholder="请输入"
                  />
                </a-form-item>
              </a-col>

              <a-col :span="8">
                <a-form-item label="出生地">
                  <a-input
                    v-decorator="[
                      'birthplace',
                      {
                        rules: [{ required: false, message: '请输入' }],
                      },
                    ]"
                    placeholder="请输入"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="健康状况">
                  <zh-dict
                    codeType="gb22613"
                    v-decorator="[
                      'healthCode',
                      {
                        rules: [{ required: false, message: '请输入' }],
                      },
                    ]"
                    placeholder="请输入"
                    @change="
                      (val, obj) => {
                        formState.healthName = obj['codeName'];
                      }
                    "
                  />
                </a-form-item>
              </a-col>
            </a-row>

            <a-row>
              <a-col :span="8">
                <a-form-item label="有何专长">
                  <a-input
                    v-decorator="[
                      'speciality',
                      {
                        rules: [{ required: false, message: '请输入' }],
                      },
                    ]"
                    placeholder="请输入"
                  />
                </a-form-item>
              </a-col>

              <a-col :span="8">
                <a-form-item label="入党时间">
                  <a-date-picker
                    v-decorator="[
                      'politicalDate',
                      {
                        rules: [{ required: false, message: '请输入' }],
                      },
                    ]"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="职称">
                  <zh-dict
                    codeType="zb094"
                    v-decorator="[
                      'rankCode',
                      {
                        rules: [{ required: false, message: '请输入' }],
                      },
                    ]"
                    @change="
                      (val, obj) => {
                        formState.rankName = obj['codeName'];
                      }
                    "
                  />
                </a-form-item>
              </a-col>
            </a-row>

            <a-row>
              <a-col :span="8">
                <a-form-item label="第二党派入党时间">
                  <a-date-picker
                    v-decorator="[
                      'politicalTwoDate',
                      {
                        rules: [{ required: false, message: '请输入' }],
                      },
                    ]"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="委员职务">
                  <zh-dict
                    codeType="zb14"
                    v-decorator="[
                      'specialRegionalGroupPost',
                      {
                        rules: [{ required: false, message: '请输入' }],
                      },
                    ]"
                    placeholder="请输入"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="委员任职时间">
                  <a-date-picker
                    v-decorator="[
                      'committeePostDate',
                      {
                        rules: [{ required: false, message: '请输入' }],
                      },
                    ]"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
            </a-row>

            <a-form-item
              label="委员任免记录"
              :labelCol="{ span: 4 }"
              :wrapperCol="{ span: 20 }"
            >
              <a-textarea
                :autosize="{ minRows: 4, maxRows: 8 }"
                v-decorator="[
                  'removalRecord',
                  {
                    rules: [{ required: false, message: '请输入' }],
                  },
                ]"
                placeholder="请输入"
              />
            </a-form-item>

            <a-row>
              <a-col :span="8">
                <a-form-item label="常委组成人员">
                  <zh-dict
                    codeType="kz15"
                    v-decorator="[
                      'oftenMemPost',
                      {
                        rules: [{ required: false, message: '请输入' }],
                      },
                    ]"
                    placeholder="请输入"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="常委任职时间">
                  <a-date-picker
                    v-decorator="[
                      'oftenMemPostDate',
                      {
                        rules: [{ required: false, message: '请输入' }],
                      },
                    ]"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="常委离任时间">
                  <a-date-picker
                    v-decorator="[
                      'oftenMemPostOutDate',
                      {
                        rules: [{ required: false, message: '请输入' }],
                      },
                    ]"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
            </a-row>

            <a-form-item
              label="常委任免记录"
              :labelCol="{ span: 4 }"
              :wrapperCol="{ span: 20 }"
            >
              <a-input
                v-decorator="[
                  'oftenMemRemovalRecord',
                  {
                    rules: [{ required: false, message: '请输入' }],
                  },
                ]"
                placeholder="请输入"
              />
            </a-form-item>

            <a-row>
              <a-col :span="8">
                <a-form-item label="专委会名称">
                  <zh-dict
                    codeType="tb04"
                    v-decorator="[
                      'specialCode',
                      {
                        rules: [{ required: false, message: '请输入' }],
                      },
                    ]"
                    placeholder="请输入"
                    @change="
                      (val, obj) => {
                        formState.specialName = obj['codeName'];
                      }
                    "
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="专委会职务">
                  <zh-dict
                    codeType="tb04_zw"
                    v-decorator="[
                      'specialPostCode',
                      {
                        rules: [{ required: false, message: '请输入' }],
                      },
                    ]"
                    placeholder="请输入"
                    @change="
                      (val, obj) => {
                        formState.specialPostName = obj['codeName'];
                      }
                    "
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="专委会任职时间">
                  <a-date-picker
                    v-decorator="[
                      'specialPostDate',
                      {
                        rules: [{ required: false, message: '请输入' }],
                      },
                    ]"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
            </a-row>

            <a-row>
              <a-col :span="8">
                <a-form-item label="专委会离任时间">
                  <a-date-picker
                    v-decorator="[
                      'specialPostOutDate',
                      {
                        rules: [{ required: false, message: '请输入' }],
                      },
                    ]"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="委员工作站">
                  <a-input
                    v-decorator="[
                      'specialRegionalGroupCode',
                      {
                        rules: [{ required: false, message: '请输入' }],
                      },
                    ]"
                    placeholder="请输入"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="委员工作站职务">
                  <a-input
                    v-decorator="[
                      'specialRegionalGroupPost',
                      {
                        rules: [{ required: false, message: '请输入' }],
                      },
                    ]"
                    placeholder="请输入"
                  />
                </a-form-item>
              </a-col>
            </a-row>

            <a-form-item
              label="专委会任免记录"
              :labelCol="{ span: 4 }"
              :wrapperCol="{ span: 20 }"
            >
              <a-input
                v-decorator="[
                  'specialRemovalRecord',
                  {
                    rules: [{ required: false, message: '请输入' }],
                  },
                ]"
                placeholder="请输入"
              />
            </a-form-item>

            <a-form-item
              label="委员工作站安排记录"
              :labelCol="{ span: 4 }"
              :wrapperCol="{ span: 20 }"
            >
              <a-textarea
                :autosize="{ minRows: 4, maxRows: 8 }"
                v-decorator="[
                  'specialRegionalGroupRecord',
                  {
                    rules: [{ required: false, message: '请输入' }],
                  },
                ]"
                placeholder="请输入"
              />
            </a-form-item>

            <a-row>
              <a-col :span="8">
                <a-form-item label="全日制院校">
                  <a-input
                    v-decorator="[
                      'fullEducationSchool',
                      {
                        rules: [{ required: false, message: '请输入' }],
                      },
                    ]"
                    placeholder="请输入"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="在职学历">
                  <zh-dict
                    codeType="qrz_xl"
                    v-decorator="[
                      'workEducationCode',
                      {
                        rules: [{ required: false, message: '请输入' }],
                      },
                    ]"
                    placeholder="请输入"
                    @change="
                      (val, obj) => {
                        formState.workEducationName = obj['codeName'];
                      }
                    "
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="在职学位">
                  <a-input
                    v-decorator="[
                      'workEducationDegreeCode',
                      {
                        rules: [{ required: false, message: '请输入' }],
                      },
                    ]"
                    placeholder="请输入"
                  />
                </a-form-item>
              </a-col>
            </a-row>

            <a-row>
              <a-col :span="8">
                <a-form-item label="在职院校">
                  <a-input
                    v-decorator="[
                      'workEducationSchool',
                      {
                        rules: [{ required: false, message: '请输入' }],
                      },
                    ]"
                    placeholder="请输入"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="在职专业">
                  <a-input
                    v-decorator="[
                      'workEducationSpecialty',
                      {
                        rules: [{ required: false, message: '请输入' }],
                      },
                    ]"
                    placeholder="请输入"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="兼任职务">
                  <a-input
                    v-decorator="[
                      'otherWorkPost',
                      {
                        rules: [{ required: false, message: '请输入' }],
                      },
                    ]"
                    placeholder="请输入"
                  />
                </a-form-item>
              </a-col>
            </a-row>

            <a-row>
              <a-col :span="8">
                <a-form-item label="移动电话">
                  <a-input
                    v-decorator="[
                      'phone',
                      {
                        rules: [
                          {
                            required: false,
                            message: '请输入正确号码',
                            pattern: /^[0-9]*$/,
                          },
                        ],
                      },
                    ]"
                    placeholder="请输入"
                  />
                </a-form-item>
              </a-col>
            </a-row>

            <a-form-item
              label="联系地址"
              :labelCol="{ span: 4 }"
              :wrapperCol="{ span: 20 }"
            >
              <a-input
                v-decorator="[
                  'address',
                  {
                    rules: [{ required: false, message: '请输入' }],
                  },
                ]"
                placeholder="请输入"
              />
            </a-form-item>
            <a-form-item
              label="个人简历"
              :labelCol="{ span: 4 }"
              :wrapperCol="{ span: 20 }"
            >
              <a-textarea
                :autosize="{ minRows: 4, maxRows: 8 }"
                v-decorator="[
                  'resume',
                  {
                    rules: [{ required: false, message: '请输入' }],
                  },
                ]"
                placeholder="请输入"
              />
            </a-form-item>

            <a-form-item
              label="主要表现"
              :labelCol="{ span: 4 }"
              :wrapperCol="{ span: 20 }"
            >
              <a-textarea
                :autosize="{ minRows: 4, maxRows: 8 }"
                v-decorator="[
                  'expression',
                  {
                    rules: [{ required: false, message: '请输入' }],
                  },
                ]"
                placeholder="请输入"
              />
            </a-form-item>

            <a-form-item
              label="主要成就"
              :labelCol="{ span: 4 }"
              :wrapperCol="{ span: 20 }"
            >
              <a-textarea
                :autosize="{ minRows: 4, maxRows: 8 }"
                v-decorator="[
                  'achievement',
                  {
                    rules: [{ required: false, message: '请输入' }],
                  },
                ]"
                placeholder="请输入"
              />
            </a-form-item>

            <a-form-item
              label="备注"
              :labelCol="{ span: 4 }"
              :wrapperCol="{ span: 20 }"
            >
              <a-textarea
                :autosize="{ minRows: 4, maxRows: 8 }"
                v-decorator="[
                  'remark',
                  {
                    rules: [{ required: false, message: '请输入' }],
                  },
                ]"
                placeholder="请输入"
              />
            </a-form-item>
          </a-form>
        </div>

        <!-- <div>
          <z-image-uploader
            :parentId="formState.id"
            :limit="1"
            v-model="formState.fileList"
            configGroup="zx"
            ref="imgUploader"
          />
        </div> -->
        <div class="upimg">
          <a-upload
            :maxCount="1"
            :file-list="fileList"
            name="file"
            list-type="picture-card"
            class="avatar-uploader"
            :show-upload-list="false"
            :action="`${baseUrl}/mem/upload`"
            @change="handleChange"
            :data="handleData"
            :headers="{
              Token:tokenObj.value
            }"
          >
            <img v-if="imageUrl" :src="imageUrl" alt="头像" class="img" />
            <div v-else>
              <div class="add">+</div>
              <div class="ant-upload-text">上传头像</div>
            </div>
          </a-upload>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script>
import { auth as http } from "@zh/common-utils/http";
import Detail from "./detail.vue";
import dayjs from "dayjs";

// 文件、图片类型
import MimeType from "@zh/common-utils/file/mime-type";

export default {
  components: {
    Detail,
  },
  props: {
    pId: String,
    callBack: Function,
    session: Object,
  },
  data() {
    return {
      baseUrl:process.env.VUE_APP_API_BASE_URL,
      isDetail: false,
      form: this.$form.createForm(this, { name: "council_mem" }),
      visible: false,
      labelCol: { span: 12 },
      wrapperCol: { span: 12 },
      mimeType: MimeType,
      formState: {
        name: undefined,
        region: undefined,
        date1: undefined,
        delivery: undefined,
        type: undefined,
        resource: undefined,
        desc: undefined,
      },
      imgLoading: false,
      imageUrl:'',
      imageUrlFilePath:'',
      fileList:[],
      tokenObj: JSON.parse(localStorage.getItem("access-token")),
    };
  },
  
  methods: {
    open(id, isDetail = false) {
      this.isDetail = isDetail;
      if (id) {
        this.getDetail(id);
      }
      this.visible = true;
    },
    handleData(file){ 
      return {
        module:'custom',
      }
    },
    getDetail(id) {
      if (id) {
        http.get(`/mem/find?id=${id}`).then((res) => {
          this.visible = true;
          this.formState = res;
          // 下载头像
         this.getImg(res.pic)
          setTimeout(() => {
            this.form.setFieldsValue({ ...res });
          }, 200);
        });
      } else {
        this.formState = {};
        this.form.resetFields();
      }
    },
    // 下载头像
    getImg(param){
      if(param){
            http.post(`/mem/downloadByPathBase64`,{filePath: param}).then((imgRes) => {
              // this.imageUrl = URL.createObjectURL(res)
              this.imageUrl = `data:image/png;base64,${imgRes}`
            })
          }else{
            this.imageUrl=''
          }
    },
    handleChange (info) {
      this.fileList=info.fileList
      if (info.file.status === 'uploading') {
        this.imgLoading = true;
        return;
      }
      if (info.file.status === 'done') {
        const fileObj=info.file
        const {response:{code=500,data=''}={}} = fileObj
        if(code==0){
          this.getImg(data)
          this.imageUrlFilePath=data
        }
      }
      if (info.file.status === 'error') {
        this.imgLoading = false;
        message.error('upload error');
      }
    },
    cancel() {
      this.visible = false;
      this.formState = {};
      this.form.resetFields();
      this.fileList=[],
      this.imageUrl='',
      this.this.imageUrlFilePath=''
    },
    valChange(val) {
      console.log("🚀 ~ file: addEdit.vue:1369 ~ valChange ~ val:", val);
    },
    handleOk() {
      this.form.validateFields((err, value) => {
        if (!err) {
          console.info("success", this.img);
          Object.keys(value).forEach((key) => {
            if (key.includes("Date") && value[key]) {
              value[key] = dayjs(value[key]).valueOf();
            }
          });
          let url = "/mem/save";
          if (this.formState["id"]) {
            url = "/mem/update";
          }
          http
            .post(url, {
              sessionCode: this.session["id"],
              ...this.formState,
              ...value,
              pic: this.imageUrlFilePath || undefined,
            })
            .then((res) => {
              this.cancel();
              this.$emit("callBack");
            });
        }
      });
      // this.cancel();
    },
  },
};
</script>

<style scoped lang="less">
@import url("./addEdit.less");
.tit {
  border-left: 6px solid #0da1ff;
  height: 21px;
  line-height: 21px;
  color: #333;
  font-size: 18px;
  padding-left: 9px;
  margin: 0px 0px 30px 10px;
  text-align: left;
}
.upimg{
  .add{
    font-size: 20px;
  }
  .img{
    // position: absolute;
    // right: 30px;
    // top: 80px;
    width: 110px;
    height: 120px;
  }
}
</style>
