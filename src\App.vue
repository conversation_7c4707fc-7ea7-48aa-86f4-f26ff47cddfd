<template>
  <ConfigProvider :locale="zhCN">
    <div id="app">
      <router-view />
    </div>
  </ConfigProvider>
</template>

<script>
import zhCN from "ant-design-vue/lib/locale-provider/zh_CN";
import { ConfigProvider } from "ant-design-vue";
import { store, globalComponent } from "@zh/base-ant";

export default {
  components: { ConfigProvider },
  data() {
    return {
      menu: [],
      zhCN,
    };
  },
  mounted() {
    this.inId = setInterval(this.dChange, 150);
  },
  methods: {
    dChange() {
      let element = document.getElementsByClassName("logo");
      if (element.length > 0) {
        // 创建监听器
        if (this.inId) {
          window.clearInterval(this.inId);
        }
        this.ob = new ResizeObserver((entries) => {
          for (let entry of entries) {
            // 监听到的元素
            // console.log(entry.target.clientWidth, "eee");
            let width = entry.target.clientWidth;
            if (width != 200) {
              entry.target.innerHTML = '<img src="/logo.png">';
            } else {
              entry.target.innerHTML = '<h1 style="margin: 0px;color: #4181ff;" class="fade-in">重庆数字政协</h1>';
            }
          }
        });
        // 监听尺寸变化
        this.ob.observe(element[0]);
      }
    },
  },
  destroyed() {
    if (this.inId) {
      window.clearInterval(this.inId);
    }
  },
};
</script>
<style lang="less">
.logo {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 22px;
  font-weight: bold;
  width: 100%;
  height: 70px;
  line-height: 70px;
  & > img {
    width: 42px !important;
    height: 42px !important;
  }
}
.fade-in {
  width: 200px;
  overflow: hidden;
  opacity: 0;
  /*使用opacity属性控制元素透明度*/
  animation-name: fade-in;
  /*使用animation-name属性赋予动画名称*/
  animation-duration: 0.8s;
  /*使用animation-duration属性控制动画时长*/
  animation-fill-mode: forwards;
  /*使用animation-fill-mode属性控制动画结束后元素的样式保持与动画最后一帧样式一致*/
}
@keyframes fade-in {
  /*使用@keyframes定义动画*/
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
.basic-layout-main > main > div:first-child {
  min-height: calc(100vh - 112px) !important;
}
.ant-spin {
  height: 72vh !important;
  max-height: 72vh !important;
  .ant-spin-dot {
    margin: 20px -10px !important;
  }
  &::before {
    background-image: url("./assets/logo.png");
    background-size: 100% 100%;
    width: 90px;
    height: 90px;
    content: "";
    display: block;
    margin: auto;
    margin-top: 17%;
  }
  &::after {
    content: "\91cd\5e86\6570\5b57\653f\534f";
    display: block;
    position: relative;
    height: 30px;
    font-size: 24px;
    color: #333;
    margin-top: 50px;
  }
}
.cp-page {
  padding: 24px;
}

.infos {
  .ant-modal {
    top: 30%;
  }
  .ant-modal-confirm-body {
    & > i:first-child {
      display: none !important;
    }
  }
  .ant-modal-confirm-btns {
    display: none !important;
  }
  .ant-modal-confirm-title {
    text-align: center;
  }
  .ant-modal-confirm-content {
    margin-left: 0px !important;
    p {
      font-size: 16px;
      line-height: 24px;
      text-indent: 2em;
    }
  }
}

.txt-over {
  display: inline-block;
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.ant-table table {
  table-layout: fixed;
}
.ant-table-tbody > tr > td {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
