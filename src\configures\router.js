
import { router } from '@zh/base-ant'

import storage from '@zh/common-utils/utils/storage'

import { ZRouteView } from '@zh/components-ant'

const install = (Vue) => {
  if (install.installed) {
    return
  }
  router.setDocumentTitle = (to) => {
    let appName = '';
    let name = '重庆数字政协';
    const routeList = to.matched.concat()
    let meta = routeList.length == 0 ? {} : routeList.pop().meta
    while (meta.hidden) {
      meta = routeList.pop().meta
    }
    setTimeout(() => {
      if (meta?.aliveKey) {
        if (`${meta.aliveKey}`.startsWith("/sendread")) {
          appName = "政协送阅";
        } else if (`${meta.aliveKey}`.startsWith("/jobinfor")) {
          appName = "政协工作信息";
        } else if (`${meta.aliveKey}`.startsWith("/panorama")) {
          appName = "全景图";
        }
        document.title = name + `${appName ? `-${appName}` : ''}`
      }
    }, 100)
  }
  // 加载路由配置
  router.loadRouteConfigFiles(require.context('../routes', true, /\.js$/), 'pc')

  // 注册动态路由
  // import demo from '../routes-dynamic/demo'
  // router.registDynamicRoutes()

  /**
   * ##################################
   * 注册静态路由（无需登录即可访问）
   * ##################################
   */
  router.registStaticRoutes([{
    path: '/login',
    name: 'login',
    hidden: true,
    component: () => import('@/views/Login')
  },
  {
    path: '/loginHkMac',
    name: 'login',
    hidden: true,
    component: () => import('@/views/LoginHkMac')
  },
  {
    path: '/mid',
    name: 'midPage',
    hidden: true,
    component: () => import('@/views/MidPage')
  },
  {
    path: '/clientLogin',
    name: 'ClientLogin',
    hidden: true,
    component: () => import('@/views/ClientLogin')
  },
  {
    path: '/expired',
    hidden: true,
    component: () => import('@/views/loginout')
  },
  {
    path: '*',
    hidden: true,
    component: () => import('@/views/404')
  }])

  /**
   * ##################################
   * 注册根路由
   * ##################################
   */
  // router.registRootChildren([{
  //   path: '/dashboard',
  //   component: ZRouteView,
  //   redirect: '/dashboard/workplace',
  //   meta: { icon: 'dashboard', title: '仪表盘' },
  //   children: [{
  //     path: '/dashboard/workplace',
  //     component: () => import('@/views/dashboard/Workplace'),
  //     meta: { title: '工作台' }
  //   }, {
  //     path: '/dashboard/analysis',
  //     component: () => import('@/views/dashboard/Analysis'),
  //     meta: { title: '分析页' }
  //   }, {
  //     path: 'https://lbs.amap.com/api/webservice/summary/',
  //     meta: { target: 'iframe', title: '内嵌iframe' }
  //   }, {
  //     path: 'https://www.baidu.com',
  //     meta: { target: '_blank', title: '外部链接' }
  //   }]
  // }], '/')

  /**
   * ##################################
   * 实现重定向登录接口
   * ##################################
   */
  router.redirectLogin = (redirectURL) => {
    if (router.currentRoute.path == '/login') {
      return
    }
    if (!redirectURL || redirectURL == '/login') {
      redirectURL = '/'
    }
    storage.removeToken()
    location.replace(`/expired`)
    // location.replace(`/login?redirect=${encodeURIComponent(redirectURL || location.pathname + location.search)}`)
  }
}

export default {
  install
}