!function e(t,n){"object"==typeof exports&&"object"==typeof module?module.exports=n():"function"==typeof define&&define.amd?define([],n):"object"==typeof exports?exports.index=n():t.index=n()}(window,(function(){return function(e){var t={};function n(i){if(t[i])return t[i].exports;var o=t[i]={i:i,l:!1,exports:{}};return e[i].call(o.exports,o,o.exports,n),o.l=!0,o.exports}return n.m=e,n.c=t,n.d=function(e,t,i){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:i})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var i=Object.create(null);if(n.r(i),Object.defineProperty(i,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var o in e)n.d(i,o,function(t){return e[t]}.bind(null,o));return i},n.n=function(e){var t=e&&e.__esModule?function t(){return e.default}:function t(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="./dist/",n(n.s=9)}([function(e,t,n){"use strict";function i(e){return(i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var o=this&&this.__assign||function(){return(o=Object.assign||function(e){for(var t,n=1,i=arguments.length;n<i;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},r=this&&this.__awaiter||function(e,t,n,i){function o(e){return e instanceof n?e:new n((function(t){t(e)}))}return new(n||(n=Promise))((function(n,r){function a(e){try{l(i.next(e))}catch(e){r(e)}}function u(e){try{l(i.throw(e))}catch(e){r(e)}}function l(e){e.done?n(e.value):o(e.value).then(a,u)}l((i=i.apply(e,t||[])).next())}))},a=this&&this.__generator||function(e,t){var n={label:0,sent:function e(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]},i,o,r,a;return a={next:u(0),throw:u(1),return:u(2)},"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function u(e){return function(t){return l([e,t])}}function l(a){if(i)throw new TypeError("Generator is already executing.");for(;n;)try{if(i=1,o&&(r=2&a[0]?o.return:a[0]?o.throw||((r=o.return)&&r.call(o),0):o.next)&&!(r=r.call(o,a[1])).done)return r;switch(o=0,r&&(a=[2&a[0],r.value]),a[0]){case 0:case 1:r=a;break;case 4:return n.label++,{value:a[1],done:!1};case 5:n.label++,o=a[1],a=[0];continue;case 7:a=n.ops.pop(),n.trys.pop();continue;default:if(!(r=n.trys,(r=r.length>0&&r[r.length-1])||6!==a[0]&&2!==a[0])){n=0;continue}if(3===a[0]&&(!r||a[1]>r[0]&&a[1]<r[3])){n.label=a[1];break}if(6===a[0]&&n.label<r[1]){n.label=r[1],r=a;break}if(r&&n.label<r[2]){n.label=r[2],n.ops.push(a);break}r[2]&&n.ops.pop(),n.trys.pop();continue}a=t.call(e,n)}catch(e){a=[6,e],o=0}finally{i=r=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}};Object.defineProperty(t,"__esModule",{value:!0}),t.Container_Type_Enum=t.CONTINUOUS_EVENT_LIST=t.PLATFORM_TYPE_ENUM=t.API_INVOKER_TYPE=t.BRIDGE_ERROR_CODE=void 0;var u=n(2),l,s,d,f,c;!function(e){e.CANCEL="-1",e.SUCCESS="0",e.API_UNDEFINED="1",e.INVALID_PARAMS="2",e.UNKNOWN_ERROR="3",e.UNAUTHORIZED_CALL="4",e.WRONG_CORP_ID="5",e.CREATE_CHAT_FAILED="6",e.UNAUTHORIZED_API="7",e.INVALID_CORP_ID="8",e.SERVER_RESPONSE_ERROR="9",e.WRONG_DEVICE_INFO="10",e.UPLOAD_FAIL="11",e.PROCESS_FAIL="12",e.DUPLICATED_CALL="13",e.TOO_LARGE_PIC="14",e.REQUEST_REJECT_OR_INSECURE_REQUEST="15",e.PC_NOT_ALLOWED_TO_OPEN_SIDE_PANE_OR_MODAL="21",e.PC_CLOSE_SIDE_PANE_OR_MODAL="22",e.UNAUTHORIZED_PARAMS="23",e.GESTURE_PASSWORD_DOES_NOT_EXIST="24",e.NETWORK_ERROR="25"}(l=t.BRIDGE_ERROR_CODE||(t.BRIDGE_ERROR_CODE={})),function(e){e.MOBILE="mobile",e.PC="pc",e.MINI_APP="mini",e.UNKNOWN="unknown"}(s=t.API_INVOKER_TYPE||(t.API_INVOKER_TYPE={})),function(e){e.ANDROID="android",e.IOS="ios",e.UNKNOW="unknow"}(d=t.PLATFORM_TYPE_ENUM||(t.PLATFORM_TYPE_ENUM={})),function(e){e.UPDATE_NETWORK_STATUS="DINGGOV_ON_NETWORK_TYPE_CHANGED",e.UPDATE_LOCATION="DINGGOV_GEO_LOCATION_UPDATE",e.UPDATE_TRACE="DINGGOV_TRACE_UPDATE",e.ON_SHAKE="onShake"}(f=t.CONTINUOUS_EVENT_LIST||(t.CONTINUOUS_EVENT_LIST={})),function(e){e.isDingTalk="DingTalk",e.isMpaas="mPaaS",e.isUnknow="unknow"}(c=t.Container_Type_Enum||(t.Container_Type_Enum={}));var p=navigator&&(navigator.swuserAgent||navigator.userAgent)||"",v=function(){function e(){this.readyFnStack=[],this.generalEventCallbackStack={},this.apiList={},this.continuousCallbackStack={},this.isH5Mobile=null,this.appType=null,this.platformType=null,this.aliBridge=window&&window.navigator&&window.AlipayJSBridge,this.isReady=!1,this.init()}return e.prototype.h5AndroidbridgeInit=function(){var e=this;this.h5BridgeReadyPromise=new Promise((function(t,n){var i=function t(){try{window.WebViewJavascriptBridgeAndroid=window.nuva&&window.nuva.require(),e.execReadyFn()}catch(e){}};window.nuva&&(void 0===window.nuva.isReady||window.nuva.isReady)?i():(document.addEventListener("runtimeready",(function(){i()}),!1),document.addEventListener("runtimefailed",(function(i){var o=i&&i.detail||{errorCode:l.INVALID_PARAMS,errorMessage:"unknown nuvajs bootstrap error"};e.handleBridgeResponse(o,t,n)}),!1))}))},e.prototype.h5IosBridgeInit=function(){var e=this;this.h5BridgeReadyPromise=new Promise((function(t,n){if("undefined"!=typeof WebViewJavascriptBridge)try{WebViewJavascriptBridge.init((function(e,t){})),e.execReadyFn()}catch(e){}else document.addEventListener("WebViewJavascriptBridgeReady",(function(){try{WebViewJavascriptBridge&&WebViewJavascriptBridge.init((function(e,t){})),e.execReadyFn()}catch(e){}}),!1)}))},e.prototype.init=function(){var e=this,t=this.getAppType(),n=this.getContainerType();if(t===s.PC&&window.dingtalk&&!window.dingtalk.isRegister&&(window.dingtalk.isRegister=!0,window.dingtalk.callbackStack={},window.dingtalk.event.register((function(t,n){if(e.continuousCallbackStack[t])e.continuousCallbackStack[t](n);else if(n){var i=""+n.msgId;"openapi.event.emit"===t?(console.log("dingtalk receive event:",n,"identifer is",i),window.dingtalk.callbackStack[i]&&(window.dingtalk.callbackStack[i](n),delete window.dingtalk.callbackStack[i])):"im.fileTask.addNewTask"===t||"im.fileTask.updateTask"===t?(n.msgId||n.taskId)&&"function"==typeof e.continuousCallbackStack[n.msgId||n.taskId]&&e.continuousCallbackStack[n.msgId||n.taskId](t,n):e.generalEventCallbackStack[t]&&e.generalEventCallbackStack[t].forEach((function(t){t.call(e,n)}))}}))),t===s.MOBILE){if(n===c.isDingTalk)this.platformType===d.ANDROID?!this.h5BridgeReadyPromise&&this.h5AndroidbridgeInit():this.platformType===d.IOS&&!this.h5BridgeReadyPromise&&this.h5IosBridgeInit();else if(n===c.isMpaas&&t===s.MOBILE)if(window.AlipayJSBridge)this.execReadyFn();else{var i=setTimeout((function(){console.warn("window.AlipayJSBridge \u672a\u521d\u59cb\u5316\u5b8c\u6bd5\uff0c\u8d70\u5230\u515c\u5e95\u903b\u8f91",e.isReady,window.AlipayJSBridge),e.isReady||e.execReadyFn.call(e)}),5200);document.addEventListener("AlipayJSBridgeReady",(function(){e.isReady||(clearTimeout(i),e.execReadyFn.call(e))}),!1)}}else setTimeout((function(){e.execReadyFn()}))},e.prototype.execReadyFn=function(){this.isReady=!0;for(var e=this.readyFnStack.shift();e;)e&&e(this),e=this.readyFnStack.shift()},e.prototype.onReady=function(e){this.isReady?e&&e(this):this.readyFnStack.push(e)},e.prototype.setCurrentInvoker=function(e){this.currentInvoker=e},e.prototype.getCurrentInvoker=function(){return this.currentInvoker},e.prototype.getBridge=function(){return this.aliBridge},e.prototype.getContainerType=function(){return/TaurusApp/g.test(p)?/DingTalk/g.test(p)?c.isDingTalk:c.isMpaas:/DingTalk/g.test(p)?c.isDingTalk:/mPaaSClient/g.test(p)||/Nebula/g.test(p)?c.isMpaas:c.isUnknow},e.prototype.getAppType=function(){return this.appType||(this.isMobile()?this.appType=s.MOBILE:window&&window.navigator&&window.navigator.userAgent&&window.navigator.userAgent.indexOf("dingtalk-win")>=0?this.appType=s.PC:u.isMiniApp()?this.appType=s.MINI_APP:(console.warn("\u68c0\u6d4b\u5230\u9875\u9762\u5728\u975e\u653f\u52a1\u9489\u9489\u5ba2\u6237\u7aef\u4e2d\u6253\u5f00\uff0cJSAPI \u8c03\u7528\u53ef\u80fd\u4e0d\u4f1a\u751f\u6548\uff01"),this.appType=s.UNKNOWN)),this.appType},e.prototype.isMobile=function(){var e=/iPhone|iPad|iPod|iOS/i.test(p),t=/Android/i.test(p),n=window&&window.navigator&&window.navigator.userAgent||"";return null!==this.isH5Mobile?this.isH5Mobile:n&&n.indexOf("dingtalk-win")>=0?(this.isH5Mobile=!1,!1):!(!n||!(n.includes("mPaaSClient")||n.includes("Nebula")||n.includes("DingTalk")))&&(this.isH5Mobile=!0,this.platformType=e?d.IOS:t?d.ANDROID:d.UNKNOW,!0)},e.prototype.registerEvent=function(e,t){var n=this;if("function"==typeof t)return this.getAppType()===s.PC?(this.generalEventCallbackStack[e]||(this.generalEventCallbackStack[e]=[]),this.generalEventCallbackStack[e].push(t),function(){var i=n.generalEventCallbackStack[e].findIndex((function(e){return e===t}));n.generalEventCallbackStack[e].splice(i,1)}):this.getAppType()===s.MOBILE?(document.addEventListener(e,t,!1),function(){document.removeEventListener(e,t)}):void 0;console.error("callback \u53c2\u6570\u5e94\u8be5\u4e3a\u51fd\u6570")},e.prototype.registerClientAPI=function(e,t){this.apiList[e]=t},e.prototype.registerAPI=function(e,t){var n=this.isMobile();if("object"===i(t)){var o=t,r=this.getAppType();this.registerClientAPI(e,o[r])}else this.registerClientAPI(e,t)},e.prototype.invokeMiniApp=function(e,t){return void 0===t&&(t={}),r(this,void 0,Promise,(function(){var n=this;return a(this,(function(i){return[2,new Promise((function(i,r){t=o({_apiName:e},t);var a=n.apiList[e],u=n.getContainerType();if(!a)return console.warn("API: "+e+"\uff0c\u672a\u6ce8\u518c"),r("API: "+e+"\uff0c\u672a\u6ce8\u518c");if(u===c.isMpaas){if("function"==typeof a)return void a.call(null,t,{context:my,resolve:i,reject:r,methodName:e});my.call(e,t,(function(e){n.handleBridgeResponse(e,i,r)}))}else if(u===c.isDingTalk){if("function"==typeof a)return void a.call(null,t,{context:dd.dtBridge,resolve:i,reject:r,methodName:e,containerType:u,appType:s.MINI_APP});dd.dtBridge({m:"taurus.common."+e,args:t,onSuccess:function e(t){n.handleBridgeResponse(t,i,r)},onFail:function e(t){n.handleBridgeResponse(t,i,r)}})}}))]}))}))},e.prototype.invokeMobile=function(e,t,n){return void 0===t&&(t={}),r(this,void 0,Promise,(function(){var i=this;return a(this,(function(r){return[2,new Promise((function(r,a){t=o({_apiName:e},t);var u=i.apiList[e],l=i.getContainerType();if(!u)return console.warn("API: "+e+"\uff0c\u672a\u6ce8\u518c"),a("API: "+e+"\uff0c\u672a\u6ce8\u518c");if(l===c.isDingTalk){if(i.platformType===d.IOS){var f=Object.assign({},t);if(!0===f.watch&&"undefined"!=typeof WebViewJavascriptBridge&&WebViewJavascriptBridge.registerHandler((null==n?void 0:n.dingTalkAPIName)?null==n?void 0:n.dingTalkAPIName:"taurus.common."+e,(function(e,n){"function"==typeof t.onSuccess&&t.onSuccess.call(null,e),n&&n({errorCode:"0",errorMessage:"success"})})),"function"==typeof u)return void u.call(null,t,{context:window.WebViewJavascriptBridge,resolve:r,reject:a,methodName:e,containerType:l,appType:s.MOBILE,platformType:d.IOS,watch:f.watch});void 0!==window.WebViewJavascriptBridge&&window.WebViewJavascriptBridge.callHandler("taurus.common."+e,Object.assign({},f),(function(e){!f.watch&&i.handleBridgeResponse(e||{},r,a)}))}else if(i.platformType===d.ANDROID){var p=e.split("."),v=p.pop()||"",_=p.join(".")||"taurus.common",h=function e(t){i.handleBridgeResponse(t,r,a)},m=function e(t){i.handleBridgeResponse(t,r,a)};if("function"==typeof u)return void u.call(null,t,{context:window.WebViewJavascriptBridgeAndroid,resolve:r,reject:a,methodName:e,containerType:l,appType:s.MOBILE,platformType:d.ANDROID});"function"==typeof window.WebViewJavascriptBridgeAndroid&&window.WebViewJavascriptBridgeAndroid(h,m,_,v,t)}}else if(l===c.isMpaas){if("function"==typeof u)return void u.call(null,t,{context:AlipayJSBridge,resolve:r,reject:a,methodName:e});AlipayJSBridge.call(e,t,(function(e){i.handleBridgeResponse(e,r,a)}))}}))]}))}))},e.prototype.findFitMsgId=function(e){var t,n;return(null===(n=null===(t=window.dingtalk)||void 0===t?void 0:t.callbackStack)||void 0===n?void 0:n[e])?this.findFitMsgId(e+1):e},e.prototype.invokePC=function(e,t,n){return void 0===t&&(t={}),void 0===n&&(n={msgId:1}),r(this,void 0,Promise,(function(){var i=this;return a(this,(function(r){return[2,new Promise((function(r,a){try{t=o({_apiName:e},t);var u=i.findFitMsgId(Date.now()),l=n.pcClientAPIName||e,s=function e(t){var n=t;return n.body?r(n.body):r(n)};if(n.msgId=u,!window.dingtalk)return Promise.reject(new Error("\u8bf7\u5728\u9489\u9489\u5bb9\u5668\u5185\u4f7f\u7528 JSAPI"));i.apiList[e]?i.apiList[e].call(null,t,n):(console.info("invoke bridge api:",l,u,t),window.dingtalk.platform.invokeAPI(u,l,t)),window.dingtalk&&window.dingtalk.isRegister&&!window.dingtalk.callbackStack&&(window.dingtalk.callbackStack={}),window.dingtalk.callbackStack[""+u]=s}catch(e){a(e)}}))]}))}))},e.prototype.handleBridgeResponse=function(e,t,n){e&&e.errorCode?e.errorCode===l.SUCCESS?t(e.result):(console.warn("API \u8c03\u7528\u5931\u8d25",e),n(e)):e&&"false"===e.success?n(e):t(e)},e.prototype.invoke=function(e,t,n){return void 0===t&&(t={}),r(this,void 0,Promise,(function(){var i;return a(this,(function(o){return(i=this.getAppType())===s.MOBILE?this.isReady?[2,this.invokeMobile(e,t,n)]:[2,Promise.reject("\u9519\u8bef\uff1a\u8bf7\u5728 dd.ready() \u56de\u8c03\u4e2d\u4f7f\u7528 JSAPI\uff0c\u5f53\u524d\u8c03\u7528\u51fd\u6570\uff1a"+e)]:i===s.PC?[2,this.invokePC(e,t,n)]:i===s.MINI_APP?[2,this.invokeMiniApp(e,t)]:[2,Promise.reject("\u9519\u8bef\uff1a\u672a\u5728\u9489\u9489\u8fd0\u884c\u73af\u5883\u4e0b\u8c03\u7528\u8be5 API\uff0c\u65e0\u6548\uff0c\u8bf7\u68c0\u67e5\u8fd0\u884c\u73af\u5883")]}))}))},e.prototype.existEventListener=function(e){return!!this.continuousCallbackStack[e]},e.prototype.registerContinuesEvent=function(e,t){this.continuousCallbackStack[e]=t},e.prototype.removeContinuesEvent=function(e){this.existEventListener(e)&&(this.continuousCallbackStack[e](),delete this.continuousCallbackStack[e])},e}();u.isMiniApp()||(window._invoker=window._invoker||new v),t.default=u.isMiniApp()?new v:window._invoker},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(){return o.default.invoke("version")}o.default.registerAPI("version",{mini:!0,mobile:!0}),t.default=r},function(e,t,n){"use strict";function i(e){return(i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function o(e){return"undefined"===e}function r(){return!o("undefined"==typeof my?"undefined":i(my))&&null!==my&&!o(i(my.alert))}Object.defineProperty(t,"__esModule",{value:!0}),t.isMiniApp=t.isUndef=void 0,t.isUndef=o,t.isMiniApp=r},function(e,t,n){"use strict";var i=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n),Object.defineProperty(e,i,{enumerable:!0,get:function e(){return t[n]}})}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),r=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.hasOwnProperty.call(e,n)&&i(t,e,n);return o(t,e),t},a=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var u=r(n(0)),l=n(10),s=a(n(11)),d=a(n(12)),f=a(n(13)),c=a(n(14)),p=a(n(15)),v=a(n(16)),_=a(n(17)),h=a(n(18)),m=a(n(19)),g=a(n(20)),b=a(n(21)),P=a(n(22)),O=a(n(23)),y=a(n(24)),I=a(n(25)),M=a(n(26)),A=a(n(27)),T=a(n(28)),E=a(n(29)),k=a(n(30)),w=a(n(31)),S=a(n(32)),R=a(n(33)),j=a(n(34)),D=a(n(35)),C=a(n(36)),N=a(n(37)),B=a(n(38)),L=a(n(39)),F=a(n(40)),U=a(n(41)),x=a(n(42)),V=a(n(43)),W=a(n(44)),H=a(n(45)),Y=a(n(46)),G=a(n(47)),z=a(n(48)),K=a(n(49)),J=a(n(5)),q=a(n(50)),Q=a(n(51)),Z=a(n(52)),X=a(n(53)),$=a(n(54)),ee=a(n(55)),te=a(n(56)),ne=a(n(57)),ie=a(n(58)),oe=a(n(59)),re=a(n(60)),ae=a(n(61)),ue=a(n(62)),le=a(n(63)),se=a(n(8)),de=a(n(64)),fe=a(n(66)),ce=a(n(67)),pe=a(n(68)),ve=a(n(69)),_e=a(n(70)),he=a(n(71)),me=a(n(3)),ge=a(n(72)),be=a(n(73)),Pe=a(n(74)),Oe=a(n(75)),ye=a(n(76)),Ie=a(n(77)),Me=a(n(78)),Ae=a(n(79)),Te=a(n(80)),Ee=a(n(81)),ke=a(n(82)),we=a(n(83)),Se=a(n(84)),Re=a(n(85)),je=a(n(86)),De=a(n(87)),Ce=a(n(88)),Ne=a(n(89)),Be=a(n(90)),Le=a(n(91)),Fe=a(n(92)),Ue=a(n(93)),xe=a(n(94)),Ve=a(n(95)),We=a(n(96)),He=a(n(97)),Ye=a(n(98)),Ge=a(n(99)),ze=a(n(100)),Ke=a(n(101)),Je=a(n(102)),qe=a(n(103)),Qe=a(n(104)),Ze=a(n(105)),Xe=a(n(106)),$e=a(n(107)),et=a(n(108)),tt=a(n(109)),nt=a(n(110)),it=a(n(111)),ot=a(n(112)),rt=a(n(113)),at=a(n(114)),ut=a(n(115)),lt=a(n(116)),st=a(n(117)),dt=a(n(118)),ft=a(n(119)),ct=a(n(120)),pt=a(n(121)),vt=a(n(122)),_t=a(n(123)),ht=a(n(124)),mt=a(n(125)),gt=a(n(126)),bt=a(n(127)),Pt=a(n(128)),Ot=a(n(129)),yt=a(n(130)),It=a(n(131)),Mt=a(n(132)),At=a(n(133)),Tt=a(n(134)),Et=a(n(135)),kt=a(n(136)),wt=a(n(137)),St=a(n(139)),Rt=a(n(140)),jt=a(n(141)),Dt=a(n(142)),Ct=a(n(143)),Nt=a(n(144)),Bt=a(n(145)),Lt=a(n(146)),Ft=a(n(147)),Ut=a(n(148)),xt=a(n(149)),Vt=a(n(150)),Wt=a(n(151)),Ht=a(n(152)),Yt=a(n(153)),Gt=a(n(154)),zt=a(n(155)),Kt=a(n(156)),Jt=a(n(157)),qt=a(n(158)),Qt=a(n(159)),Zt=a(n(1)),Xt=a(n(160)),$t={alert:s.default,authConfig:d.default,callPhone:f.default,canIUse:c.default,checkVPNAppInstalled:p.default,checkVPNAppOnline:v.default,chooseContact:_.default,chooseContactWithComplexPicker:h.default,chooseDateRangeWithCalendar:m.default,chooseDayWithCalendar:g.default,chooseDepartments:b.default,chooseFile:P.default,chooseHalfDayWithCalendar:O.default,chooseImage:y.default,chooseLocalImage:I.default,chooseSpaceDir:M.default,chooseTimeWithCalendar:A.default,chooseVideo:T.default,closePage:E.default,confirm:k.default,copyToClipboard:w.default,createChatGroup:S.default,createDing:R.default,createDingV2:j.default,createVideoMeeting:D.default,dealWithBackAction:C.default,disablePullToRefresh:N.default,disableWebviewBounce:B.default,downloadAudio:L.default,downloadFile:F.default,enablePullToRefresh:U.default,enableVpn:x.default,enableWebviewBounce:V.default,exclusiveInvoke:W.default,faceComparison:H.default,faceRecognition:Y.default,getAppInstallStatus:G.default,getAuthCode:z.default,getConfig:K.default,getContainerType:J.default,getDeviceId:q.default,getFromClipboard:Q.default,getGeolocation:Z.default,getGeolocationStatus:X.default,getHotspotInfo:$.default,getLanguageSetting:ee.default,getLoginUser:te.default,getNetworkType:ne.default,getPhoneInfo:ie.default,getStorageItem:oe.default,getTraceStatus:re.default,getUUID:ae.default,getUserAgent:ue.default,getWaterMark:le.default,getWaterMarkConfigV2:se.default,getWaterMarkV2:de.default,getWifiStatus:fe.default,getWorkbenchContext:ce.default,goBack:pe.default,hideLoading:ve.default,hideOptionMenu:_e.default,hideTitleBar:he.default,index:me.default,isDownloadFileExist:ge.default,locateOnMap:be.default,on:Pe.default,onAudioPlayEnd:Oe.default,onRecordAudioEnd:ye.default,openApp:Ie.default,openBrowser:Me.default,openChat:Ae.default,openDownloadFile:Te.default,openLink:Ee.default,openPage:ke.default,openSchemeUrl:we.default,openSlidePanel:Se.default,openWatermarkCamera:Re.default,pauseAudio:je.default,pickChat:De.default,pickChatByCorpId:Ce.default,playAudio:Ne.default,previewDoc:Be.default,previewImage:Le.default,printFile:Fe.default,printNativeLog:Ue.default,prompt:xe.default,readImageToBase64:Ve.default,ready:We.default,reduceImageSize:He.default,removeStorageItem:Ye.default,replacePage:Ge.default,resetView:ze.default,resumeAudio:Ke.default,rotateView:Je.default,scan:qe.default,searchOnMap:Qe.default,sendOutData:Ze.default,setNavIcon:Xe.default,setNavLeftText:$e.default,setOptionMenu:et.default,setStorageItem:tt.default,setTitle:nt.default,shareFileToMessage:it.default,shareImageToMessage:ot.default,shareToMessage:rt.default,shootVideo:at.default,showActionSheet:ut.default,showCallMenu:lt.default,showDatePicker:st.default,showDateTimePicker:dt.default,showExtendModal:ft.default,showHomeBottomTab:ct.default,showLoading:pt.default,showModal:vt.default,showMultiSelect:_t.default,showOnMap:ht.default,showOptionMenu:mt.default,showPlainInputUponKeyboard:gt.default,showQuickCallMenu:bt.default,showSelect:Pt.default,showSignature:Ot.default,showSocialShare:yt.default,showTimePicker:It.default,showTitleBar:Mt.default,startFaceRecognition:At.default,startGeolocation:Tt.default,startListenNetworkStatus:Et.default,startRecordAudio:kt.default,startRequest:wt.default,startTraceReport:St.default,startVPNApp:Rt.default,startWatchShake:jt.default,stopAudio:Dt.default,stopGeolocation:Ct.default,stopListenNetworkStatus:Nt.default,stopPullToRefresh:Bt.default,stopRecordAudio:Lt.default,stopTraceReport:Ft.default,stopVPNApp:Ut.default,stopWatchShake:xt.default,subscribe:Vt.default,takePhoto:Wt.default,toast:Ht.default,unlockWithSecurityVerification:Yt.default,unsubscribe:Gt.default,uploadFile:zt.default,uploadFileByType:Kt.default,uploadLocalFile:Jt.default,uploadRemoteFileToDisk:qt.default,ut:Qt.default,version:Zt.default,vibrate:Xt.default};u.default.getAppType()===u.API_INVOKER_TYPE.MINI_APP?$t=new Proxy($t,{get:function e(t,n,i){return n in $t?Reflect.get(t,n,i):l.c2p(Reflect.get(my,n,i),n)}}):(Object.defineProperty(window,"dd",{value:$t}),Object.defineProperty(window,"gdt",{value:$t})),t.default=$t},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=1,o=-1,r=0;function a(e,t){return"number"!=typeof e&&(e=0),"number"!=typeof t&&(t=0),e>t?1:e<t?-1:0}function u(e,t){void 0===e&&(e=""),void 0===t&&(t="");var n=/^\d+(\.\d+){2,3}$/;if(!n.test(e)||!n.test(t))throw new Error("\u8bf7\u4f20\u5165\u6b63\u786e\u7684\u7248\u672c\u53f7\u683c\u5f0f");for(var i=(""+e).split(".").map((function(e){return parseInt(e,10)})),o=(""+t).split(".").map((function(e){return parseInt(e,10)})),r=Math.max(i.length,o.length),u=0,l=0;l<r&&0===(u=a(i[l],o[l]));l++);return u}t.default=u},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0)),r=n(0);function a(){return o.default.getContainerType()}Object.defineProperty(t,"Container_Type_Enum",{enumerable:!0,get:function e(){return r.Container_Type_Enum}}),t.default=a},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0)),r,a,u;function l(e){return o.default.invoke("getWaterMarkConfig",e)}!function(e){e.off="0",e.on="1"}(r||(r={})),function(e){e[e.off=0]="off",e[e.on=1]="on"}(a||(a={})),function(e){e[e.name=1]="name",e[e.id=2]="id",e[e.custom=3]="custom"}(u||(u={})),o.default.registerAPI("getWaterMarkConfig",{pc:function e(t,n){window.dingtalk.platform.invokeAPI(n.msgId,"getWaterMarkConfig",t)},mini:!0,mobile:!0}),t.default=l},function(e,t,n){"use strict";n.r(t),n.d(t,"generateWatermark",(function(){return M}));var i=n(2),o=n.n(i);function r(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,i)}return n}function a(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?r(Object(n),!0).forEach((function(t){u(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):r(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function u(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function l(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function s(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}function d(e,t,n){return t&&s(e.prototype,t),n&&s(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function f(e){return(f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var c="h5Page",p,v,_=[c,"meetingDetail","docPreview"],h=!Object(i.isUndef)("undefined"==typeof my?"undefined":f(my))&&null!==my&&!Object(i.isUndef)(f(my.alert)),m;h&&(m=my.getSystemInfoSync());var g=h?m.platform:navigator.userAgent,b=h?m.screenWidth:window.screen.width,P=(h?m.pixelRatio:window.devicePixelRatio)||2,O=h?Promise.resolve(""):"",y=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};l(this,e),this.options=a({texts:[""],width:50,height:50,textRotate:-10,textColor:"#000000",textFont:"PingFangSC-Regular,system-ui,sans-serif",fontStyle:"normal",opacity:90,canvas:[],fontSize:14},t),this.options.width*=this.options.fontSize/12,this.options.height*=this.options.fontSize/12,this.options.deg=this.options.textRotate*Math.PI/180,this.options.cosDeg=Math.cos(this.options.deg),this.options.absSinDeg=Math.abs(Math.sin(this.options.deg))}return d(e,[{key:"init",value:function e(){var t=this,n=null,i=null;h?i=my.createCanvasContext("canvasBg"):(n=this.createCanvas(),i=n.getContext("2d")),this.calcTextSize();var o=this.options,r=o.allItemsWidth,a=o.drawItems,u=o.height,l=o.containerComp,s=Math.ceil(b/r),d=new Array(s).fill(a).reduce((function(e,t){return e.concat(t)}),[]),f=function e(){t.setCanvasStyle(i),t.drawText(i,d),i.translate(0,u),t.drawText(i,d.reverse(),!0)};if(h)return new Promise((function(e){l.setState({width:r*s,height:2*u},(function(){setTimeout((function(){f(),i.draw(),e(i.toDataURL("image/png"))}),0)}))}));n.width=r*s,n.height=2*u,n.style.display="none",f();var c=n.toDataURL("image/png");return this.destroy(),c}},{key:"calcTextSize",value:function e(){var t=0,n=0,i=this.options;i.drawItems=[].map.call(i.texts,(function(e){var o,r;if(h){for(var a=0,u=0;u<e.length;u+=1)a+=/[\uff00-\uffff]/.test(e[u])?1:.5;o=1.1*i.fontSize*a,r=1.2*i.fontSize}else{var l,s=function e(t){var n=document.createElement("div");return n.innerHTML=t.trim(),n.firstChild}('<span style="font:'.concat(i.fontSize,"px ").concat(i.textFont,';visibility:hidden;">').concat(e,"</span>"));document.body.appendChild(s),o=s.offsetWidth,r=s.offsetHeight,document.body.removeChild(s)}return t=Math.max(t,o),i.fontHeight||(i.fontHeight=r),n+=Math.ceil(i.cosDeg*(i.width<o?o:i.width)),{txt:e,width:o,height:r}})),t>i.width&&(i.width=t);var o=t*i.absSinDeg+i.fontHeight*i.cosDeg;o>i.height&&(i.height=o),i.maxItemWidth=t,i.allItemsWidth=n}},{key:"setCanvasStyle",value:function e(t){var n=this.options,i=n.deg,o=n.absSinDeg,r=n.height,a=n.fontHeight,u=n.fontStyle,l=n.fontSize,s=n.textFont,d=n.textColor,f=n.opacity;t.rotate(i);var c=o*(r-a);t.translate(-c,0),t.font="".concat(u," ").concat(l,"px ").concat(s),t.fillStyle=d,t.textAlign="left",t.textBaseline="bottom",t.globalAlpha=f}},{key:"drawText",value:function e(t,n){var i=arguments.length>2&&void 0!==arguments[2]&&arguments[2],o=this.options,r=o.maxItemWidth,a=o.width,u=o.height,l=o.deg,s=o.cosDeg,d=o.absSinDeg;n.forEach((function(e,n){var o=s*(r-e.width)/2,f=a*s*n,c=Math.abs(f*Math.tan(l))+u;t.fillText(e.txt,f+(i?s*(a-e.width)/2:o),c+(i?d*(a-e.width)/2:0))}))}},{key:"createCanvas",value:function e(){var t=document.createElement("canvas");return this.options.canvas.push(t),t}},{key:"destroy",value:function e(){this.options.canvas.forEach((function(e){e.remove(),e=null}))}}]),e}();function I(e,t){var n=JSON.parse(e),i=n.watermark||n;if(!i||"0"===String(i.watermarkStatus))return O;if(!Array.isArray(i.targetPages)||!i.targetPages.some((function(e){return e.name===t&&"1"===String(e.value)})))return O;var o=[];if(Array.isArray(i.contentType)){var r="";i.contentType.includes(1)&&(r+="".concat(i.userName," ")),i.contentType.includes(2)&&(r+=(i.account||"").slice(-4)),r&&o.push(r),i.contentType.includes(0)&&i.contentCustom&&o.push(i.contentCustom)}if(!o.length)return O;var a=/Android|Adr|SymbianOS|Windows\s*Phone|Mobile/.test(g),u=/iPhone|iPad|iPod|Mac\s*OS.*Mobile|iOS/.test(g),l="0"===String(i.watermarkShowDensity),s,d;u?l?(s=114,d=66):(s=86,d=45):a?l?(s=47*P,d=40*P):(s=25*P,d=25*P):l?(s=300,d=126):(s=194,d=106);var f,c={0:12,1:16,2:28},p;return new y({containerComp:this,texts:o,width:s,height:d,textRotate:-10,textColor:{0:"#FF0000",1:"#000000",2:"#0000FF"}[i.fontColor]||"#000000",textFont:"PingFangSC-Regular,system-ui,sans-serif",fontStyle:"0"===String(i.fontStyle)?"normal":"bold",opacity:(120-parseInt(i.fontDiaphaneity,10))/100,fontSize:c[i.fontSize]||16}).init()}function M(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:c;if(!_.includes(t))throw new Error("\u7b2c\u4e8c\u4e2a\u53ef\u9009\u53c2\u6570\uff0c\u4ec5\u80fd\u4e3a\u201ch5Page\u201d\u6216\u201cmeetingDetail\u201d");try{return I.call(this,JSON.stringify(e),t)}catch(e){throw e}}},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.EnableEnum=void 0;var o=i(n(0)),r;function a(e){return o.default.invoke("getWaterMarkConfigV2",e)}!function(e){e[e.ENABLE=1]="ENABLE",e[e.DISABLE=0]="DISABLE"}(r=t.EnableEnum||(t.EnableEnum={})),o.default.registerAPI("getWaterMarkConfigV2",{mobile:!0,mini:!0,pc:!0}),a.version={android:"2.8.0",ios:"2.8.0",pc:"2.8.0"},t.default=a},function(e,t,n){e.exports=n(3)},function(e,t,n){"use strict";var i=this&&this.__assign||function(){return(i=Object.assign||function(e){for(var t,n=1,i=arguments.length;n<i;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function o(e){return function(t){var n=t.success,i=t.fail,o=t.complete;e(t).then((function(e){n&&n(e)})).catch((function(e){i&&i(e)})).finally((function(){o&&o()}))}}function r(e,t){if(e)return function(n){return"function"==typeof n||t.includes("Sync")||t.startsWith("create")?e(n):new Promise((function(t,o){e(i(i({},n),{success:function e(n){t(n)},fail:function e(t){o(t)}}))}))}}Object.defineProperty(t,"__esModule",{value:!0}),t.c2p=t.p2c=void 0,t.p2c=o,t.c2p=r},function(e,t,n){"use strict";var i=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n),Object.defineProperty(e,i,{enumerable:!0,get:function e(){return t[n]}})}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),r=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.hasOwnProperty.call(e,n)&&i(t,e,n);return o(t,e),t};Object.defineProperty(t,"__esModule",{value:!0});var a=r(n(0));function u(e,t){var n=t.resolve,i=t.reject,o=t.context,r=t.containerType,u=t.appType,l=t.platformType;if(r){var s=function e(t){a.default.handleBridgeResponse(t,n,i)},d=function e(t){a.default.handleBridgeResponse(t,n,i)};u===a.API_INVOKER_TYPE.MINI_APP?o&&o({m:"taurus.common.alert",args:e,onSuccess:s,onFail:d}):l===a.PLATFORM_TYPE_ENUM.ANDROID?o&&o(s,d,"taurus.common","alert",e):l===a.PLATFORM_TYPE_ENUM.IOS&&o.callHandler("taurus.common.alert",Object.assign({},e),(function(e){n(e)}))}else o&&o.call("alert",e,(function(){n()}))}function l(e){return a.default.invoke("alert",e)}a.default.registerAPI("alert",{mini:u,mobile:u}),l.version={android:"1.3.2",ios:"1.3.2"},t.default=l},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(e){return o.default.invoke("authConfig",e)}o.default.registerAPI("authConfig",{mini:!0,mobile:!0}),r.version={android:"1.2.0",ios:"1.2.0",pc:"1.2.0"},t.default=r},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(e){return o.default.invoke("callPhone",e)}o.default.registerAPI("callPhone",{mini:!0,mobile:!0}),r.version={android:"1.1.0",ios:"1.1.0"},t.default=r},function(e,t,n){"use strict";var i=this&&this.__awaiter||function(e,t,n,i){function o(e){return e instanceof n?e:new n((function(t){t(e)}))}return new(n||(n=Promise))((function(n,r){function a(e){try{l(i.next(e))}catch(e){r(e)}}function u(e){try{l(i.throw(e))}catch(e){r(e)}}function l(e){e.done?n(e.value):o(e.value).then(a,u)}l((i=i.apply(e,t||[])).next())}))},o=this&&this.__generator||function(e,t){var n={label:0,sent:function e(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]},i,o,r,a;return a={next:u(0),throw:u(1),return:u(2)},"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function u(e){return function(t){return l([e,t])}}function l(a){if(i)throw new TypeError("Generator is already executing.");for(;n;)try{if(i=1,o&&(r=2&a[0]?o.return:a[0]?o.throw||((r=o.return)&&r.call(o),0):o.next)&&!(r=r.call(o,a[1])).done)return r;switch(o=0,r&&(a=[2&a[0],r.value]),a[0]){case 0:case 1:r=a;break;case 4:return n.label++,{value:a[1],done:!1};case 5:n.label++,o=a[1],a=[0];continue;case 7:a=n.ops.pop(),n.trys.pop();continue;default:if(!(r=n.trys,(r=r.length>0&&r[r.length-1])||6!==a[0]&&2!==a[0])){n=0;continue}if(3===a[0]&&(!r||a[1]>r[0]&&a[1]<r[3])){n.label=a[1];break}if(6===a[0]&&n.label<r[1]){n.label=r[1],r=a;break}if(r&&n.label<r[2]){n.label=r[2],n.ops.push(a);break}r[2]&&n.ops.pop(),n.trys.pop();continue}a=t.call(e,n)}catch(e){a=[6,e],o=0}finally{i=r=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}},r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var a=r(n(1)),u=r(n(4)),l=r(n(3)),s=navigator&&navigator.userAgent||"",d=function e(){return s.indexOf("Android")>-1||s.indexOf("Adr")>-1},f=function e(){return!!s.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/)},c=function e(){return/(windows)/i.test(navigator.userAgent)};function p(e){return i(this,void 0,Promise,(function(){var t,n,i;return o(this,(function(o){switch(o.label){case 0:return l.default[e]?[4,a.default()]:[2,!1];case 1:return t=o.sent().version,n=l.default[e].version,i=d()?"android":f()?"ios":c()?"pc":"unknown",[2,!(!n||!n[i])&&u.default(t,n[i])>0]}}))}))}t.default=p},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(){return o.default.invoke("checkVPNAppInstalled")}o.default.registerAPI("checkVPNAppInstalled",{mini:!0,mobile:!0}),r.version={android:"1.6.0",ios:"1.6.0"},t.default=r},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(){return o.default.invoke("checkVPNAppOnline")}o.default.registerAPI("checkVPNAppOnline",{mini:!0,mobile:!0}),r.version={android:"1.6.0",ios:"1.6.0"},t.default=r},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.VisibilityCodesEnum=t.PanelTypeEnum=t.SelectVersionEnum=void 0;var o=i(n(0)),r,a,u;function l(e){return o.default.invoke("chooseContact",e)}!function(e){e[e.DEFAULT=1]="DEFAULT",e[e.NEW=2]="NEW"}(r=t.SelectVersionEnum||(t.SelectVersionEnum={})),function(e){e[e.GLOBAL_ORG=1]="GLOBAL_ORG",e[e.FRIEND=2]="FRIEND",e[e.GROUP=4]="GROUP",e[e.RECOMMEND=5]="RECOMMEND",e[e.SPECIAL_ATTENTION=7]="SPECIAL_ATTENTION",e[e.LOAD_GROUP_PERSON=8]="LOAD_GROUP_PERSON",e[e.ORG=9]="ORG"}(a=t.PanelTypeEnum||(t.PanelTypeEnum={})),function(e){e.PHONE_HIDE="PHONE_HIDE",e.CHAT_INVALID="CHAT_INVALID",e.GROUP_CHAT_PULL_INVALID="GROUP_CHAT_PULL_INVALID",e.APP_DING_INVALID="APP_DING_INVALID",e.PHONE_DING_INVALID="PHONE_DING_INVALID",e.SMS_DING_INVALID="SMS_DING_INVALID",e.AUDIO_VIDEO_HIDE="AUDIO_VIDEO_HIDE"}(u=t.VisibilityCodesEnum||(t.VisibilityCodesEnum={})),o.default.registerAPI("chooseContact",{pc:function e(t,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.contact.choose",t)}}),l.version={pc:"1.1.0"},t.default=l},function(e,t,n){"use strict";var i=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n),Object.defineProperty(e,i,{enumerable:!0,get:function e(){return t[n]}})}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),r=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.hasOwnProperty.call(e,n)&&i(t,e,n);return o(t,e),t};Object.defineProperty(t,"__esModule",{value:!0});var a=r(n(0));function u(e,t){var n=t.resolve,i=t.reject,o=t.context,r=t.containerType,u=t.appType,l=t.platformType;if(r){var s=function e(t){a.default.handleBridgeResponse(t,n,i)},d=function e(t){a.default.handleBridgeResponse(t,n,i)};u===a.API_INVOKER_TYPE.MINI_APP?o&&o({m:"taurus.common.chooseContactWithComplexPicker",args:e,onSuccess:s,onFail:d}):l===a.PLATFORM_TYPE_ENUM.ANDROID?o&&o(s,d,"taurus.common","chooseContactWithComplexPicker",e):l===a.PLATFORM_TYPE_ENUM.IOS&&o.callHandler("taurus.common.chooseContactWithComplexPicker",Object.assign({},e),(function(e){a.default.handleBridgeResponse(e,n,i)}))}else o&&o.call("chooseContactWithComplexPicker",e,(function(t){t.error&&t.error.toString()===a.BRIDGE_ERROR_CODE.API_UNDEFINED?o.call("complexPicker",e,(function(e){a.default.handleBridgeResponse(e,n,i)})):a.default.handleBridgeResponse(t,n,i)}))}function l(e){return a.default.invoke("chooseContactWithComplexPicker",e)}a.default.registerAPI("chooseContactWithComplexPicker",{mini:u,mobile:u,pc:function e(t,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.contact.complexPicker",t)}}),l.version={android:"1.1.0",ios:"1.1.0",pc:"1.6.2"},t.default=l},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(e){return o.default.invoke("chooseDateRangeWithCalendar",e)}o.default.registerAPI("chooseDateRangeWithCalendar",{mini:!0,mobile:!0}),r.version={android:"1.3.10",ios:"1.3.10"},t.default=r},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(e){return o.default.invoke("chooseDayWithCalendar",e)}o.default.registerAPI("chooseDayWithCalendar",{mini:!0,mobile:!0}),r.version={android:"1.3.10",ios:"1.3.10"},t.default=r},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(e){return o.default.invoke("chooseDepartments",e)}o.default.registerAPI("chooseDepartments",{mini:!0,mobile:!0,pc:function e(t,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.contact.departmentsPicker",t)}}),r.version={android:"1.1.0",ios:"1.1.0",pc:"1.6.2"},t.default=r},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(e){return o.default.invoke("chooseFile",e)}o.default.registerAPI("chooseFile",{mini:!0,mobile:!0,pc:function e(t,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.util.chooseFile",t)}}),r.version={android:"1.3.2",ios:"1.3.2",pc:"1.3.6"},t.default=r},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(e){return o.default.invoke("chooseHalfDayWithCalendar",e)}o.default.registerAPI("chooseHalfDayWithCalendar",{mini:!0,mobile:!0}),r.version={android:"1.3.10",ios:"1.3.10"},t.default=r},function(e,t,n){"use strict";var i=this&&this.__assign||function(){return(i=Object.assign||function(e){for(var t,n=1,i=arguments.length;n<i;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},o=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.ImageType=void 0;var r=o(n(0)),a;function u(e){return r.default.invoke("dgChooseImage",i(i({},e),{_apiName:"chooseImage"}))}!function(e){e[e.image=0]="image",e[e.video=1]="video"}(a=t.ImageType||(t.ImageType={})),r.default.registerAPI("dgChooseImage",{mini:!0,mobile:!0,pc:function e(t,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.util.dgChooseImage",t)}}),u.version={android:"1.3.2",ios:"1.3.2",pc:"1.3.6"},t.default=u},function(e,t,n){"use strict";var i=this&&this.__assign||function(){return(i=Object.assign||function(e){for(var t,n=1,i=arguments.length;n<i;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},o=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}},r;function a(e){return new Promise((function(t,n){my.chooseImage(i(i({},e),{success:function e(n){t(n)},fail:function e(t){n(t)}}))}))}Object.defineProperty(t,"__esModule",{value:!0}),o(n(0)).default.registerAPI("chooseImage",{mini:!0}),a.version={android:"1.6.2",ios:"1.6.2"},t.default=a},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(){return o.default.invoke("chooseSpaceDir")}o.default.registerAPI("chooseSpaceDir",{mini:!0,mobile:!0,pc:function e(t,n){void 0===t&&(t={}),window.dingtalk.platform.invokeAPI(n.msgId,"biz.util.chooseSpaceDir",t)}}),r.version={android:"1.6.0",ios:"1.6.0",pc:"2.6.0"},t.default=r},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(e){return o.default.invoke("chooseTimeWithCalendar",e)}o.default.registerAPI("chooseTimeWithCalendar",{mini:!0,mobile:!0}),r.version={android:"1.3.10",ios:"1.3.10"},t.default=r},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(e){return o.default.invoke("chooseVideo",e)}o.default.registerAPI("chooseVideo",{mini:!0,mobile:!0}),r.version={android:"1.6.2",ios:"1.6.2"},t.default=r},function(e,t,n){"use strict";var i=this&&this.__assign||function(){return(i=Object.assign||function(e){for(var t,n=1,i=arguments.length;n<i;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},o=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n),Object.defineProperty(e,i,{enumerable:!0,get:function e(){return t[n]}})}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),r=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),a=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.hasOwnProperty.call(e,n)&&o(t,e,n);return r(t,e),t};Object.defineProperty(t,"__esModule",{value:!0});var u=a(n(0));function l(e,t){var n=t.resolve,i=t.reject,o=t.context,r=t.containerType,a=t.appType,l=t.platformType;if(r){var s=function e(t){u.default.handleBridgeResponse(t,n,i)},d=function e(t){u.default.handleBridgeResponse(t,n,i)};a===u.API_INVOKER_TYPE.MINI_APP?o&&o({m:"biz.navigation.close",args:e,onSuccess:s,onFail:d}):l===u.PLATFORM_TYPE_ENUM.ANDROID?o&&o(s,d,"biz.navigation","close",e):l===u.PLATFORM_TYPE_ENUM.IOS&&o.callHandler("biz.navigation.close",Object.assign({},e),(function(e){u.default.handleBridgeResponse(e,n,i)}))}else o&&o.call("popWindow",e,(function(e){u.default.handleBridgeResponse(e,n,i)}))}function s(e){return u.default.invoke("closePage",i(i({},e),{_apiName:"closePage"}))}u.default.registerAPI("closePage",{mini:l,mobile:l,pc:function e(t,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.navigation.quit",t)}}),s.version={android:"1.2.0",ios:"1.2.0",pc:"1.2.0"},t.default=s},function(e,t,n){"use strict";var i=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n),Object.defineProperty(e,i,{enumerable:!0,get:function e(){return t[n]}})}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),r=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.hasOwnProperty.call(e,n)&&i(t,e,n);return o(t,e),t};Object.defineProperty(t,"__esModule",{value:!0});var a=r(n(0));function u(e){return a.default.invoke("confirm",e)}a.default.registerAPI("confirm",{mini:function e(t,n){var i=n.resolve,o=n.reject,r=n.context,u=n.containerType,l={message:t.message,title:t.title,okButton:t.buttonLabels[0],cancelButton:t.buttonLabels[1]};u===a.Container_Type_Enum.isDingTalk?r({m:"taurus.common.confirm",args:l,onSuccess:function e(t){var n={errorCode:a.BRIDGE_ERROR_CODE.SUCCESS,result:{buttonIndex:t.ok?0:1}};a.default.handleBridgeResponse(n,i,o)},onFail:function e(t){a.default.handleBridgeResponse(t,i,o)}}):r&&r.call("confirm",l,(function(e){var t={errorCode:a.BRIDGE_ERROR_CODE.SUCCESS,result:{buttonIndex:e.ok?0:1}};a.default.handleBridgeResponse(t,i,o)}))},mobile:function e(t,n){var i=n.resolve,o=n.reject,r=n.context,u=n.containerType,l=n.platformType,s={message:t.message,title:t.title,okButton:t.buttonLabels[0],cancelButton:t.buttonLabels[1]};if(u){var d=function e(t){var n={errorCode:a.BRIDGE_ERROR_CODE.SUCCESS,result:{buttonIndex:t.ok?0:1}};a.default.handleBridgeResponse(n,i,o)},f=function e(t){a.default.handleBridgeResponse(t,i,o)};l===a.PLATFORM_TYPE_ENUM.ANDROID?r&&r(d,f,"taurus.common","confirm",s):l===a.PLATFORM_TYPE_ENUM.IOS&&r.callHandler("taurus.common.confirm",Object.assign({},s),(function(e){a.default.handleBridgeResponse(e,i,o)}))}else r&&r.call("confirm",s,(function(e){var t={errorCode:a.BRIDGE_ERROR_CODE.SUCCESS,result:{buttonIndex:e.ok?0:1}};a.default.handleBridgeResponse(t,i,o)}))},pc:function e(t,n){window.dingtalk.platform.invokeAPI(n.msgId,"device.notification.confirm",t)}}),u.version={android:"1.3.2",ios:"1.3.2",pc:"1.3.2"},t.default=u},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(e){return o.default.invoke("copyToClipboard",e)}o.default.registerAPI("copyToClipboard",{mini:!0,mobile:!0}),r.version={android:"1.3.2",ios:"1.3.2"},t.default=r},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(e){return o.default.invoke("createChatGroup",e)}o.default.registerAPI("createChatGroup",{mini:!0,mobile:!0}),r.version={android:"1.3.0",ios:"1.3.0",pc:"1.3.0"},t.default=r},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(e){return o.default.invoke("createDing",e)}o.default.registerAPI("createDing",{mini:!0,mobile:!0,pc:function e(t,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.ding.create",t)}}),r.version={android:"1.3.9",ios:"1.3.9",pc:"1.3.9"},t.default=r},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(e){return o.default.invoke("createDingV2",e)}o.default.registerAPI("createDingV2",{mini:!0,mobile:!0,pc:function e(t,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.ding.createV2",t)}}),r.version={android:"2.7.0",ios:"2.7.0",pc:"2.7.0"},t.default=r},function(e,t,n){"use strict";var i=this&&this.__assign||function(){return(i=Object.assign||function(e){for(var t,n=1,i=arguments.length;n<i;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},o=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var r=o(n(0));function a(e){return r.default.invoke("createVideoMeeting",e)}r.default.registerAPI("createVideoMeeting",{mini:!0,mobile:!0,pc:function e(t,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.meeting.create",i({isVideoConference:!0},t))}}),a.version={android:"1.3.1.1",ios:"1.3.1.1",pc:"1.9.4"},t.default=a},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(e){return o.default.invoke("dealWithBackAction",e)}o.default.registerAPI("dealWithBackAction",{mobile:!0}),r.version={android:"1.2.0.10"},t.default=r},function(e,t,n){"use strict";var i=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n),Object.defineProperty(e,i,{enumerable:!0,get:function e(){return t[n]}})}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),r=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.hasOwnProperty.call(e,n)&&i(t,e,n);return o(t,e),t};Object.defineProperty(t,"__esModule",{value:!0});var a=r(n(0));function u(){return a.default.invoke("disablePullToRefresh",{_apiName:"disablePullToRefresh"})}a.default.registerAPI("disablePullToRefresh",{mobile:function e(t,n){var i=n.resolve,o=n.reject,r=n.context,u=n.containerType,l=n.platformType;if(u){var s=function e(){i()},d=function e(){o()};l===a.PLATFORM_TYPE_ENUM.ANDROID?r&&r(s,d,"ui.pullToRefresh","disable",{}):l===a.PLATFORM_TYPE_ENUM.IOS&&r.callHandler("ui.pullToRefresh.disable",Object.assign({},{}),(function(e){i(e)}))}else r&&r.call("pullRefresh",{pullRefresh:!1},(function(){i()}))}}),u.version={android:"1.3.0",ios:"1.3.0"},t.default=u},function(e,t,n){"use strict";var i=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n),Object.defineProperty(e,i,{enumerable:!0,get:function e(){return t[n]}})}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),r=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.hasOwnProperty.call(e,n)&&i(t,e,n);return o(t,e),t};Object.defineProperty(t,"__esModule",{value:!0});var a=r(n(0));function u(){return a.default.invoke("disableWebviewBounce",{_apiName:"disableWebviewBounce"})}a.default.registerAPI("disableWebviewBounce",{mobile:function e(t,n){var i=n.resolve,o=n.reject,r=n.context,u=n.containerType,l=n.platformType;if(u){var s=function e(){i()},d=function e(){o()};l===a.PLATFORM_TYPE_ENUM.ANDROID?r&&r(s,d,"ui.webViewBounce","disable",{}):l===a.PLATFORM_TYPE_ENUM.IOS&&r.callHandler("ui.webViewBounce.disable",Object.assign({},{}),(function(e){i(e)}))}else r&&r.call("bounce",{enable:!1},(function(e){i(e)}))}}),u.version={ios:"1.3.0"},t.default=u},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(e){return o.default.invoke("downloadAudio",e)}o.default.registerAPI("downloadAudio",{mini:!0,mobile:!0}),r.version={android:"1.3.0",ios:"1.3.0"},t.default=r},function(e,t,n){"use strict";var i=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n),Object.defineProperty(e,i,{enumerable:!0,get:function e(){return t[n]}})}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),r=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.hasOwnProperty.call(e,n)&&i(t,e,n);return o(t,e),t};Object.defineProperty(t,"__esModule",{value:!0});var a=r(n(0)),u=1;function l(e){return a.default.invoke("downloadFile",e)}a.default.registerAPI("downloadFile",{mini:function e(t,n){var i=n.resolve,o=n.reject,r=n.containerType,u=n.context,l,s;r===a.Container_Type_Enum.isDingTalk?u&&u({m:"taurus.common.downloadFile",args:t,onSuccess:function e(t){a.default.handleBridgeResponse(t,i,o)},onFail:function e(t){a.default.handleBridgeResponse(t,i,o)}}):u&&u.call("downloadFile",t,(function(e){e.error?o(e):i(e)}))},pc:function e(t,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.util.downloadFile",t),a.default.registerContinuesEvent(n.msgId,(function(e,i){"im.fileTask.addNewTask"===e&&(a.default.removeContinuesEvent(n.msgId),a.default.registerContinuesEvent(i.taskId,(function(e,n){if("im.fileTask.updateTask"===e){var i=n.doneSize,o=n.fileName,r=n.filePath,u=n.fileSize,l=n.speed;t.onProgress({doneSize:i,fileName:o,filePath:r,fileSize:u,speed:l}),1===n.status&&a.default.removeContinuesEvent(n.taskId)}})))}))}}),l.version={pc:"1.3.5"},t.default=l},function(e,t,n){"use strict";var i=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n),Object.defineProperty(e,i,{enumerable:!0,get:function e(){return t[n]}})}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),r=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.hasOwnProperty.call(e,n)&&i(t,e,n);return o(t,e),t};Object.defineProperty(t,"__esModule",{value:!0});var a=r(n(0));function u(){return a.default.invoke("enablePullToRefresh",{_apiName:"enablePullToRefresh"})}a.default.registerAPI("enablePullToRefresh",{mobile:function e(t,n){var i=n.resolve,o=n.reject,r=n.context,u=n.containerType,l=n.platformType;if(u){var s=function e(){i()},d=function e(){o()};l===a.PLATFORM_TYPE_ENUM.ANDROID?r&&r(s,d,"ui.pullToRefresh","enable",{}):l===a.PLATFORM_TYPE_ENUM.IOS&&r.callHandler("ui.pullToRefresh.enable",Object.assign({},{}),(function(){i()}))}else r&&r.call("pullRefresh",{pullRefresh:!0},(function(){i()}))}}),u.version={android:"1.3.0",ios:"1.3.0"},t.default=u},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(){return o.default.invoke("enableVpn")}o.default.registerAPI("enableVpn",{mini:!0,mobile:!0}),r.version={android:"1.1.0",ios:"1.1.0"},t.default=r},function(e,t,n){"use strict";var i=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n),Object.defineProperty(e,i,{enumerable:!0,get:function e(){return t[n]}})}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),r=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.hasOwnProperty.call(e,n)&&i(t,e,n);return o(t,e),t};Object.defineProperty(t,"__esModule",{value:!0});var a=r(n(0));function u(){return a.default.invoke("enableWebviewBounce",{_apiName:"enableWebviewBounce"})}a.default.registerAPI("enableWebviewBounce",{mobile:function e(t,n){var i=n.resolve,o=n.reject,r=n.context,u=n.containerType,l=n.platformType;if(u){var s=function e(){i()},d=function e(){o()};l===a.PLATFORM_TYPE_ENUM.ANDROID?r&&r(s,d,"taurus.common","bounce",{enable:!0}):l===a.PLATFORM_TYPE_ENUM.IOS&&r.callHandler("taurus.common.bounce",Object.assign({},{enable:!0}),(function(e){i(e)}))}else r&&r.call("bounce",{enable:!0},(function(e){i(e)}))}}),u.version={ios:"1.3.0"},t.default=u},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(e){return o.default.invoke("exclusiveInvoke",e)}o.default.registerAPI("exclusiveInvoke",{mini:!0,mobile:!0}),r.version={ios:"1.9.5",android:"1.9.5"},t.default=r},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(e){return o.default.invoke("faceComparison",e)}o.default.registerAPI("faceComparison",{mobile:!0,mini:!0}),r.version={android:"2.4.0",ios:"2.4.0"},t.default=r},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.ImageTypeEnum=void 0;var o=i(n(0)),r;function a(e){return o.default.invoke("faceRecognition",e)}!function(e){e.PNG="png",e.JPG="jpg"}(r=t.ImageTypeEnum||(t.ImageTypeEnum={})),o.default.registerAPI("faceRecognition",{mobile:!0,mini:!0}),a.version={android:"2.4.0",ios:"2.4.0"},t.default=a},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(e){return o.default.invoke("getAppInstallStatus",e)}o.default.registerAPI("getAppInstallStatus",{mini:!0,mobile:!0}),r.version={android:"2.1.10",ios:"2.1.10"},t.default=r},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(e){return o.default.invoke("getAuthCode",e)}o.default.registerAPI("getAuthCode",{pc:function e(t,n){window.dingtalk.platform.invokeAPI(n.msgId,"runtime.permission.requestAuthCode",t)},mobile:!0,mini:!0}),r.version={android:"1.0.0",ios:"1.0.0",pc:"1.0.0"},t.default=r},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(){return o.default.invoke("getConfig",{})}o.default.registerAPI("getConfig",{mobile:!0,mini:!0,pc:function e(t,n){window.dingtalk.platform.invokeAPI(n.msgId,"getConfig",t)}}),r.version={android:"1.3.2",ios:"1.3.2",pc:"1.3.2"},t.default=r},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(){return o.default.invoke("getDeviceId",{})}o.default.registerAPI("getDeviceId",{mobile:!0,mini:!0}),r.version={android:"2.5.0",ios:"2.5.0"},t.default=r},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(){return o.default.invoke("getFromClipboard")}o.default.registerAPI("getFromClipboard",{mini:!0,mobile:!0,pc:function e(t,n){window.dingtalk.platform.invokeAPI(n.msgId,"util.clipboardData.getData",t)}}),r.version={android:"2.3.1",ios:"2.3.1",pc:"2.6.10"},t.default=r},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(e){return o.default.invoke("getGeolocation",e)}o.default.registerAPI("getGeolocation",{mini:!0,mobile:!0}),r.version={android:"1.2.0",ios:"1.2.0"},t.default=r},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(e){return o.default.invoke("getGeolocationStatus",e)}o.default.registerAPI("getGeolocationStatus",{mobile:!0,mini:!0}),r.version={android:"1.6.2",ios:"1.6.2"},t.default=r},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(){return o.default.invoke("getHotspotInfo")}o.default.registerAPI("getHotspotInfo",{mobile:!0,mini:!0}),r.version={android:"1.3.5",ios:"1.3.5"},t.default=r},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(){return o.default.invoke("getLanguageSetting")}o.default.registerAPI("getLanguageSetting",{mobile:!0,mini:!0,pc:function e(t,n){window.dingtalk.platform.invokeAPI(n.msgId,"getLanguageSetting",t)}}),r.version={android:"1.4.0",ios:"1.4.0",pc:"1.4.0"},t.default=r},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(){return o.default.invoke("getLoginUser")}o.default.registerAPI("getLoginUser",{mobile:!0,mini:!0}),r.version={android:"1.1.0",ios:"1.1.0"},t.default=r},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(){return o.default.invoke("getNetworkType")}o.default.registerAPI("getNetworkType",{mobile:!0,mini:!0}),r.version={android:"1.3.0",ios:"1.3.0"},t.default=r},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(){return o.default.invoke("getPhoneInfo")}o.default.registerAPI("getPhoneInfo",{mini:!0,mobile:!0}),r.version={android:"1.3.5",ios:"1.3.5"},t.default=r},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(e){return o.default.invoke("getStorageItem",e)}o.default.registerAPI("getStorageItem",{mobile:!0,mini:!0}),r.version={android:"1.3.1.1",ios:"1.3.1.1"},t.default=r},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(e){return o.default.invoke("getTraceStatus",e)}o.default.registerAPI("getTraceStatus",{mobile:!0}),r.version={android:"1.3.4",ios:"1.3.4"},t.default=r},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(){return o.default.invoke("getUUID")}o.default.registerAPI("getUUID",{mobile:!0,mini:!0}),r.version={android:"1.3.5",ios:"1.3.5"},t.default=r},function(e,t,n){"use strict";var i=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n),Object.defineProperty(e,i,{enumerable:!0,get:function e(){return t[n]}})}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),r=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.hasOwnProperty.call(e,n)&&i(t,e,n);return o(t,e),t};Object.defineProperty(t,"__esModule",{value:!0});var a=r(n(0)),u=/TaurusApp\((\S*)\/(\S*)\)/;function l(){if(window&&window.navigator){var e=window.navigator.userAgent;if(e){var t=e.match(u);return Promise.resolve({group:"TaurusApp",name:t[1],version:t[2]})}return Promise.reject("\u8c03\u7528\u9519\u8bef\uff1a\u65e0\u6cd5\u68c0\u6d4b\u5230\u5f53\u4e0b\u73af\u5883\u7684 userAgent\uff0c\u8bf7\u786e\u4fdd\u5728\u653f\u52a1\u9489\u9489\u5ba2\u6237\u7aef H5 \u5bb9\u5668\u4e0b\u8c03\u7528\u3002")}}function s(){var e=a.default.getAppType();return e===a.API_INVOKER_TYPE.PC||e===a.API_INVOKER_TYPE.MOBILE?l():e===a.API_INVOKER_TYPE.MINI_APP?a.default.invoke("getUserAgent",{}):void 0}a.default.registerAPI("getUserAgent",{mobile:!0,mini:!0,pc:!0}),s.version={android:"1.6.2",ios:"1.6.2",pc:"1.6.2"},t.default=s},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(6)),r=n(7);function a(e,t){return void 0===e&&(e=""),new Promise((function(n,i){o.default({pageInfo:e}).then((function(e){try{var o=r.generateWatermark(e,t);n(o)}catch(e){i(e)}}))}))}a.version={android:"1.1.0",ios:"1.1.0",pc:"1.1.0"},t.default=a},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(65)),r=i(n(6)),a=i(n(8)),u=n(7),l=i(n(1)),s=i(n(4));function d(e){return new Promise((function(t,n){l.default().then((function(i){var l=i.version,d;-1!==s.default(l,"2.8.0")?a.default({pageInfo:e}).then((function(i){try{var r=o.default(i,e);t(r)}catch(e){n(e)}})):r.default({pageInfo:e}).then((function(i){try{var o=u.generateWatermark(i,e);t(o)}catch(e){n(e)}}))}))}))}t.default=d},function(e,t,n){"use strict";var i,o;Object.defineProperty(t,"__esModule",{value:!0}),t.HorizontalTypeEnum=t.PageInfoEnum=t.EnableEnum=void 0,function(e){e[e.DISABLE=0]="DISABLE",e[e.ENABLE=1]="ENABLE"}(i=t.EnableEnum||(t.EnableEnum={})),function(e){e.IMSESSIONLIST="imSessionList",e.DOCPREVIEW="docPreview",e.H5PAGEOTHER="h5PageOther",e.MEETINGDETAIL="meetingDetail",e.H5PAGEBASIC="h5PageBasic",e.SELECTIONCOMPONENT="selectionComponent",e.CONTACTLIST="contactList",e.CONTACTDETAIL="contactDetail",e.CHAT="chat",e.SECRETCHAT="secretChat",e.CAMERA="camera"}(o=t.PageInfoEnum||(t.PageInfoEnum={}));var r="",a={1:"normal",2:"bold",3:"italic"},u,l;!function(e){e[e.LOOSE=0]="LOOSE",e[e.NORMAL=1]="NORMAL",e[e.DENSE=2]="DENSE"}(u||(u={})),function(e){e[e.RIGHT=0]="RIGHT",e[e.LEFT=1]="LEFT"}(l=t.HorizontalTypeEnum||(t.HorizontalTypeEnum={}));var s=749,d=326,f=200,c=16,p=1.3,v=function(){function e(e){var t={texts:"",width:50,height:50,tiltAngle:-15,fontColor:"#171A1D",textFont:"PingFangSC-Regular,system-ui,sans-serif",transparency:90,canvas:[],fontSize:13,tWidth:0,tHeight:0,deg:-15};this.options=Object.assign(t,e,{width:e.leftAndRightSpacing,height:e.upAndDownSpacing}),this.options.deg=this.options.tiltAngle*Math.PI/180}return e.prototype.init=function(){var e,t,n,i,o=null,r=null,a;return r=(o=this.createCanvas()).getContext("2d"),o.width=(null===(e=null===window||void 0===window?void 0:window.screen)||void 0===e?void 0:e.width)||(null===(t=null===document||void 0===document?void 0:document.documentElement)||void 0===t?void 0:t.clientWidth)||749,o.height=(null===(n=null===window||void 0===window?void 0:window.screen)||void 0===n?void 0:n.height)||(null===(i=null===document||void 0===document?void 0:document.documentElement)||void 0===i?void 0:i.clientHeight)||326,this.calcTextSize(),this.setCanvasStyle(r),this.drawText(r),o.toDataURL("image/png")},e.prototype.calcTextSize=function(){var e=this.options,t=function e(t){var n=document.createElement("div");return n.innerHTML=t.trim(),n.firstChild},n="exclusiveDingTalkWaterMarkCustomClass"+100*Math.random(),i=t('<span id="'+n+'" style="font:'+e.fontSize+"px "+e.textFont+';visibility:hidden;display:inline-block;">'+e.texts+"</span>");document.body.appendChild(i);var o=document.getElementById(n),r=Math.max(o.clientWidth,e.texts.length*e.fontSize*1.3)||200,a=Math.min(o.clientHeight,1.3*e.fontSize)||16;e.tWidth=r,e.tHeight=a,document.body.removeChild(i)},e.prototype.setCanvasStyle=function(e){var t=this.options,n=t.deg,i=t.fontStyle,o=t.fontSize,r=t.textFont,a=t.fontColor,u=t.transparency;e.rotate(n),e.font=i+" "+o+"px "+r,e.fillStyle=a,e.textAlign="left",e.textBaseline="bottom",e.globalAlpha=u/100},e.prototype.fillContent=function(e,t){for(var n=this.options,i=n.width,o=n.height,r=n.texts,a=n.tWidth,u=n.tHeight,s=0;s<40;s++)for(var d=s*o+u,f=0;f<40;f++){var c=void 0;c=s%2==0?e===l.RIGHT?(a+i)*f:(a+i)*f+a+i:e===l.RIGHT?(a+i)*f+i:(a+i)*f+a,t.fillText(r,e===l.RIGHT?c:-c,d)}},e.prototype.drawText=function(e){this.fillContent(l.RIGHT,e),this.fillContent(l.LEFT,e)},e.prototype.createCanvas=function(){var e=document.createElement("canvas");return this.options.canvas.push(e),e},e}();function _(e,t){var n,r,u,l,s,d,f;void 0===t&&(t=o.H5PAGEOTHER);var c=null;try{c=JSON.parse(e)}catch(e){c={}}var p=null===(n=null==c?void 0:c.watermark)||void 0===n?void 0:n.ruleContent,_=null==c?void 0:c.userInfo;if((null==p?void 0:p.enable)===i.ENABLE&&(null===(r=null==p?void 0:p.effectPage)||void 0===r?void 0:r[t])!==i.ENABLE)return"";var h="",m;return(null===(u=null==p?void 0:p.watermarkContent)||void 0===u?void 0:u.enableUsername)===i.ENABLE&&(h+=null==_?void 0:_.userName),(null===(l=null==p?void 0:p.watermarkContent)||void 0===l?void 0:l.enablePhoneNumber)===i.ENABLE&&(h+=" "+(null==_?void 0:_.lastFourPhoneNo)),(null===(s=null==p?void 0:p.watermarkContent)||void 0===s?void 0:s.customCopy)&&(h+=" "+(null===(d=null==p?void 0:p.watermarkContent)||void 0===d?void 0:d.customCopy)),h.length?new v(Object.assign({texts:h,textFont:"PingFangSC-Regular,system-ui,sans-serif"},null==p?void 0:p.watermarkStyle,{fontStyle:a[null===(f=null==p?void 0:p.watermarkStyle)||void 0===f?void 0:f.fontStyle]})).init():""}function h(e,t){void 0===t&&(t=o.H5PAGEOTHER);try{return _.call(null,JSON.stringify(e),t)}catch(e){return""}}t.default=h},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(){return o.default.invoke("getWifiStatus")}o.default.registerAPI("getWifiStatus",{mobile:!0,mini:!0}),r.version={android:"1.3.5",ios:"1.3.5"},t.default=r},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(){return o.default.invoke("getWorkbenchContext")}o.default.registerAPI("getWorkbenchContext",{mini:!0,mobile:!0,pc:function e(t,n){window.dingtalk.platform.invokeAPI(n.msgId,"getWorkbenchContext",t)}}),r.version={android:"2.1.10",ios:"2.1.10"},t.default=r},function(e,t,n){"use strict";var i=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n),Object.defineProperty(e,i,{enumerable:!0,get:function e(){return t[n]}})}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),r=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.hasOwnProperty.call(e,n)&&i(t,e,n);return o(t,e),t};Object.defineProperty(t,"__esModule",{value:!0});var a=r(n(0));function u(){return a.default.invoke("h5PageBack",{_apiName:"goBack"})}a.default.registerAPI("h5PageBack",{mobile:function e(t,n){var i=n.resolve,o=n.reject,r=n.context,u=n.containerType,l=n.platformType;if(u){var s=function e(t){a.default.handleBridgeResponse(t,i,o)},d=function e(t){a.default.handleBridgeResponse(t,i,o)};l===a.PLATFORM_TYPE_ENUM.ANDROID?r&&r(s,d,"biz.navigation","goBack",t):l===a.PLATFORM_TYPE_ENUM.IOS&&r.callHandler("biz.navigation.goBack",Object.assign({},t),(function(e){i(e)}))}else r&&r.call("h5PageBack",{_apiName:"goBack"},(function(){i()}))}}),u.version={android:"1.3.0",ios:"1.3.9"},t.default=u},function(e,t,n){"use strict";var i=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n),Object.defineProperty(e,i,{enumerable:!0,get:function e(){return t[n]}})}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),r=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.hasOwnProperty.call(e,n)&&i(t,e,n);return o(t,e),t};Object.defineProperty(t,"__esModule",{value:!0});var a=r(n(0));function u(e,t){var n=t.resolve,i=t.reject,o=t.context,r=t.containerType,u=t.appType,l=t.platformType;if(r){var s=function e(t){a.default.handleBridgeResponse(t,n,i)},d=function e(t){a.default.handleBridgeResponse(t,n,i)};u===a.API_INVOKER_TYPE.MINI_APP?o&&o({m:"device.notification.hidePreloader",args:e,onSuccess:s,onFail:d}):l===a.PLATFORM_TYPE_ENUM.ANDROID?o&&o(s,d,"biz.notification","hidePreloader",e):l===a.PLATFORM_TYPE_ENUM.IOS&&o.callHandler("device.notification.hidePreloader",Object.assign({},e),(function(e){n(e)}))}else o&&o.call("hideLoading",e,(function(){n()}))}function l(){return a.default.invoke("hideLoading")}a.default.registerAPI("hideLoading",{mini:u,mobile:u}),l.version={android:"1.3.2",ios:"1.3.2"},t.default=l},function(e,t,n){"use strict";var i=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n),Object.defineProperty(e,i,{enumerable:!0,get:function e(){return t[n]}})}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),r=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.hasOwnProperty.call(e,n)&&i(t,e,n);return o(t,e),t};Object.defineProperty(t,"__esModule",{value:!0});var a=r(n(0));function u(e,t){var n=t.resolve,i=t.reject,o=t.context,r=t.containerType,u=t.appType,l=t.platformType;if(r){var s=function e(t){a.default.handleBridgeResponse(t,n,i)},d=function e(t){a.default.handleBridgeResponse(t,n,i)},f={show:!1,control:!0,text:""};l===a.PLATFORM_TYPE_ENUM.ANDROID?o&&o(s,d,"biz.navigation","setRight",f):l===a.PLATFORM_TYPE_ENUM.IOS&&o.callHandler("biz.navigation.setRight",Object.assign({},f),(function(e){n(e)}))}else o&&o.call("hideOptionMenu",e,(function(){n()}))}function l(){return a.default.invoke("hideOptionMenu")}a.default.registerAPI("hideOptionMenu",{mobile:u}),l.version={android:"1.1.0",ios:"1.1.0"},t.default=l},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(){return o.default.invoke("hideTitlebar")}o.default.registerAPI("hideTitlebar",{mini:!0,mobile:!0}),r.version={android:"2.1.0",ios:"2.1.0"},t.default=r},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(e){return o.default.invoke("isDownloadFileExist",e)}o.default.registerAPI("isDownloadFileExist",{pc:function e(t,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.util.isLocalFileExist",t)}}),r.version={pc:"1.3.5"},t.default=r},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(e){return o.default.invoke("locateOnMap",e)}o.default.registerAPI("locateOnMap",{mobile:!0,mini:!0}),r.version={android:"1.3.0",ios:"1.3.0"},t.default=r},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(e,t){return o.default.registerEvent(e,t)}t.default=r},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(){return o.default.invoke("onAudioPlayEnd")}o.default.registerAPI("onAudioPlayEnd",{mini:!0,mobile:!0}),r.version={android:"1.6.2",ios:"1.6.2"},t.default=r},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(e){return o.default.invoke("onRecordAudioEnd",e)}o.default.registerAPI("onRecordAudioEnd",{mini:!0,mobile:!0}),r.version={android:"1.3.0",ios:"1.3.0"},t.default=r},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(e){return o.default.invoke("openApp",e)}o.default.registerAPI("openApp",{mini:!0,mobile:!0}),r.version={android:"1.3.2",ios:"1.3.2"},t.default=r},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(e){return o.default.invoke("openBrowser",e)}o.default.registerAPI("openBrowser",{mini:!0,mobile:!0}),r.version={android:"1.2.3"},t.default=r},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(e){return o.default.invoke("openChat",e)}o.default.registerAPI("openChat",{mini:!0,mobile:!0,pc:function e(t,n){window.dingtalk.platform.invokeAPI(n.msgId,"internal.chat.toConversation",{cid:t.chatId})}}),r.version={android:"1.2.0",ios:"1.2.0",pc:"1.2.0"},t.default=r},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(e){return o.default.invoke("openDownloadFile",e)}o.default.registerAPI("openDownloadFile",{pc:function e(t,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.util.openLocalFile",t)}}),r.version={pc:"1.3.5"},t.default=r},function(e,t,n){"use strict";var i=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n),Object.defineProperty(e,i,{enumerable:!0,get:function e(){return t[n]}})}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),r=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.hasOwnProperty.call(e,n)&&i(t,e,n);return o(t,e),t},a=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var u=r(n(0)),l=a(n(1)),s=a(n(4));function d(e,t){var n=t.resolve,i=t.reject,o=t.context,r=t.containerType,a=t.appType,d=t.platformType;l.default().then((function(t){var l=t.version,f=-1!==s.default(l,"1.6.2");if(r){var c=function e(t){u.default.handleBridgeResponse(t,n,i)},p=function e(t){u.default.handleBridgeResponse(t,n,i)};a===u.API_INVOKER_TYPE.MINI_APP?o&&o({m:f?"taurus.common.openLink":"taurus.common.pushWindow",args:e,onSuccess:c,onFail:p}):d===u.PLATFORM_TYPE_ENUM.ANDROID?o&&o(c,p,"taurus.common",f?"openLink":"pushWindow",e):d===u.PLATFORM_TYPE_ENUM.IOS&&o.callHandler(f?"taurus.common.openLink":"taurus.common.pushWindow",Object.assign({},e),(function(e){u.default.handleBridgeResponse(e,n,i)}))}else o&&o.call(f?"openLink":"pushWindow",e,(function(e){u.default.handleBridgeResponse(e,n,i)}))}))}function f(e){return u.default.invoke("openLink",e)}u.default.registerAPI("openLink",{mini:d,mobile:d,pc:function e(t,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.util.openLink",t)}}),f.version={android:"1.2.0",ios:"1.2.0",pc:"1.2.0"},t.default=f},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(e){return o.default.invoke("openPage",e)}o.default.registerAPI("openPage",{mini:!0,mobile:!0}),r.version={android:"1.1.0",ios:"1.1.0"},t.default=r},function(e,t,n){"use strict";var i=this&&this.__assign||function(){return(i=Object.assign||function(e){for(var t,n=1,i=arguments.length;n<i;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},o=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var r=o(n(0));function a(e){return r.default.invoke("dgOpenApp",i(i({},e),{_apiName:"openSchemeUrl"}))}r.default.registerAPI("dgOpenApp",{mobile:!0,mini:!0}),a.version={android:"1.3.1.1",ios:"1.3.1.1"},t.default=a},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(e){return o.default.invoke("openSlidePanel",e)}o.default.registerAPI("openSlidePanel",{pc:function e(t,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.util.openSlidePanel",t)}}),r.version={pc:"1.3.5"},t.default=r},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(){return o.default.invoke("openWatermarkCamera")}o.default.registerAPI("openWatermarkCamera",{mobile:!0,mini:!0}),r.version={android:"1.3.7",ios:"1.3.7"},t.default=r},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(e){return o.default.invoke("pauseAudio",e)}o.default.registerAPI("pauseAudio",{mini:!0,mobile:!0}),r.version={android:"1.3.0",ios:"1.3.0"},t.default=r},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(e){return o.default.invoke("pickChat",e)}o.default.registerAPI("pickChat",{mini:!0,mobile:!0}),r.version={android:"1.2.0",ios:"1.2.0"},t.default=r},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(e){return o.default.invoke("pickChatByCorpId",e)}o.default.registerAPI("pickChatByCorpId",{mini:!0,mobile:!0}),t.default=r},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(e){return o.default.invoke("playAudio",e)}o.default.registerAPI("playAudio",{mini:!0,mobile:!0}),r.version={android:"1.3.0",ios:"1.3.0"},t.default=r},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(e){return o.default.invoke("previewDoc",e)}o.default.registerAPI("previewDoc",{mini:!0,mobile:!0}),r.version={android:"1.1.0",ios:"1.1.0"},t.default=r},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(e){return o.default.invoke("previewImage",e)}o.default.registerAPI("previewImage",{mini:!0,mobile:!0,pc:function e(t,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.util.previewImage",t)}}),r.version={android:"1.2.0",ios:"1.2.0",pc:"1.2.0"},t.default=r},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(e){return o.default.invoke("printFile",e)}o.default.registerAPI("printFile",{mini:!0,mobile:!0}),r.version={android:"2.2.10"},t.default=r},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(e){return o.default.invoke("printNativeLog",e)}o.default.registerAPI("printNativeLog",{mini:!0,mobile:!0}),r.version={android:"1.9.4",ios:"1.9.4"},t.default=r},function(e,t,n){"use strict";var i=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n),Object.defineProperty(e,i,{enumerable:!0,get:function e(){return t[n]}})}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),r=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.hasOwnProperty.call(e,n)&&i(t,e,n);return o(t,e),t};Object.defineProperty(t,"__esModule",{value:!0});var a=r(n(0));function u(e,t){var n=t.resolve,i=t.reject,o=t.context,r=t.containerType,u=t.appType,l=t.platformType,s={message:e.message,title:e.title,okButton:e.buttonLabels[0],cancelButton:e.buttonLabels[1]};if(r){var d=function e(t){a.default.handleBridgeResponse(t,n,i)},f=function e(t){a.default.handleBridgeResponse(t,n,i)};u===a.API_INVOKER_TYPE.MINI_APP?o&&o({m:"taurus.common.prompt",args:s,onSuccess:d,onFail:f}):l===a.PLATFORM_TYPE_ENUM.ANDROID?o&&o(d,f,"taurus.common","prompt",s):l===a.PLATFORM_TYPE_ENUM.IOS&&o.callHandler("taurus.common.prompt",Object.assign({},s),(function(e){a.default.handleBridgeResponse(e,n,i)}))}else o&&o.call("prompt",s,(function(e){var t={errorCode:a.BRIDGE_ERROR_CODE.SUCCESS,result:{buttonIndex:e.ok?0:1,value:e.inputValue}};a.default.handleBridgeResponse(t,n,i)}))}function l(e){return a.default.invoke("prompt",e)}a.default.registerAPI("prompt",{mini:u,mobile:u,pc:function e(t,n){window.dingtalk.platform.invokeAPI(n.msgId,"device.notification.prompt",t)}}),l.version={android:"1.3.2",ios:"1.3.2",pc:"1.3.2"},t.default=l},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(e){return o.default.invoke("readImageToBase64",e)}o.default.registerAPI("readImageToBase64",{mini:!0,mobile:!0,pc:function e(t,n){window.dingtalk.platform.invokeAPI(n.msgId,"readImageToBase64",t)}}),r.version={ios:"2.1.0",android:"2.1.0",pc:"2.1.0"},t.default=r},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(e){"function"==typeof e?o.default.onReady(e):console.error("dd.ready's param must be function! ")}t.default=r},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.COMPRESS_LEVEL=void 0;var o=i(n(0)),r;function a(e){return o.default.invoke("reduceImageSize",e)}!function(e){e[e.ADJUST_BY_NET=0]="ADJUST_BY_NET",e[e.LOW_QUALITY=1]="LOW_QUALITY",e[e.MID_QUALITY=2]="MID_QUALITY",e[e.HIGH_QUALITY=3]="HIGH_QUALITY",e[e.NOT_COMPRESSED=4]="NOT_COMPRESSED",e[e.CUSTOM=5]="CUSTOM"}(r=t.COMPRESS_LEVEL||(t.COMPRESS_LEVEL={})),o.default.registerAPI("reduceImageSize",{mini:!0,mobile:!0,pc:function e(t,n){window.dingtalk.platform.invokeAPI(n.msgId,"reduceImageSize",t)}}),a.version={ios:"2.1.0",android:"2.1.0",pc:"2.1.0"},t.default=a},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(e){return o.default.invoke("removeStorageItem",e)}o.default.registerAPI("removeStorageItem",{mobile:!0,mini:!0}),r.version={android:"1.3.1.1",ios:"1.3.1.1"},t.default=r},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(e){return o.default.invoke("replacePage",e)}o.default.registerAPI("replacePage",{mini:!0,mobile:!0}),r.version={android:"1.3.2",ios:"1.3.2"},t.default=r},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(){return o.default.invoke("resetView")}o.default.registerAPI("resetView",{mini:!0,mobile:!0}),r.version={android:"1.3.0",ios:"1.3.0"},t.default=r},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(e){return o.default.invoke("resumeAudio",e)}o.default.registerAPI("resumeAudio",{mini:!0,mobile:!0}),r.version={android:"1.3.0",ios:"1.3.0"},t.default=r},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(e){return o.default.invoke("rotateView",e)}o.default.registerAPI("rotateView",{mini:!0,mobile:!0}),r.version={android:"1.3.0",ios:"1.3.0"},t.default=r},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(e){return o.default.invoke("scan",e)}o.default.registerAPI("scan",{mini:!0,mobile:!0}),r.version={android:"1.3.2",ios:"1.3.2"},t.default=r},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(e){return o.default.invoke("searchOnMap",e)}o.default.registerAPI("searchOnMap",{mini:!0,mobile:!0}),r.version={android:"1.3.2",ios:"1.3.2"},t.default=r},function(e,t,n){"use strict";var i=this&&this.__assign||function(){return(i=Object.assign||function(e){for(var t,n=1,i=arguments.length;n<i;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},o=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var r=o(n(0));function a(e){return i(i({},e),{actionId:"",actionType:"0"})}function u(e,t){var n=t.resolve,i=t.context;i&&i.call("sendOutData",a(e),(function(){n()}))}function l(e){return r.default.invoke("cardSendOutData",e)}r.default.registerAPI("cardSendOutData",{mini:u,mobile:u}),l.version={android:"2.5.0",ios:"2.5.0"},t.default=l},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(e){return o.default.invoke("setNavIcon",e)}o.default.registerAPI("setNavIcon",{mobile:!0}),r.version={android:"1.3.0",ios:"1.3.0"},t.default=r},function(e,t,n){"use strict";var i=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n),Object.defineProperty(e,i,{enumerable:!0,get:function e(){return t[n]}})}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),r=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.hasOwnProperty.call(e,n)&&i(t,e,n);return o(t,e),t},a=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var u=r(n(0)),l=a(n(5));function s(e,t){var n=t.resolve,i=t.reject,o=t.context,r=t.containerType,a=t.appType,l=t.platformType,s=t.watch;if(r){var d=function t(o){e.onSuccess&&e.onSuccess(),u.default.handleBridgeResponse(o,n,i)},f=function e(t){u.default.handleBridgeResponse(t,n,i)};a===u.API_INVOKER_TYPE.MINI_APP?o&&o({m:"biz.navigation.setLeft",args:e,onSuccess:d,onFail:f}):l===u.PLATFORM_TYPE_ENUM.ANDROID?o&&o(d,f,"biz.navigation","setLeft",e):l===u.PLATFORM_TYPE_ENUM.IOS&&o.callHandler("biz.navigation.setLeft",Object.assign({},e),(function(e){!s&&n(e)}))}else o&&o.call("setNavLeftText",e,(function(){n()}))}function d(e){var t=l.default();return u.default.invoke("setNavLeftText",t===u.Container_Type_Enum.isDingTalk?Object.assign({watch:!0,show:!0,control:!1,showIcon:!0,text:""},e):e,{dingTalkAPIName:t===u.Container_Type_Enum.isDingTalk?"biz.navigation.setLeft":null})}u.default.registerAPI("setNavLeftText",{mini:s,mobile:s,pc:function e(t,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.navigation.setLeft",t)}}),d.version={ios:"1.2.0",pc:"1.2.0"},t.default=d},function(e,t,n){"use strict";var i=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n),Object.defineProperty(e,i,{enumerable:!0,get:function e(){return t[n]}})}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),r=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.hasOwnProperty.call(e,n)&&i(t,e,n);return o(t,e),t},a=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var u=r(n(0)),l=a(n(5));function s(e){var t=l.default();return u.default.invoke("setOptionMenu",t===u.Container_Type_Enum.isDingTalk?Object.assign({watch:!0,show:!0,control:!1,showIcon:!0,text:""},e):e,t===u.Container_Type_Enum.isDingTalk?{dingTalkAPIName:"biz.navigation.setRight"}:null)}u.default.registerAPI("setOptionMenu",{mobile:function e(t,n){var i,o,r,a,l=n.resolve,s=n.reject,d=n.context,f=n.containerType,c=n.platformType;if(f){var p=function e(){t.onSuccess&&t.onSuccess(),l()},v=function e(){s()},_={text:t.title,show:void 0===t.show||t.show,control:void 0===t.control||t.control};c===u.PLATFORM_TYPE_ENUM.ANDROID?d&&d(p,v,"biz.navigation",(null===(i=null==t?void 0:t.menus)||void 0===i?void 0:i.length)>1?"setMenu":"setRight",(null===(o=null==t?void 0:t.menus)||void 0===o?void 0:o.length)>1?t:_):c===u.PLATFORM_TYPE_ENUM.IOS&&d.callHandler((null===(r=null==t?void 0:t.menus)||void 0===r?void 0:r.length)>1?"biz.navigation.setMenu":"biz.navigation.setRight",Object.assign({},(null===(a=null==t?void 0:t.menus)||void 0===a?void 0:a.length)>1?t:_),(function(){l()}))}else d&&d.call("setOptionMenu",t,(function(){l()}))}}),s.version={android:"1.1.0",ios:"1.1.0"},t.default=s},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(e){return o.default.invoke("setStorageItem",e)}o.default.registerAPI("setStorageItem",{mobile:!0,mini:!0}),r.version={android:"1.3.1.1",ios:"1.3.1.1"},t.default=r},function(e,t,n){"use strict";var i=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n),Object.defineProperty(e,i,{enumerable:!0,get:function e(){return t[n]}})}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),r=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.hasOwnProperty.call(e,n)&&i(t,e,n);return o(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.goBackBtnEnum=void 0;var a=r(n(0)),u;function l(e,t){var n=t.resolve,i=t.reject,o=t.context,r=t.containerType,u=t.appType,l=t.platformType;if(r){var s=function e(t){a.default.handleBridgeResponse(t,n,i)},d=function e(t){a.default.handleBridgeResponse(t,n,i)};u===a.API_INVOKER_TYPE.MINI_APP?o&&o({m:"biz.navigation.setTitle",args:e,onSuccess:s,onFail:d}):l===a.PLATFORM_TYPE_ENUM.ANDROID?o&&o(s,d,"biz.navigation","setTitle",e):l===a.PLATFORM_TYPE_ENUM.IOS&&o.callHandler("biz.navigation.setTitle",Object.assign({},e),(function(e){a.default.handleBridgeResponse(e,n,i)}))}else o&&o.call("setTitle",e,(function(){n()}))}function s(e){return a.default.invoke("setTitle",e)}!function(e){e.TRUE="true",e.FALSE="false"}(u=t.goBackBtnEnum||(t.goBackBtnEnum={})),a.default.registerAPI("setTitle",{mini:l,mobile:l,pc:function e(t,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.navigation.setTitle",t)}}),s.version={android:"1.2.0",ios:"1.2.0",pc:"1.2.0"},t.default=s},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(e){return o.default.invoke("shareFileToMessage",e)}o.default.registerAPI("shareFileToMessage",{mini:!0,mobile:!0,pc:function e(t,n){window.dingtalk.platform.invokeAPI(n.msgId,"shareFileToMessage",t)}}),r.version={android:"1.8.2",ios:"1.8.2",pc:"1.8.2"},t.default=r},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(e){return o.default.invoke("shareImageToMessage",e)}o.default.registerAPI("shareImageToMessage",{mini:!0,mobile:!0,pc:function e(t,n){window.dingtalk.platform.invokeAPI(n.msgId,"shareImageToMessage",t)}}),r.version={android:"1.8.2",ios:"1.8.2",pc:"1.8.2"},t.default=r},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(e){return o.default.invoke("shareToMessage",e)}o.default.registerAPI("shareToMessage",{mini:!0,mobile:!0,pc:function e(t,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.util.share",t)}}),r.version={android:"1.3.5",ios:"1.3.5",pc:"1.3.5"},t.default=r},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(){return o.default.invoke("shootVideo")}o.default.registerAPI("shootVideo",{mini:!0,mobile:!0}),r.version={android:"1.3.5",ios:"1.3.5"},t.default=r},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(e){return o.default.invoke("showActionSheet",e)}o.default.registerAPI("showActionSheet",{mini:!0,mobile:!0,pc:function e(t,n){window.dingtalk.platform.invokeAPI(n.msgId,"device.notification.actionSheet",t)}}),r.version={android:"1.3.2",ios:"1.3.2",pc:"1.3.2"},t.default=r},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(e){return o.default.invoke("showCallMenu",e)}o.default.registerAPI("showCallMenu",{mini:!0,mobile:!0}),r.version={android:"1.3.9",ios:"1.3.9"},t.default=r},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(e){return o.default.invoke("showDatePicker",e)}o.default.registerAPI("showDatePicker",{mobile:!0,mini:!0}),r.version={android:"1.3.0",ios:"1.3.0"},t.default=r},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(e){return o.default.invoke("showDateTimePicker",e)}o.default.registerAPI("showDateTimePicker",{mini:!0,mobile:!0}),r.version={android:"1.3.10",ios:"1.3.10"},t.default=r},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(e){return o.default.invoke("showExtendModal",e)}o.default.registerAPI("showExtendModal",{mini:!0,mobile:!0}),r.version={android:"1.3.5",ios:"1.3.5"},t.default=r},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(e){return o.default.invoke("showHomeBottomTab",e)}o.default.registerAPI("showHomeBottomTab",{mobile:!0}),r.version={android:"1.3.0",ios:"1.3.0"},t.default=r},function(e,t,n){"use strict";var i=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n),Object.defineProperty(e,i,{enumerable:!0,get:function e(){return t[n]}})}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),r=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.hasOwnProperty.call(e,n)&&i(t,e,n);return o(t,e),t};Object.defineProperty(t,"__esModule",{value:!0});var a=r(n(0));function u(e,t){var n=t.resolve,i=t.reject,o=t.context,r=t.containerType,u=t.appType,l=t.platformType;if(r){var s=function e(t){a.default.handleBridgeResponse(t,n,i)},d=function e(t){a.default.handleBridgeResponse(t,n,i)};u===a.API_INVOKER_TYPE.MINI_APP?o&&o({m:"device.notification.showPreloader",args:e,onSuccess:s,onFail:d}):l===a.PLATFORM_TYPE_ENUM.ANDROID?o&&o(s,d,"biz.notification","showPreloader",e):l===a.PLATFORM_TYPE_ENUM.IOS&&o.callHandler("device.notification.showPreloader",Object.assign({},e),(function(e){n(e)}))}else o&&o.call("showLoading",e,(function(){n()}))}function l(e){return a.default.invoke("showLoading",e)}a.default.registerAPI("showLoading",{mini:u,mobile:u}),l.version={android:"1.3.2",ios:"1.3.2"},t.default=l},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(e){return o.default.invoke("showModal",e)}o.default.registerAPI("showModal",{mini:!0,mobile:!0,pc:function e(t,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.util.openModal",t)}}),r.version={android:"1.3.5",ios:"1.3.5",pc:"1.3.5"},t.default=r},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(e){return o.default.invoke("showMultiSelect",e)}o.default.registerAPI("showMultiSelect",{mini:!0,mobile:!0}),r.version={android:"1.3.10",ios:"1.3.10"},t.default=r},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(e){return o.default.invoke("showOnMap",e)}o.default.registerAPI("showOnMap",{mini:!0,mobile:!0}),r.version={android:"1.3.2",ios:"1.3.2"},t.default=r},function(e,t,n){"use strict";var i=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n),Object.defineProperty(e,i,{enumerable:!0,get:function e(){return t[n]}})}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),r=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.hasOwnProperty.call(e,n)&&i(t,e,n);return o(t,e),t};Object.defineProperty(t,"__esModule",{value:!0});var a=r(n(0));function u(){return a.default.invoke("showOptionMenu")}a.default.registerAPI("showOptionMenu",{mobile:function e(t,n){var i=n.resolve,o=n.reject,r=n.context,u=n.platformType,l;if(n.containerType){var s=function e(){i()},d=function e(){o()};u===a.PLATFORM_TYPE_ENUM.ANDROID?r&&r(s,d,"taurus.common","showOptionMenu",t):u===a.PLATFORM_TYPE_ENUM.IOS&&r.callHandler("taurus.common.showOptionMenu",Object.assign({},t),(function(){i()}))}else r&&r.call("showOptionMenu",t,(function(){i()}))}}),u.version={android:"1.1.0",ios:"1.1.0"},t.default=u},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(e){return o.default.invoke("showPlainInputUponKeyboard",e)}o.default.registerAPI("showPlainInputUponKeyboard",{mobile:!0,mini:!0}),r.version={android:"1.3.0",ios:"1.3.0"},t.default=r},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(e){return o.default.invoke("showQuickCallMenu",e)}o.default.registerAPI("showQuickCallMenu",{mini:!0,mobile:!0}),r.version={android:"1.6.2",ios:"1.6.2"},t.default=r},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(e){return o.default.invoke("showSelect",e)}o.default.registerAPI("showSelect",{mini:!0,mobile:!0}),r.version={android:"1.3.2",ios:"1.3.2"},t.default=r},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(e){return o.default.invoke("showSignature",e)}o.default.registerAPI("showSignature",{mobile:!0}),r.version={android:"1.3.4"},t.default=r},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(e){return o.default.invoke("showSocialShare",e)}o.default.registerAPI("showSocialShare",{mini:!0,mobile:!0}),r.version={android:"1.2.0.10",ios:"1.2.0.10"},t.default=r},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(e){return o.default.invoke("showTimePicker",e)}o.default.registerAPI("showTimePicker",{mobile:!0,mini:!0}),r.version={android:"1.3.0",ios:"1.3.0"},t.default=r},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(){return o.default.invoke("showTitlebar")}o.default.registerAPI("showTitlebar",{mini:!0,mobile:!0}),r.version={android:"2.1.0",ios:"2.1.0"},t.default=r},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(e){return o.default.invoke("startFaceRecognition",e)}o.default.registerAPI("startFaceRecognition",{mini:!0,mobile:!0}),r.version={android:"1.8.2",ios:"1.8.2"},t.default=r},function(e,t,n){"use strict";var i=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n),Object.defineProperty(e,i,{enumerable:!0,get:function e(){return t[n]}})}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),r=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.hasOwnProperty.call(e,n)&&i(t,e,n);return o(t,e),t};Object.defineProperty(t,"__esModule",{value:!0});var a=r(n(0));function u(e,t){var n=t.resolve,i=t.reject,o=t.context,r=t.platformType,u=t.containerType,l=t.appType,s=a.default.registerEvent(a.CONTINUOUS_EVENT_LIST.UPDATE_LOCATION,(function(t){var n=t.data;n.errorCode!==a.BRIDGE_ERROR_CODE.SUCCESS?e.onFail&&e.onFail(n):e.onSuccess&&e.onSuccess(n.result)}));if(u){var d=function t(o){a.default.registerContinuesEvent(e.sceneId,s),a.default.handleBridgeResponse(o,n,i)},f=function t(o){a.default.registerContinuesEvent(e.sceneId,s),a.default.handleBridgeResponse(o,n,i)};l===a.API_INVOKER_TYPE.MINI_APP?(console.log("taurus.common.startGeolocation",e),o&&o({m:"taurus.common.startGeolocation",args:e,onSuccess:d,onFail:f})):r===a.PLATFORM_TYPE_ENUM.ANDROID?o&&o(d,f,"taurus.common","startGeolocation",e):r===a.PLATFORM_TYPE_ENUM.IOS&&o.callHandler("taurus.common.startGeolocation",Object.assign({},e),(function(t){a.default.registerContinuesEvent(e.sceneId,s),a.default.handleBridgeResponse(t,n,i)}))}else o&&o.call("startGeolocation",e,(function(t){a.default.registerContinuesEvent(e.sceneId,s),a.default.handleBridgeResponse(t,n,i)}))}function l(e){return a.default.invoke("startGeolocation",e)}a.default.registerAPI("startGeolocation",{mobile:u,mini:u}),l.version={android:"1.3.2",ios:"1.3.2"},t.default=l},function(e,t,n){"use strict";var i=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n),Object.defineProperty(e,i,{enumerable:!0,get:function e(){return t[n]}})}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),r=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.hasOwnProperty.call(e,n)&&i(t,e,n);return o(t,e),t};Object.defineProperty(t,"__esModule",{value:!0});var a=r(n(0));function u(e,t){var n=t.resolve,i=t.reject,o=t.context,r=t.containerType,u=t.appType,l=t.platformType,s=a.default.registerEvent(a.CONTINUOUS_EVENT_LIST.UPDATE_NETWORK_STATUS,(function(t){var n=t.data;n.errorCode!==a.BRIDGE_ERROR_CODE.SUCCESS?e.onFail&&e.onFail(n):e.onSuccess&&e.onSuccess(n.result)}));if(r){var d=function e(t){a.default.registerContinuesEvent(t.result.requestId,s),a.default.handleBridgeResponse(t,n,i)},f=function e(t){a.default.registerContinuesEvent(t.result.requestId,s),a.default.handleBridgeResponse(t,n,i)};u===a.API_INVOKER_TYPE.MINI_APP?o&&o({m:"taurus.common.startListenNetworkStatus",args:e,onSuccess:d,onFail:f}):l===a.PLATFORM_TYPE_ENUM.ANDROID?o&&o(d,f,"taurus.common","startListenNetworkStatus",e):l===a.PLATFORM_TYPE_ENUM.IOS&&o.callHandler("taurus.common.startListenNetworkStatus",Object.assign({},e),(function(e){a.default.registerContinuesEvent(e.result.requestId,s),a.default.handleBridgeResponse(e,n,i)}))}else o&&o.call("startListenNetworkStatus",e,(function(e){a.default.registerContinuesEvent(e.result.requestId,s),a.default.handleBridgeResponse(e,n,i)}))}function l(e){return a.default.invoke("startListenNetworkStatus",e)}a.default.registerAPI("startListenNetworkStatus",{mobile:u,mini:u}),l.version={android:"1.3.1.1",ios:"1.3.1.1"},t.default=l},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(e){return o.default.invoke("startRecordAudio",e)}o.default.registerAPI("startRecordAudio",{mini:!0,mobile:!0}),r.version={android:"1.3.0",ios:"1.3.0"},t.default=r},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(138));t.default=o.default},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(e){return o.default.invoke("startRequest",e)}o.default.registerAPI("startRequest",{mini:!0,mobile:!0,pc:function e(t,n){window.dingtalk.platform.invokeAPI(n.msgId,"startRequest",{url:t.apiName,params:JSON.stringify(t.params)})}}),t.default=r},function(e,t,n){"use strict";var i=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n),Object.defineProperty(e,i,{enumerable:!0,get:function e(){return t[n]}})}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),r=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.hasOwnProperty.call(e,n)&&i(t,e,n);return o(t,e),t};Object.defineProperty(t,"__esModule",{value:!0});var a=r(n(0));function u(e,t){var n=t.resolve,i=t.reject,o=t.context,r=t.containerType,u=t.platformType,l=t.appType,s=a.default.registerEvent(a.CONTINUOUS_EVENT_LIST.UPDATE_TRACE,(function(t){var n=t.data;n.errorCode&&n.errorCode!==a.BRIDGE_ERROR_CODE.SUCCESS?e.onFail&&e.onFail(n):e.onSuccess&&e.onSuccess(n.result||n)}));if(r){var d=function t(o){a.default.registerContinuesEvent(e.traceId,s),a.default.handleBridgeResponse(o,n,i)},f=function t(o){a.default.registerContinuesEvent(e.traceId,s),a.default.handleBridgeResponse(o,n,i)};l===a.API_INVOKER_TYPE.MINI_APP?o&&o({m:"taurus.common.startTraceReport",args:e,onSuccess:d,onFail:f}):u===a.PLATFORM_TYPE_ENUM.ANDROID?o&&o(d,f,"taurus.common","startTraceReport",e):u===a.PLATFORM_TYPE_ENUM.IOS&&o.callHandler("taurus.common.startTraceReport",Object.assign({},e),(function(t){a.default.registerContinuesEvent(e.traceId,s),a.default.handleBridgeResponse(t,n,i)}))}else o&&o.call("startTraceReport",e,(function(t){a.default.registerContinuesEvent(e.traceId,s),a.default.handleBridgeResponse(t,n,i)}))}function l(e){return a.default.invoke("startTraceReport",e)}a.default.registerAPI("startTraceReport",{mobile:u,mini:!0}),l.version={android:"1.3.4",ios:"1.3.4"},t.default=l},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(e){return o.default.invoke("startVPNApp",e)}o.default.registerAPI("startVPNApp",{mini:!0,mobile:!0}),r.version={android:"1.6.0",ios:"1.6.0"},t.default=r},function(e,t,n){"use strict";var i=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n),Object.defineProperty(e,i,{enumerable:!0,get:function e(){return t[n]}})}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),r=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.hasOwnProperty.call(e,n)&&i(t,e,n);return o(t,e),t};Object.defineProperty(t,"__esModule",{value:!0});var a=r(n(0));function u(e,t){var n=t.resolve,i=t.reject,o=t.context,r=t.containerType,u=t.platformType,l=a.default.registerEvent(a.CONTINUOUS_EVENT_LIST.ON_SHAKE,(function(){e.onSuccess&&e.onSuccess()}));if(r){var s=function e(t){a.default.registerContinuesEvent("shake",l),a.default.handleBridgeResponse(t,n,i)},d=function e(t){a.default.registerContinuesEvent("shake",l),a.default.handleBridgeResponse(t,n,i)};u===a.PLATFORM_TYPE_ENUM.ANDROID?o&&o(s,d,"taurus.common","startWatchShake",e):u===a.PLATFORM_TYPE_ENUM.IOS&&o.callHandler("taurus.common.startWatchShake",Object.assign({},e),(function(e){a.default.registerContinuesEvent("shake",l),a.default.handleBridgeResponse(e,n,i)}))}else o&&o.call("startWatchShake",e,(function(e){a.default.registerContinuesEvent("shake",l),a.default.handleBridgeResponse(e,n,i)}))}function l(e){return a.default.invoke("startWatchShake",e)}a.default.registerAPI("startWatchShake",{mobile:u}),l.version={android:"1.6.2",ios:"1.6.2"},t.default=l},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(e){return o.default.invoke("stopAudio",e)}o.default.registerAPI("stopAudio",{mini:!0,mobile:!0}),r.version={android:"1.3.0",ios:"1.3.0"},t.default=r},function(e,t,n){"use strict";var i=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n),Object.defineProperty(e,i,{enumerable:!0,get:function e(){return t[n]}})}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),r=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.hasOwnProperty.call(e,n)&&i(t,e,n);return o(t,e),t};Object.defineProperty(t,"__esModule",{value:!0});var a=r(n(0));function u(e,t){var n=t.resolve,i=t.reject,o=t.containerType,r=t.platformType,u=t.appType,l=t.context;if(o){var s=function t(o){a.default.removeContinuesEvent(e.sceneId),a.default.handleBridgeResponse(o,n,i)},d=function t(o){a.default.removeContinuesEvent(e.sceneId),a.default.handleBridgeResponse(o,n,i)};u===a.API_INVOKER_TYPE.MINI_APP?l&&l({m:"taurus.common.startGeolocation",args:e,onSuccess:s,onFail:d}):r===a.PLATFORM_TYPE_ENUM.ANDROID?l&&l(s,d,"taurus.common","stopGeolocation",e):r===a.PLATFORM_TYPE_ENUM.IOS&&l.callHandler("taurus.common.stopGeolocation",Object.assign({},e),(function(t){a.default.removeContinuesEvent(e.sceneId),a.default.handleBridgeResponse(t,n,i)}))}else l&&l.call("stopGeolocation",e,(function(t){a.default.removeContinuesEvent(e.sceneId),a.default.handleBridgeResponse(t,n,i)}))}function l(e){return a.default.invoke("stopGeolocation",e)}a.default.registerAPI("stopGeolocation",{mobile:u,mini:u}),l.version={android:"1.3.2",ios:"1.3.2"},t.default=l},function(e,t,n){"use strict";var i=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n),Object.defineProperty(e,i,{enumerable:!0,get:function e(){return t[n]}})}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),r=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.hasOwnProperty.call(e,n)&&i(t,e,n);return o(t,e),t};Object.defineProperty(t,"__esModule",{value:!0});var a=r(n(0));function u(e,t){var n=t.resolve,i=t.reject,o=t.containerType,r=t.appType,u=t.platformType,l=t.context;if(o){var s=function t(o){a.default.removeContinuesEvent(e.requestId),a.default.handleBridgeResponse(o,n,i)},d=function t(o){a.default.removeContinuesEvent(e.requestId),a.default.handleBridgeResponse(o,n,i)};r===a.API_INVOKER_TYPE.MINI_APP?l&&l({m:"taurus.common.stopListenNetworkStatus",args:e,onSuccess:s,onFail:d}):u===a.PLATFORM_TYPE_ENUM.ANDROID?l&&l(s,d,"taurus.common","stopListenNetworkStatus",e):u===a.PLATFORM_TYPE_ENUM.IOS&&l.callHandler("taurus.common.stopListenNetworkStatus",Object.assign({},e),(function(t){a.default.removeContinuesEvent(e.requestId),a.default.handleBridgeResponse(t,n,i)}))}else l&&l.call("stopListenNetworkStatus",e,(function(t){a.default.removeContinuesEvent(e.requestId),a.default.handleBridgeResponse(t,n,i)}))}function l(e){return a.default.invoke("stopListenNetworkStatus",e)}a.default.registerAPI("stopListenNetworkStatus",{mini:u,mobile:u}),l.version={android:"1.3.1.1",ios:"1.3.1.1"},t.default=l},function(e,t,n){"use strict";var i=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n),Object.defineProperty(e,i,{enumerable:!0,get:function e(){return t[n]}})}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),r=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.hasOwnProperty.call(e,n)&&i(t,e,n);return o(t,e),t};Object.defineProperty(t,"__esModule",{value:!0});var a=r(n(0));function u(){return a.default.invoke("stopPullToRefresh",{_apiName:"stopPullToRefresh"})}a.default.registerAPI("stopPullToRefresh",{mobile:function e(t,n){var i=n.resolve,o=n.reject,r=n.context,u=n.containerType,l=n.platformType;if(u){var s=function e(){i()},d=function e(){o()};l===a.PLATFORM_TYPE_ENUM.ANDROID?r&&r(s,d,"ui.pullToRefresh","stop",t):l===a.PLATFORM_TYPE_ENUM.IOS&&r.callHandler("ui.pullToRefresh.stop",Object.assign({},t),(function(){i()}))}else r&&r.call("restorePullToRefresh",t,(function(){i()}))}}),u.version={android:"1.3.0",ios:"1.3.0"},t.default=u},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(e){return o.default.invoke("stopRecordAudio",e)}o.default.registerAPI("stopRecordAudio",{mini:!0,mobile:!0}),r.version={android:"1.3.0",ios:"1.3.0"},t.default=r},function(e,t,n){"use strict";var i=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n),Object.defineProperty(e,i,{enumerable:!0,get:function e(){return t[n]}})}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),r=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.hasOwnProperty.call(e,n)&&i(t,e,n);return o(t,e),t};Object.defineProperty(t,"__esModule",{value:!0});var a=r(n(0));function u(e,t){var n=t.resolve,i=t.reject,o=t.containerType,r=t.platformType,u=t.context;if(o){var l=function t(o){a.default.removeContinuesEvent(e.traceId),a.default.handleBridgeResponse(o,n,i)},s=function t(o){a.default.removeContinuesEvent(e.traceId),a.default.handleBridgeResponse(o,n,i)};r===a.PLATFORM_TYPE_ENUM.ANDROID?u&&u(l,s,"taurus.common","stopTraceReport",e):r===a.PLATFORM_TYPE_ENUM.IOS&&u.callHandler("taurus.common.stopTraceReport",Object.assign({},e),(function(t){a.default.removeContinuesEvent(e.traceId),a.default.handleBridgeResponse(t,n,i)}))}else u&&u.call("stopTraceReport",e,(function(t){a.default.removeContinuesEvent(e.traceId),a.default.handleBridgeResponse(t,n,i)}))}function l(e){return a.default.invoke("stopTraceReport",e)}a.default.registerAPI("stopTraceReport",{mobile:u}),l.version={android:"1.3.4",ios:"1.3.4"},t.default=l},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(e){return o.default.invoke("stopVPNApp",e)}o.default.registerAPI("stopVPNApp",{mini:!0,mobile:!0}),r.version={android:"1.6.0",ios:"1.6.0"},t.default=r},function(e,t,n){"use strict";var i=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n),Object.defineProperty(e,i,{enumerable:!0,get:function e(){return t[n]}})}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),r=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.hasOwnProperty.call(e,n)&&i(t,e,n);return o(t,e),t};Object.defineProperty(t,"__esModule",{value:!0});var a=r(n(0));function u(e,t){var n=t.resolve,i=t.reject,o=t.containerType,r=t.platformType,u=t.context;if(o){var l=function e(t){a.default.removeContinuesEvent("shake"),a.default.handleBridgeResponse(t,n,i)},s=function e(t){a.default.removeContinuesEvent("shake"),a.default.handleBridgeResponse(t,n,i)};r===a.PLATFORM_TYPE_ENUM.ANDROID?u&&u(l,s,"taurus.common","toast",e):r===a.PLATFORM_TYPE_ENUM.IOS&&u.callHandler("taurus.common.stopWatchShake",Object.assign({},e),(function(e){a.default.removeContinuesEvent("shake"),a.default.handleBridgeResponse(e,n,i)}))}else u&&u.call("stopWatchShake",e,(function(e){a.default.removeContinuesEvent("shake"),a.default.handleBridgeResponse(e,n,i)}))}function l(){return a.default.invoke("stopWatchShake")}a.default.registerAPI("stopWatchShake",{mobile:u}),l.version={android:"1.6.2",ios:"1.6.2"},t.default=l},function(e,t,n){"use strict";var i=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n),Object.defineProperty(e,i,{enumerable:!0,get:function e(){return t[n]}})}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),r=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.hasOwnProperty.call(e,n)&&i(t,e,n);return o(t,e),t};Object.defineProperty(t,"__esModule",{value:!0});var a=r(n(0));function u(e){return a.default.invoke("subscribe",e)}a.default.registerAPI("subscribe",{mobile:function e(t,n){var i=n.resolve,o=n.reject,r=n.context,u=n.containerType,l=n.platformType,s=!1;if(u){var d=function e(n){s?(t.onSuccess||t.onFail)&&("0"!==n.errorCode?t.onFail&&t.onFail(n):t.onSuccess&&t.onSuccess(n.result)):(s=!0,a.default.handleBridgeResponse(n,i,o))},f=function e(n){s?t.onFail&&t.onFail(n):(s=!0,a.default.handleBridgeResponse(n,i,o))};l===a.PLATFORM_TYPE_ENUM.ANDROID?r&&r(d,f,"taurus.common","subscribe",t):l===a.PLATFORM_TYPE_ENUM.IOS&&r.callHandler("taurus.common.subscribe",Object.assign({},t),(function(e){s?(t.onSuccess||t.onFail)&&("0"!==e.errorCode?t.onFail&&t.onFail(e):t.onSuccess&&t.onSuccess(e.result)):(s=!0,a.default.handleBridgeResponse(e,i,o))}))}else r&&r.call("subscribe",t,(function(e){s?(t.onSuccess||t.onFail)&&("0"!==e.errorCode?t.onFail&&t.onFail(e):t.onSuccess&&t.onSuccess(e.result)):(s=!0,a.default.handleBridgeResponse(e,i,o))}))}}),u.version={android:"1.6.0",ios:"1.6.0"},t.default=u},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(){return o.default.invoke("takePhoto")}o.default.registerAPI("takePhoto",{mini:!0,mobile:!0}),r.version={android:"1.3.5",ios:"1.3.5"},t.default=r},function(e,t,n){"use strict";var i=this&&this.__createBinding||(Object.create?function(e,t,n,i){void 0===i&&(i=n),Object.defineProperty(e,i,{enumerable:!0,get:function e(){return t[n]}})}:function(e,t,n,i){void 0===i&&(i=n),e[i]=t[n]}),o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),r=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.hasOwnProperty.call(e,n)&&i(t,e,n);return o(t,e),t};Object.defineProperty(t,"__esModule",{value:!0});var a=r(n(0));function u(e,t){var n=t.resolve,i=t.reject,o=t.context,r=t.containerType,u=t.platformType,l=t.appType,s={type:"error"===e.icon?"fail":"success"===e.icon?"success":"none",content:e.text,duration:1e3*e.duration,taurusToastStyle:e.taurusToastStyle};if(r){var d=function e(){a.default.handleBridgeResponse({errorCode:a.BRIDGE_ERROR_CODE.SUCCESS,result:{}},n,i)},f=function e(t){a.default.handleBridgeResponse(t,n,i)};l===a.API_INVOKER_TYPE.MINI_APP?o&&o({m:"taurus.common.toast",args:s,onSuccess:d,onFail:f}):u===a.PLATFORM_TYPE_ENUM.ANDROID?o&&o(d,f,"taurus.common","toast",s):u===a.PLATFORM_TYPE_ENUM.IOS&&o.callHandler("taurus.common.toast",Object.assign({},s),(function(){a.default.handleBridgeResponse({errorCode:a.BRIDGE_ERROR_CODE.SUCCESS,result:{}},n,i)}))}else o&&o.call("toast",s,(function(){a.default.handleBridgeResponse({errorCode:a.BRIDGE_ERROR_CODE.SUCCESS,result:{}},n,i)}))}function l(e){return a.default.invoke("toast",e)}a.default.registerAPI("toast",{mobile:u,mini:u,pc:function e(t,n){var i=t.icon,o=t.text,r=t.duration,a=t.delay;window.dingtalk.platform.invokeAPI(n.msgId,"device.notification.toast",{type:i,text:o,duration:r,delay:a})}}),l.version={android:"1.3.2",ios:"1.3.2"},t.default=l},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(){return o.default.invoke("unlockWithSecurityVerification")}o.default.registerAPI("unlockWithSecurityVerification",{mini:!0,mobile:!0}),r.version={android:"1.3.1.1",ios:"1.3.1.1"},t.default=r},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(e){return o.default.invoke("unsubscribe",e)}o.default.registerAPI("unsubscribe",{mobile:!0}),r.version={android:"1.6.0",ios:"1.6.0"},t.default=r},function(e,t,n){"use strict";var i=this&&this.__assign||function(){return(i=Object.assign||function(e){for(var t,n=1,i=arguments.length;n<i;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},o=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var r=o(n(0));function a(e){return r.default.invoke("dgUploadFile",i(i({},e),{_apiName:"uploadFile"}))}r.default.registerAPI("dgUploadFile",{mini:!0,mobile:!0,pc:function e(t,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.util.dgUploadFile",t)}}),a.version={android:"1.3.2",ios:"1.3.2",pc:"1.3.6"},t.default=a},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(e){return o.default.invoke("uploadFileByType",e)}o.default.registerAPI("uploadFileByType",{mini:!0,mobile:!0}),r.version={android:"1.3.0",ios:"1.3.0"},t.default=r},function(e,t,n){"use strict";var i=this&&this.__assign||function(){return(i=Object.assign||function(e){for(var t,n=1,i=arguments.length;n<i;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},o=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}},r;function a(e){return new Promise((function(t,n){my.uploadFile(i(i({},e),{success:function e(n){t(n)},fail:function e(t){n(t)}}))}))}Object.defineProperty(t,"__esModule",{value:!0}),o(n(0)).default.registerAPI("uploadFile",{mini:!0}),a.version={android:"1.6.2",ios:"1.6.2"},t.default=a},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(e){return o.default.invoke("uploadRemoteFileToDisk",e)}o.default.registerAPI("uploadRemoteFileToDisk",{mini:!0,mobile:!0,pc:function e(t,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.util.uploadRemoteFileToDisk",t)}}),r.version={android:"1.6.0",ios:"1.6.0",pc:"2.6.0"},t.default=r},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(e){return o.default.invoke("ut",e)}o.default.registerAPI("ut",{pc:function e(t,n){window.dingtalk.platform.invokeAPI(n.msgId,"biz.util.ut",t)}}),r.version={pc:"1.3.10"},t.default=r},function(e,t,n){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=i(n(0));function r(e){return o.default.invoke("vibrate",e)}o.default.registerAPI("vibrate",{mini:!0,mobile:!0}),r.version={android:"1.3.1",ios:"1.3.1"},t.default=r}])}));