<!DOCTYPE html>
<html>

<head>
  <meta charset='utf-8'>
  <meta http-equiv='X-UA-Compatible' content='IE=edge'>
  <title>登录中...</title>
  <script src="./index.js"></script>
</head>

<body>
  <!-- 单点登录页面 -->
  <!-- http://***************:8089/ssologin.html?app_code=dee9976ecd2cdea4b338&currentAppList=dee9976ecd2cdea4b338&client=zwdd&zwdd_id=295034&timestamp=1699411948762&itemType=2&redirect=http%253A%252F%252F23.210.52.41%253A80%252Fssologin.html%252Fsendread%252Finform%252Flist
       http://localhost:6080/ssologin.html?app_code=1724361174697779201&ssoLoginUrl=%2Fssologin.html&zwdd_id=10086&timestamp=1699411948762&itemType=2&redirect=%2Fhome -->
  <!-- <div class="loading-screen" id="loadging">
    <div class="loading">
      <span></span>
      <span></span>
      <span></span>
      <span></span>
    </div>
  </div> -->
  <div id="app">
    <div id="first-loading-wrp" class="first-loading-wrp">
      <div class="loading-wrp" style="flex-direction: column">
        <div>
          <img src="/logo.png" style="width: 90px; height: 90px" />
        </div>
        <div style="margin: 20px">
          <span class="dot dot-spin">
            <i></i>
            <i></i>
            <i></i>
            <i></i>
          </span>
        </div>
        <div style="font-size: 24px; color: #333">重庆数字政协</div>

      </div>
    </div>
  </div>
  <div id="notifyNoPermissions" class="notify-no-permissions" style="display: none;">
    <div style=" display: flex; justify-content: center; ">
      <div style="max-width: 50%;text-align: center;">
        尊敬的用户，根据应用权限设置，您没有本应用访问权限。
      </div>
    </div>
  </div>

  <div id="notifyNoPermissionsMessage"
    style="font-size: 24px; overflow: hidden; max-width: 100%; word-wrap: break-word;"></div>

  <div id="showUnitsModal" class="showUnitsModal" style="display: none;">
    <div style="justify-content: center;vertical-align: middle; ">
      <div style="margin-bottom: 20px;">
        <p>尊敬的<span id="userName"></span>用户，请选择你要登录的机构：</p>
        <p></p>
        <p></p>
      </div>
      <div id="radio-group" class="radio-group">
      </div>
    </div>
  </div>  
</body>

</html>

<style>
  
  .showUnitsModal {
      display: none;
      position: fixed;
      top: 50%;
      left: 50%;
      min-width: 400px;
      min-height: 200px;
      transform: translate(-50%, -50%);
      padding: 20px;
      background-color: #fff;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
      border-radius: 8px;
    }
    .unitbutton {
      background-attachment: scroll;
      background-clip: border-box;
      background-color: #038ff8;
      background-image: none;
      background-origin: padding-box;
      background-position-x: 0%;
      background-position-y: 0%;
      background-repeat: repeat;
      background-size: auto;
      border: none;
      border-radius: 5px;
      color: rgb(255, 255, 255);
      cursor: pointer;
      fill: rgb(255, 255, 255);
      font-family: "Segoe UI", "Segoe UI Midlevel", sans-serif;
      font-size: 14px;
      height: 46px;
      line-height: 20px;
      outline-color: rgb(255, 255, 255);
      outline-style: none;
      outline-width: 0px;
      user-select: none;
      vertical-align: middle;
      margin: 10px;
      align-self: center;
      min-width: 80%;
    }
    /* 单选按钮样式，改变颜色为蓝色 */
    .radio-group {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      vertical-align: middle;
    }

    @media (max-width:600px){
      .showUnitsModal {
        min-width: 400px;
      }
      .unitbutton {
        font-size: 30px;
        height: 100px;
      }
    }
</style>

<style>
  .first-loading-wrp {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    /* min-height: 420px; */
    height: 98vh
  }

  .first-loading-wrp>h1 {
    font-size: 128px
  }

  .first-loading-wrp .loading-wrp {
    padding: 98px;
    display: flex;
    justify-content: center;
    align-items: center
  }

  .dot {
    animation: antRotate 1.2s infinite linear;
    transform: rotate(45deg);
    position: relative;
    display: inline-block;
    font-size: 32px;
    width: 32px;
    height: 32px;
    box-sizing: border-box
  }

  .dot i {
    width: 14px;
    height: 14px;
    position: absolute;
    display: block;
    background-color: #1890ff;
    border-radius: 100%;
    transform: scale(.75);
    transform-origin: 50% 50%;
    opacity: .3;
    animation: antSpinMove 1s infinite linear alternate
  }

  .dot i:nth-child(1) {
    top: 0;
    left: 0
  }

  .dot i:nth-child(2) {
    top: 0;
    right: 0;
    -webkit-animation-delay: .4s;
    animation-delay: .4s
  }

  .dot i:nth-child(3) {
    right: 0;
    bottom: 0;
    -webkit-animation-delay: .8s;
    animation-delay: .8s
  }

  .dot i:nth-child(4) {
    bottom: 0;
    left: 0;
    -webkit-animation-delay: 1.2s;
    animation-delay: 1.2s
  }

  @keyframes antRotate {
    to {
      -webkit-transform: rotate(405deg);
      transform: rotate(405deg)
    }
  }

  @-webkit-keyframes antRotate {
    to {
      -webkit-transform: rotate(405deg);
      transform: rotate(405deg)
    }
  }

  @keyframes antSpinMove {
    to {
      opacity: 1
    }
  }

  @-webkit-keyframes antSpinMove {
    to {
      opacity: 1
    }
  }
</style>
<style>
  .button {
    color: #fff;
    background-color: #1677ff;
    border: none;
    font-size: 20px;
    padding: 10px;
    width: 120px;
    border-radius: 10px;
  }

  .notify-no-permissions {
    width: 100%;
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 30px;
    align-items: center;
    flex-direction: row;
    flex-wrap: wrap;
    align-content: center;
  }

  .notify-back {
    margin-top: 30px;
    display: flex;
    justify-content: center;
  }

  * {
    margin: 0;
    padding: 0;
  }

  .loading-screen {
    width: 100%;
    height: 100vh;
    background: white;
    position: fixed;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .loading {
    width: 80px;
    display: flex;
    flex-wrap: wrap;
    animation: rotate 3s linear infinite;
  }

  @keyframes rotate {
    to {
      transform: rotate(360deg);
    }
  }

  .loading span {
    width: 32px;
    height: 32px;
    background: red;
    margin: 4px;
    animation: scale 1.5s linear infinite;
  }

  @keyframes scale {
    50% {
      transform: scale(1.2);
    }
  }

  .loading span:nth-child(1) {
    border-radius: 50% 50% 0 50%;
    background: #e77f67;
    transform-origin: bottom right;
  }

  .loading span:nth-child(2) {
    border-radius: 50% 50% 50% 0;
    background: #778beb;
    transform-origin: bottom left;
    animation-delay: 0.5s;
  }

  .loading span:nth-child(3) {
    border-radius: 50% 0 50% 50%;
    background: #f8a5c2;
    transform-origin: top right;
    animation-delay: 1.5s;
  }

  .loading span:nth-child(4) {
    border-radius: 0 50% 50% 50%;
    background: #f5cd79;
    transform-origin: top left;
    animation: 1s;
  }
</style>
<script>
  // function getQueryString(name) {
  //   let reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
  //   let r = window.location.search.substr(1).match(reg);
  //   if (r != null) {
  //     return unescape(r[2]);
  //   };
  //   return null;
  // }

  let urlParam = window.location.search + window.location.hash;
  function getQueryString(name) {
    let reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
    let r = urlParam.substr(1).match(reg);
    if (r != null) {
      return unescape(r[2]);
    };
    return null;
  }
  let notify = {}
  let auth_code = getQueryString('auth_code') || ''
  const app_code = getQueryString('app_code') || ''
  const zwdd_code = getQueryString('zwdd_code') || ''
  const zwdd_id = getQueryString('zwdd_id') || ''
  const client = getQueryString('client') || ''
  const ssoLoginUrl = getQueryString('ssoLoginUrl') || ''
  const token = getQueryString('token') || ''
  const redirect = getQueryString('redirect') || ''
  const targetUrl = getQueryString('targetUrl') || ''
  const currentAppList = getQueryString('currentAppList') || ''
  const network = getQueryString('network') || ''

  const userOpenId = getQueryString('userOpenId')|| ''
  const unitOpenId = getQueryString('unitOpenId')|| ''

  if (network != undefined) {
    sessionStorage.setItem('network', network)
  }
  // 此处修改接口IP地址 互联网（http://***************:8095/api  用户中心-> http://***************:8090/api） 政务外网（http://************:8090/api）
  // let ip = 'http://***********:8092/api';
  let ip = 'http://***************:8096/api';
  function setToken(token, validTime) {
    const acctoken = JSON.stringify({
      expire: validTime,
      value: token
    })
    localStorage.setItem('access-token', acctoken)
  }

  function getApplicationURLByCode() {
    return get(ip + '/app/sysApplication/appCode/' + app_code, true)
  }

  function showNotify() {
    notify = {}
    // notify.param = {auth_code,currentAppList}
    // notify.url = '/zwdd/zwddLogin'
    // notify.response = response
    // notifyNoPermissions(notify)
  }

  function notifyNoPermissions(notifyJson) {
    document.getElementById("first-loading-wrp").style.display = 'none'
    document.getElementById("notifyNoPermissions").style.display = 'flex'
    // 不需要前端提示时 注释下面三行代码
    if (notifyJson !== null) {
      document.getElementById("notifyNoPermissionsMessage").innerText = JSON.stringify(notifyJson)
    }
  }
  // 创建 MyPromise构造函数
  function promise(fn) {
    var state = 'pending';
    // 声明函数
    var nowResolve = function (arg) { return arg };
    var nowReject = function (arg) { return arg };
    var nextResolve = function (arg) { return arg };
    var nextReject = function (arg) { return arg };
    var catchReject = function (e) { throw e + ' (in promise)' };

    promise.prototype.then = function (res, rej) {
      typeof res === 'function' ? nowResolve = res : '';
      typeof rej === 'function' ? nowReject = rej : '';
      // return 新的对象
      return new promise(function (resolve, reject) {
        // then 中 return 的值传递给下一个 resolve/reject
        nextResolve = resolve;
        // 若是 then 中有 reject 回调函数， 则将 return 的值传递给下一个 resolve， 不然继续传递给reject
        nextReject = typeof rej === 'function' ? resolve : reject;
        // 捕获错误的回调函数
        catchReject = reject;
      });
    }

    promise.prototype.catch = function (fn) {
      return this.then(null, fn);
    }

    // 传值到下一个回调，以及异常捕获
    function tryCatchFn(state, arg) {
      try {
        state === 'fulfilled' ? nextResolve(nowResolve(arg)) : nextReject(nowReject(arg));
      } catch (e) {
        catchReject(e);
      }
    }

    function callback(value) {
      return function (arg) {
        if (state !== 'pending') { return; }
        state = value;
        // 若是传参是 promise 构造器生成的对象，传递对象 resolve 的值
        if (arg instanceof promise) {
          arg.then(function (res) {
            tryCatchFn('fulfilled', res);
          }, function (rej) {
            tryCatchFn('rejected', rej);
          });
          return;
        }
        // 若是是普通的传值，setTimeout 是为了 resolve/reject 同步代码的时候正常工做
        setTimeout(function () {
          tryCatchFn(state, arg);
        });
      }
    }

    fn(
      callback('fulfilled'),
      callback('rejected')
    );
  }

  function get(url, needtoken = false) {
    function ajaxFunction() {
      var xmlHttp;
      try { // Firefox, Opera 8.0+, Safari
        xmlHttp = new XMLHttpRequest();
      } catch (e) {
        try { // Internet Explorer
          xmlHttp = new ActiveXObject("Msxml2.XMLHTTP");
        } catch (e) {
          try {
            xmlHttp = new ActiveXObject("Microsoft.XMLHTTP");
          } catch (e) { }
        }
      }
      return xmlHttp;
    }
    const xhr = ajaxFunction();
    let result = new Promise((resolve, reject) => {
      xhr.onreadystatechange = function () {
        if (xhr.readyState == 4) {
          if (xhr.status == 200 || xhr.status == 304) {
            try {
              resolve(JSON.parse(xhr.response))
            } catch (e) {
              result = new promise((resolve, reject) => {
                xhr.onreadystatechange = function () {
                  if (xhr.readyState == 4) {
                    if (xhr.status == 200 || xhr.status == 304) {
                      try {
                        resolve(JSON.parse(xhr.response))
                      } catch (e) {
                        notifyNoPermissions({message:'返回值解析错误',url:url,env:navigator.userAgent.toLowerCase(),response:xhr.response});
                      }
                    } else {
                      notifyNoPermissions({message:'调用请求失败',url:url,env:navigator.userAgent.toLowerCase()});
                    }
                  }
                }
              })
            }
          } else {
            result = new promise((resolve, reject) => {
              xhr.onreadystatechange = function () {
                if (xhr.readyState == 4) {
                  if (xhr.status == 200 || xhr.status == 304) {
                    try {
                      resolve(JSON.parse(xhr.response))
                    } catch (e) {
                      notifyNoPermissions({message:'返回值解析错误',url:url,env:navigator.userAgent.toLowerCase(),response:xhr.response});
                    }
                  } else {
                    notifyNoPermissions({message:'调用请求失败',url:url,env:navigator.userAgent.toLowerCase()});
                  }
                }
              }
            })
          }
        }
      }
    })
    xhr.open("GET", url, true);
    if (needtoken) {
      xhr.setRequestHeader('token', JSON.parse(localStorage.getItem('access-token')).value)
    }
    xhr.send(null)
    return result
  }

  function loginSuccess(resp) {
    setToken(resp.token, resp.validTime);
    if (client || ssoLoginUrl) {
      let loginUrl = decodeURIComponent(ssoLoginUrl);
      let newUrl = loginUrl + (loginUrl.indexOf("?") > -1 ? "&token=" : "?token=") + resp.token +
        "&redirect=" + decodeURIComponent(redirect) + "&currentAppList=" + currentAppList + "&app_code=" + app_code + "&network=" + network;
      debugger
      window.location.href = loginUrl + (loginUrl.indexOf("?") > -1 ? "&token=" : "?token=") + resp.token +
      "&redirect=" + decodeURIComponent(redirect) + "&currentAppList=" + currentAppList + "&app_code=" + app_code + "&network=" + network;
    } else if (app_code) {
      getApplicationURLByCode(app_code).then((res) => {
        if (res && res.data) {
          let newRedirect = redirect ? redirect : res.data.targetUrl;
          if (res.data.ssoLoginUrl) {
            // window.location.href = res.data.ssoLoginUrl + (res.data.ssoLoginUrl.indexOf("?") > -1 ? "&token=" :
            //   "?token=") +
            //   resp.token + "&targetUrl=" + decodeURIComponent(newRedirect) + "&currentAppList=" + currentAppList + "&app_code=" + app_code + "&network=" + network;
          openModal(res.data.ssoLoginUrl + (res.data.ssoLoginUrl.indexOf("?") > -1 ? "&token=" :
              "?token=") +
              resp.token + "&targetUrl=" + decodeURIComponent(newRedirect) + "&currentAppList=" + currentAppList + "&app_code=" + app_code + "&network=" + network)
            } else {
            // window.location.href = decodeURIComponent(targetUrl) ? decodeURIComponent(newRedirect) : '/home'
            openModal(decodeURIComponent(targetUrl) ? decodeURIComponent(newRedirect) : '/home')
          }
        } else {
          notify = {}
          notify.param = { app_code }
          notify.url = ip + '/getApplicationURLByCode'
          notify.response = res
          notifyNoPermissions(notify)
        }
      });
    } else {
      if (resp.token) {
        // get(ip + `/custom/sso/login?loginCode=${resp.token}&currentAppList=${currentAppList}`).then((response) => {
        //   if (response.code != 0) {
        //     notifyNoPermissions()
        //     return;
        //   }
        //   debugger
        // });
        // 缓存 token
        setToken(res.token, res.validTime);
        window.location.href = decodeURIComponent(targetUrl) ? decodeURIComponent(redirect) : '/home'
      }
    }
  }

  function zwloginById() {
    get(ip + "/zwdd/loginById?zwddId=" + zwdd_id + "&currentAppList=" + currentAppList,).then((response) => {
      if (response.code != 0) {
        notify = {}
        notify.param = { zwdd_id, currentAppList }
        notify.url = ip + '/zwdd/loginById'
        notify.response = response
        notifyNoPermissions(notify)
        return;
      }
      this.loginSuccess(response.data);
    })
  }

  function loginByAuthCode() {

    get(ip + "/zwdd/zwddLogin?auth_code=" + auth_code + "&currentAppList=" + currentAppList,).then((response) => {
      if (response.code != 0) {
        notify = {}
        notify.param = { auth_code, currentAppList }
        notify.url = ip + '/zwdd/zwddLogin'
        notify.response = response
        notifyNoPermissions(notify)
        return;
      }
      this.loginSuccess(response.data);
    })
  }

  function redirectLogin() {
    // window.location.href = '/login'
  }

  function getAllUrlParams(unParams) {
    let newUrl = window.location.search + window.location.hash;
    // 获取URL中的参数部分
    let queryString = newUrl.split('?');
    queryString.splice(0, 1);
    // 将参数部分按照&符号分割成数组
    let params = [];
    queryString.forEach(item => {
      params = params.concat(item.split('&'))
    })
    // 创建一个空对象用于存储参数
    let newParams = [];
    // 遍历参数数组
    params.forEach(function (param) {
      // 将参数按照=符号分割成键值对
      var keyValue = param.split('=');
      if (!unParams.includes(keyValue[0])) {
        newParams.push(param)
      }
    });
    // 输出参数对象
    console.log(newParams.join('&'));
    return newParams.join('&')
  }

  if (token) {
    // loginSuccess({
    //   token: token,
    //   validTime: 3600
    // })
    get(ip + `/custom/sso/login?loginCode=${token}&currentAppList=${currentAppList || app_code}`).then((response) => {
      if (response.code != 0) {
        notify = {}
        currentAppList = currentAppList || app_code
        notify.param = { token, currentAppList }
        notify.url = ip + '/custom/sso/login'
        notify.response = response
        notifyNoPermissions(notify)
        return;
      }
      // 缓存 token
      setToken(response.data.token, response.data.validTime);
      let params = getAllUrlParams(['token', 'redirect'])
      let url = redirect ? decodeURIComponent(redirect) : targetUrl ? decodeURIComponent(targetUrl) : '/home'
      // window.location.href = url + (url.indexOf("?") > -1 ? "&" : "?") + params
      openModal(url + (url.indexOf("?") > -1 ? "&" : "?") + params)
    });
  } else if (zwdd_id) {
    zwloginById();
  } else if (auth_code) {
    loginByAuthCode();
  } else if (app_code) {
    dd.getAuthCode({
      corpId: "",
    }).then((res) => {
      auth_code = res.code;
      loginByAuthCode();
    })
  } else {
    redirectLogin();
  }

  
  // 打开弹框
  function openModal(url) {
    getUserInfo().then(res=>{
      if(res.data && res.data.length > 1){
        document.getElementById('showUnitsModal').style.display = 'block';
        document.getElementById('first-loading-wrp').style.background = '#ddd';
        document.getElementById('userName').innerText = res.data[0].fullname;
        document.getElementById('radio-group').innerHTML = res.data.map(item => `<button class="unitbutton" onClick="openUrl('${item.id}','${url}')" >${item.unitName}-${item.deptName}</button>`).join('');  
      }else if(res.data && res.data.length == 1){
        window.location.href = url
      }else{
        notify = {}
        notify.param = { unitOpenId, userOpenId }
        notify.url = ip + 'getUserInfo'
        notify.response = res
        notifyNoPermissions(notify)
        return;
      }
    })
  }

  // 关闭弹框
  function closeModal() {
    document.getElementById('showUnitsModal').style.display = 'none';
    document.getElementById('first-loading-wrp').style.background = '#FFF';
  }

  function getUserInfo(){
    return get(ip + '/system/SysUnituser/getPermUnituserList?userOpenId=' + userOpenId + '&unitOpenId=' + unitOpenId, true)
  }

  function changeLoginUnit (unitUserId) {
    return get(ip + `/sys/sso/changeUnit/${unitUserId}`,true)
  }

  function openUrl(unitUserId,url){
    closeModal()
    changeLoginUnit(unitUserId).then(response => {
      if (response.code != 0) {
        notifyNoPermissions()
        return;
      }
      // 缓存 token
      setToken(response.data);
      window.location.href = url
    })
  }
</script>