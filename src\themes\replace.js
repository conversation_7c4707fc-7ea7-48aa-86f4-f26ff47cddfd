const replaceFontSize = require('@zh/base-ant/framework/themes/replace-font-size')

/**
 * dir - 指定修改哪些目录中的 vue 文件
 * excludes - 排除文件或目录
 */
const options = [
  //   {
  //   dir: 'node_modules/@zh/base-ant/framework',
  //   excludes: []
  // }, {
  //   dir: 'node_modules/@zh/yunfeng-ant/lib',
  //   excludes: ['']
  // }, {
  //   dir: 'node_modules/@zh/feiliu-ant/lib',
  //   excludes: []
  // }, {
  //   dir: 'node_modules/@zh/components-ant/lib',
  //   excludes: []
  // }, {
  //   dir: 'node_modules/@zh/jingran-ant/lib',
  //   excludes: []
  // }, {
  //   dir: 'D:/Workspace/Vue/zongheng4.0/moli-pc',
  //   excludes: []
  // }
]

/**
 * 修改项目中写死的 px 为 css 变量
 */
replaceFontSize(options)