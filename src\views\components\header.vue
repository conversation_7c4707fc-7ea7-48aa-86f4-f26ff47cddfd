<template>
  <span class="header">{{ name }}</span>
</template>

<script>
export default {
  name: "header",
  data() {
    let name = "统一认证中心";
    let path = window.location.pathname;
    if (path.startsWith("/sendread")) {
      // 送阅
      name = "政协送阅";
    } else if (path.startsWith("/council")) {
      // 委员信息
      name = "政协委员信息";
    } else if (path.startsWith("/jobinfor")) {
      // 政协工作信息
      name = "政协工作信息";
    }
    return {
      name,
    };
  },
  created() {},
  methods: {},
};
</script>

<style lang="less" scoped>
.header {
  font-size: 20px;
  font-weight: bold;
}
</style>
