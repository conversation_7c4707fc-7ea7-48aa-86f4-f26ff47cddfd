<template>
  <div class="cp-page">
    <div class="sear">
      <div>
        <span>加分项名称：</span>
        <a-space>
          <a-input v-model="search.name" placeholder="请输入" style="width: 300px" />
        </a-space>
      </div>

      <div>
        <span>加分项类型：</span>
        <a-space>
          <a-input v-model="search.type" placeholder="请输入" style="width: 300px" />
        </a-space>
      </div>
      <div>
        <a-space>
          <a-button type="primary" @click="getList()">查询</a-button>
          <a-button
            type="primary"
            @click="
              () => {
                search = {};
                getList();
              }
            "
            >重置</a-button
          >
          <a-button type="primary" @click="() => addOpen({})">新增</a-button>
        </a-space>
      </div>
    </div>
    <a-table
      :dataSource="dataSource"
      :columns="columns"
      :pagination="pagination"
      @change="
        (obj) => {
          getList(obj.current, obj.pageSize);
        }
      "
    >
      <span slot="action" slot-scope="text, record">
        <a @click="addOpen(record)">修改</a>
      </span>
    </a-table>
    <AddEdit ref="addEditRef" @callBack="getList" />
  </div>
</template>

<script>
import { auth as http } from "@zh/common-utils/http";
import AddEdit from "./components/addEdit.vue";
export default {
  components: { AddEdit },
  data() {
    return {
      dataSource: [],
      columns: [
        {
          title: "加分项名称",
          dataIndex: "name",
          key: "name",
        },
        {
          title: "加分项类型",
          dataIndex: "type",
          key: "type",
        },
        {
          title: "分值",
          dataIndex: "score",
          key: "score",
        },
        {
          title: "操作",
          width: 80,
          scopedSlots: { customRender: "action" },
        },
      ],
      search: {},
      pageNum: 1,
      pageSize: 10,
      labelCol: { span: 3 },
      wrapperCol: { span: 20 },
      pagination: {},
    };
  },
  // mounted() {
  //   this.getList();
  // },
  activated() {
    this.getList();
  },
  methods: {
    getList(pageNum = 1, pageSize = 10) {
      http
        .post("/bonusFactor/list", {
          ...this.search,
          pageNum: pageNum || this.pageNum,
          pageSize: pageSize || this.pageSize,
        })
        .then((res) => {
          const { currPage, pageSize, list, totalCount } = res;
          this.dataSource = list;
          this.pageNum = currPage;
          this.pageSize = pageSize;
          this.pagination = {
            current: currPage,
            total: totalCount,
          };
        });
    },
    addOpen(record) {
      this.$refs.addEditRef.open(record["id"]);
    },
  },
};
</script>

<style scoped lang="less">
.sear {
  & > div {
    display: inline-block;
    margin-right: 36px;
    margin-bottom: 18px;
  }
}
</style>
