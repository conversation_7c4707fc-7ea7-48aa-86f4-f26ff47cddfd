<template>
  <div class="cp-page page">
    <div class="sear">
      <div>
        <span>统计类型：</span>
        <a-space>
          <zh-dict v-model="search.statisticsType" codeType="CPPCC_TJLX" style="width: 240px" />
        </a-space>
      </div>
      <div>
        <span>年度：</span>
        <a-space>
          <!-- <a-input
            v-model="search.year"
            placeholder="请输入"
            style="width: 240px"
          /> -->
          <a-select v-model="search.year" placeholder="请输入" style="width: 240px">
            <a-select-option v-for="(item, index) in yearData" :key="index" :value="item" :dataRef="item">
              {{ item }}
            </a-select-option>
          </a-select>
        </a-space>
      </div>
      <div>
        <span>季度：</span>
        <a-space>
          <zh-dict v-model="search.quarter" codeType="CPPCC_JD" style="width: 240px" />
        </a-space>
      </div>
      <div>
        <span>报送时间：</span>
        <a-space>
          <a-date-picker v-model="search.startTime" style="width: 240px" />
          <span>至</span>
          <a-date-picker v-model="search.endTime" style="width: 240px" />
        </a-space>
      </div>
    </div>
    <div style="margin-bottom: 18px; text-align: center">
      <a-space>
        <a-button type="primary" @click="getList(1)">查询</a-button>
        <a-button
          type="primary"
          @click="
            () => {
              search = {
                statisticsType: 2,
              };
              statisticsType = 2;
              getList(1);
            }
          "
        >
          重置
        </a-button>
        <a-button type="primary" @click="reportF"> 导出Excel </a-button>
      </a-space>
    </div>
    <table class="table">
      <colgroup width="270"></colgroup>
      <colgroup width="270"></colgroup>
      <colgroup width="270"></colgroup>
      <colgroup width="270"></colgroup>
      <colgroup width="270"></colgroup>
      <colgroup width="270"></colgroup>
      <thead>
        <tr style="font-size: 16px; font-weight: bold">
          <td>报送单位（委员）</td>
          <td>报送数量</td>
          <td>采编数量</td>
          <td>得分情况</td>
          <td>领导批示（人次）</td>
          <td>排名</td>
        </tr>
      </thead>
      <tbody>
        <tr v-for="(item, index) in dataSource" :key="index">
          <td>{{ item["unitName"] }}</td>
          <td @click="addOpen({ unitId: item['unitId'], reportType: item['reportType'], ...search })" style="cursor: pointer">
            <a>{{ item["bsTotal"] }}</a>
          </td>
          <td @click="addOpen({ unitId: item['unitId'], reportType: item['reportType'], ...search, detailType: 1 })" style="cursor: pointer">
            <a>{{ item["cbTotal"] }}</a>
          </td>
          <td>{{ item["dfTotal"] }}</td>
          <td>{{ item["ldpsCount"] }}</td>
          <td>{{ item["ranking"] }}</td>
        </tr>
      </tbody>
    </table>
    <a-pagination v-if="total > 20" :pageSize="20" v-model="current" :total="total" show-less-items style="text-align: right; margin-top: 10px" @change="getList" />

    <AddEdit ref="addEditRef" @callBack="getList" />
  </div>
</template>

<script>
import { auth as http } from "@zh/common-utils/http";
import AddEdit from "./components/addEdit.vue";
import dayjs from "dayjs";
export default {
  components: { AddEdit },
  data() {
    let year = dayjs().year();
    let data = new Array(5).fill("").map((obj, index) => year - index);
    return {
      yearData: data,
      dataSource: [],
      statisticsType: undefined,
      search: {
        statisticsType: 2,
      },
      keyword: undefined,
      pageNum: 1,
      pageSize: 10,
      labelCol: { span: 3 },
      wrapperCol: { span: 20 },
      current: 1,
      total: 0,
    };
  },
  // mounted() {
  //   this.getList();
  // },
  activated() {
    this.getList();
  },
  methods: {
    getList(pageNum = 1) {
      let temp = { ...this.search };
      this.statisticsType = this.search.statisticsType;
      Object.keys(temp).forEach((key) => {
        if (key.includes("Time") && temp[key]) {
          temp[key] = dayjs(temp[key]).valueOf();
        }
      });
      http
        .post("/syReport/collectEditStatistics", {
          pageNum: pageNum,
          pageSize: 20,
          ...temp,
        })
        .then((res) => {
          this.dataSource = res["list"];
          this.current = res["currPage"] || 1;
          this.total = res["totalCount"];
        });
    },
    addOpen(record) {
      let temp = { ...record };
      Object.keys(temp).forEach((key) => {
        if (key.includes("Time") && temp[key]) {
          temp[key] = dayjs(temp[key]).valueOf();
        }
      });
      this.$refs.addEditRef.open(temp);
    },
    reportF() {
      let temp = { ...this.search };
      Object.keys(temp).forEach((key) => {
        if (key.includes("Time") && temp[key]) {
          temp[key] = dayjs(temp[key]).valueOf();
        }
      });
      http
        .post(
          "/syReport/collectEditStatistics/export",
          {
            pageNum: 1,
            pageSize: 999,
            ...temp,
          },
          {
            responseType: "blob",
          }
        )
        .then((res) => {
          console.log("🚀 ~ file: index.vue:156 ~ .then ~ res:", res);
          const blob = res.data;
          const url = window.URL.createObjectURL(blob);

          let link = document.createElement("a");

          link.style.display = "none";

          link.href = url;

          link.setAttribute("download", new Date().valueOf()); //文件名后缀记得添加，我写的zip

          document.body.appendChild(link);

          link.click();

          document.body.removeChild(link); //下载完成移除元素

          window.URL.revokeObjectURL(url); //释放掉blob对象
        });
    },
  },
};
</script>

<style scoped lang="less">
.page {
  width: 1660px;
  margin: auto;
}
.sear {
  & > div {
    display: inline-block;
    margin-right: 36px;
    margin-bottom: 18px;
  }
}
.table {
  margin: auto;
  table {
    table-layout: fixed;
  }

  td {
    border: 1px solid #b7b7b7;
    text-align: center;
    height: 46px;
  }
  thead > tr > td {
    height: 60px;
  }
}
</style>
