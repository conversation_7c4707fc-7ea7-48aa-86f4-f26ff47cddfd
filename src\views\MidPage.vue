<template>
  <div class="middle_page">
    <div class="cmm" @click="() => (password.visible = true)">修改密码</div>

    <div class="tit">重庆市政协港澳委员数字化履职平台</div>
    <div class="menus">
      <div class="menu_item" v-for="item in menus" :key="item.name">
        <img class="icon" :src="item.icon" @click="menuClick(item.key)" />
        <div class="title">{{ item.name }}</div>
      </div>
    </div>
    <div class="menus">
      <div class="menu_item" v-for="item in menus1" :key="item.name">
        <img class="icon" :src="item.icon" @click="menuClick(item.key)" />
        <div class="title">{{ item.name }}</div>
      </div>
    </div>

    <a-modal
      title="修改密码"
      :centered="true"
      :maskClosable="false"
      :visible="password.visible"
      :confirmLoading="password.loading"
      @ok="handleChangePassword"
      @cancel="password.visible = false"
    >
      <a-form-model
        :model="password.form"
        ref="form"
        :label-col="{ span: 5 }"
        :wrapper-col="{ span: 18 }"
        :rules="{
          oldPass: [{ required: true, message: '此项为必填项' }],
          newPass: [
            { validator: validatePass, trigger: 'blur' },
            { required: true, message: '此项为必填项' },
          ],
          mpassword: [
            { validator: validatePass, trigger: 'blur' },
            { required: true, message: '此项为必填项' },
          ],
        }"
      >
        <a-form-model-item label="原密码" prop="oldPass">
          <a-input v-model.trim="password.form.oldPass" type="password" />
        </a-form-model-item>
        <a-form-model-item label="新密码" prop="newPass">
          <a-input v-model.trim="password.form.newPass" type="password" />
        </a-form-model-item>
        <a-form-model-item label="确认密码" prop="mpassword">
          <a-input v-model.trim="password.form.mpassword" type="password" />
        </a-form-model-item>
      </a-form-model>
    </a-modal>
  </div>
</template>

<script>
import { changePassword } from "@zh/base-ant/framework/api/user/index";

export default {
  components: {},
  data() {
    return {
      menus: [
        {
          icon: require("../assets/midpage/sqmy.png"),
          name: "社情民意",
          key: "sqmy",
        },
        {
          icon: require("../assets/midpage/wylz.png"),
          name: "委员履职",
          key: "wylz",
        },
        {
          icon: require("../assets/midpage/tabl.png"),
          name: "提案办理",
          key: "tabl",
        },
        {
          icon: require("../assets/midpage/qwhfy.png"),
          name: "全委会服务",
          key: "qwhfw",
        },
        // {
        //   icon: require("../assets/midpage/yshsl.png"),
        //   name: " 渝事好商量",
        //   key: "yshsl",
        // },
        // {
        //   icon: require('../assets/midpage/wygzs.png'),
        //   name: ' 委员工作室',
        //   key:'wygzs',
        // },
        // {
        //   icon: require('../assets/midpage/wyxx.png'),
        //   name: ' 委员学习',
        //   key:'wyxx',
        // },
        // {
        //   icon: require('../assets/midpage/sy.png'),
        //   name: '  送阅',
        //   key:'sy',
        // }
      ],
      menus1: [
        // {
        //   icon: require('../assets/midpage/sqmy.png'),
        //   name: '社情民意',
        //   key:'sqmy',
        // },
        // {
        //   icon: require('../assets/midpage/wylz.png'),
        //   name: '委员履职',
        //   key:'wylz',
        // },
        // {
        //   icon: require('../assets/midpage/tabl.png'),
        //   name: '提案办理',
        //   key:'tabl',
        // },
        // {
        //   icon: require('../assets/midpage/yshsl.png'),
        //   name: ' 渝事好商量',
        //   key:'yshsl',
        // },
        // {
        //   icon: require("../assets/midpage/wygzs.png"),
        //   name: " 委员工作室",
        //   key: "wygzs",
        // },
        // {
        //   icon: require("../assets/midpage/wyxx.png"),
        //   name: " 委员学习",
        //   key: "wyxx",
        // },
      ],
      visible: false,
      form: this.$form.createForm(this, { name: "coordinated" }),
      password: {
        form: {},
        loading: false,
        visible: false,
      },
    };
  },
  methods: {
    menuClick(type) {
      const token = this.$route.query.token;
      if (type === "sqmy") {
        window.open(`http://szzx.cqzx.gov.cn:8091/authLogin?type=message&token=${token}`);
      }
      if (type === "wylz") {
        window.open(`http://szzx.cqzx.gov.cn:8091/authLogin?type=perform&token=${token}`);
      }
      if (type === "tabl") {
        window.open(`http://szzx.cqzx.gov.cn:8091/authLogin?type=proposal&token=${token}`);
      }
      if (type === "yshsl") {
        window.open(`http://szzx.cqzx.gov.cn:8092/web/#/discuss?token=${token}`);
      }
      if (type === "wygzs") {
        window.open(`http://szzx.cqzx.gov.cn:8092/web/#/studio?token=${token}`);
      }
      if (type === "wyxx") {
        window.open(`http://szzx.cqzx.gov.cn:8092/web/#/study?token=${token}`);
      }
      if (type === "qwhfw") {
        window.open(`http://szzx.cqzx.gov.cn:8091/authLogin?type=meeting&token=${token}`);
      }
    },

    handleChangePassword() {
      this.$refs.form.validate((ok) => {
        if (!ok) {
          return;
        }
        if (this.password.form.newPass != this.password.form.mpassword) {
          this.message.error("确认密码与新密码不一致");
          return;
        }
        this.password.loading = true;
        changePassword(this.password.form)
          .then((response) => {
            this.password.visible = false;
            this.$router.redirectLogin(this.$route.fullpath);
          })
          .finally(() => {
            this.password.loading = false;
          });
      });
    },
  },
};
</script>

<style scoped lang="less">
.middle_page {
  width: 100%;
  height: 100%;
  overflow: hidden;
  background-image: url("../assets/loginbg.jpg");
  background-size: cover;
  background-repeat: no-repeat;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  .tit {
    font-size: 46px;
    color: white;
    letter-spacing: 8px;
  }
  .menus {
    width: 60%;
    display: flex;
    justify-content: space-between;
    margin-top: 80px;
    .menu_item {
      margin: 0 20px;
    }
    .icon {
      width: 130px;
      height: 130px;
      cursor: pointer;
    }
    .title {
      text-align: center;
      margin-top: 10px;
      color: white;
      font-size: 24px;
    }
  }

  .cmm {
    font-size: 18px;
    color: white;
    position: absolute;
    right: 30px;
    top: 80px;
  }
}
</style>
