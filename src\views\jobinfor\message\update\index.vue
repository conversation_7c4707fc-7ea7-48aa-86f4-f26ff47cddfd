<template>
  <div class="cp-page">
    <a-row>
      <a-col span="8" class="sear">
        <span>信息标题：</span>
        <div>
          <a-input
            v-model="search.title"
            placeholder="请输入"
            style="width: 100%"
          />
        </div>
      </a-col>
      <a-col span="8" class="sear">
        <span>报送时间：</span>
        <div>
          <a-date-picker v-model="search.reportStartDate" style="width: 47%" />
          <span style="width: 6%; display: inline-block; text-align: center">
            至
          </span>
          <a-date-picker v-model="search.reportEndDate" style="width: 47%" />
        </div>
      </a-col>
      <a-col span="3" class="sear">
        <span>年号：</span>
        <div>
          <a-input-number v-model="search.oaYear" style="width: 100%" />
        </div>
      </a-col>
      <a-col span="3" class="sear">
        <span>期号：</span>
        <div>
          <a-input-number v-model="search.oaIssue" style="width: 100%" />
        </div>
      </a-col>
    </a-row>

    <div style="text-align: center; margin-bottom: 16px">
      <a-space>
        <a-button type="primary" @click="getList()">查询</a-button>
        <a-button
          type="primary"
          @click="
            () => {
              search = { oaYear: dayjs().year() };
              getList();
            }
          "
          >重置</a-button
        >
      </a-space>
    </div>
    <a-table
      :dataSource="dataSource"
      :columns="columns"
      :pagination="pagination"
      @change="
        (obj) => {
          getList(obj.current, obj.pageSize);
        }
      "
    >
      <a
        slot="name"
        slot-scope="text, record"
        @click="addOpen(record)"
        :title="text"
      >
        {{ text }}
      </a>
      <span slot="oaYear" slot-scope="text, record">
        {{ text ? text + "年" : ""
        }}{{ record["oaIssue"] ? `第${record["oaIssue"]}期` : "" }}
      </span>
      <span slot="reportDate" slot-scope="text">
        {{ text ? dayjs(text).format("YYYY-MM-DD") : text }}
      </span>
      <span slot="action" slot-scope="text, record">
        <a-space>
          <a @click="addOpen(record)">详情</a>
          <a @click="down(record, true)"> 下载 </a>
          <a-popconfirm title="是否确认删除？" @confirm="del(record)">
            <a style="color: red">删除</a>
          </a-popconfirm>
        </a-space>
      </span>
    </a-table>
    <AddEdit ref="addEditRef" @callBack="getList" />
  </div>
</template>

<script>
import { auth as http } from "@zh/common-utils/http";
import AddEdit from "../update/components/addEdit.vue";
import dayjs from "dayjs";
export default {
  components: { AddEdit },
  data() {
    return {
      dayjs,
      dataSource: [{ id: "123456", name: 1231 }],
      columns: [
        {
          title: "信息标题",
          dataIndex: "oaTitle",
          width: 600,
          scopedSlots: { customRender: "name" },
        },
        {
          title: "期号",
          dataIndex: "oaYear",
          scopedSlots: { customRender: "oaYear" },
        },
        {
          title: "发布时间",
          dataIndex: "reportDate",
          key: "reportDate",
          scopedSlots: { customRender: "reportDate" },
        },
        {
          title: "操作",
          key: "action",
          width: 160,
          scopedSlots: { customRender: "action" },
        },
      ],
      search: { oaYear: dayjs().year() },
      keyword: undefined,
      pageNum: 1,
      pageSize: 10,
      labelCol: { span: 3 },
      wrapperCol: { span: 20 },
      pagination: {},
    };
  },
  // mounted() {
  //   this.getList();
  // },
  activated() {
    this.getList();
  },
  methods: {
    getList(pageNum = 1, pageSize = 10) {
      let temp = { ...this.search };
      Object.keys(temp).forEach((key) => {
        if (key.includes("Date") && temp[key]) {
          temp[key] = dayjs(temp[key]).valueOf();
        }
      });
      http
        .post("/workOa/list", {
          ...temp,
          pageType: 3,
          pageNum: pageNum || this.pageNum,
          pageSize: pageSize || this.pageSize,
        })
        .then((res) => {
          const { currPage, pageSize, list, totalCount } = res;
          this.dataSource = list;
          this.pageNum = currPage;
          this.pageSize = pageSize;
          this.pagination = {
            current: currPage,
            total: totalCount,
          };
        });
    },
    addOpen(record) {
      this.$refs.addEditRef.open(record["id"]);
    },
    down(record, isDown = false) {
      // http
      //   .post("/workOa/saveCount", {
      //     ...record,
      //     countType: isDown ? 2 : 1,
      //   })
      //   .then((res) => {});
      if (isDown) {
        let syncFile = record["syncOaFile"];
        if (syncFile) {
          this.base64ToFile(syncFile["file"], syncFile["fileName"]);
        }
      } else {
        window.open(
          process.env.VUE_APP_READ +
            `?file=` +
            process.env.VUE_APP_READ_BASE_URL +
            `/sk/file/getOaJson?fileId=${record["fileId"] || record["id"]}` +
            `%26serviceUrl=${process.env.VUE_APP_READ_BASE_URL}`
        );
      }
    },
    base64ToFile(fileByte, fileName) {
      // 下载文件
      var bstr = atob(fileByte);
      var n = bstr.length;
      var u8arr = new Uint8Array(n);
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n);
      }
      let blob = new Blob([u8arr], {
        type: "",
      });
      if (window.navigator && window.navigator.msSaveOrOpenBlob) {
        window.navigator.msSaveOrOpenBlob(blob, fileName);
      } else {
        let downloadElement = document.createElement("a");
        let href = window.URL.createObjectURL(blob);
        downloadElement.href = href;
        downloadElement.download = fileName;
        document.body.appendChild(downloadElement);
        downloadElement.click();
        document.body.removeChild(downloadElement);
        window.URL.revokeObjectURL(href);
      }
    },
    del(record) {
      http.get(`/workOa/delete?id=${record['id']}`).then(() => {
        this.getList();
      });
    },
  },
};
</script>

<style scoped lang="less">
.sear {
  display: flex;
  align-items: center;
  font-size: 14px;
  & > span:first-child {
    width: 80px;
    text-align: right;
  }
  & > :last-child {
    flex: 1;
  }
}
.ant-row {
  margin-bottom: 16px;
}
</style>
