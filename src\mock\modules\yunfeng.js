export default {
  'true#post#/tableInfo/list': ({ body, url }) => {
    return {
      code: 0,
      data: [{
        dbId: '1552930400647516161', id: '1556576438218395650', tableChName: 'sys_user', tableEnName: 'sys_user'
      }, {
        dbId: '1552930400647516161', id: '1556576438952398850', tableChName: 'test_class', tableEnName: 'test_class'
      }, {
        dbId: '1552930400647516161', id: '1556576439237611521', tableChName: 'test_student', tableEnName: 'test_student'
      }, {
        dbId: '1552930400647516161', id: '1559071970769772545', tableChName: 't_user2', tableEnName: 't_user2'
      }, {
        dbId: '1552930400647516161', id: '1572436450056630274', tableChName: 'auser', tableEnName: 'auser'
      }, {
        dbId: '1552930400647516161', id: '1572436450346037250', tableChName: 'undo_log', tableEnName: 'undo_log'
      }],
      msg: 'success'
    }
  },
  // 表单设计数据
  'true#post#/formDesign/getRealByFormId': ({ body, url }) => {
    return {
      code: 0,
      data: {
        createBy: '10091',
        createName: '系统管理员5',
        deleted: '0',
        formId: '1622772722931138562',
        id: '1622772801045856257',
        published: '0',
        recycleMobile: '',
        recyclePC: '[]',
        serialDesign: "{\"formDesc\":{\"key_1677223793225\":{\"apptype\":\"number-app\",\"labelLayout\":4,\"dataType\":\"num\",\"columnConfig\":{\"field\":{\"conditon\":true,\"precision\":2,\"length\":255,\"type\":\"string\",\"key\":\"key_1677223793225\"}},\"label\":\"数字\",\"sort\":3,\"type\":\"number\",\"required\":false,\"layout\":24,\"vif\":true,\"field\":\"key_1677223793225\",\"operators\":[{\"label\":\"大于\",\"value\":\"gt\"},{\"label\":\"大于等于\",\"value\":\"dategt\"},{\"label\":\"等于\",\"value\":\"equal\"},{\"label\":\"不等于\",\"value\":\"notequal\"},{\"label\":\"小于\",\"value\":\"lt\"},{\"label\":\"小于等于\",\"value\":\"datelt\"},{\"component\":{\"type\":\"\"},\"label\":\"为空\",\"value\":\"null\"},{\"component\":{\"type\":\"\"},\"label\":\"不为空\",\"value\":\"notnull\"}],\"disabled\":false,\"dataConverter\":\"number\",\"group\":\"base\"},\"key_1677223792038\":{\"break\":\"0\",\"apptype\":\"textarea-app\",\"labelLayout\":4,\"dataType\":\"string\",\"columnConfig\":{\"field\":{\"conditon\":false,\"length\":1000,\"type\":\"string\",\"key\":\"key_1677223792038\"}},\"rules\":[{\"trigger\":\"blur\",\"message\":\"不允许输入 \\\\ / : * ? \\\" | < > \",\"validatorFn\":\"function(value){\\n      if(value==null || value===undefined || value===''){\\n        return true\\n      }\\n      return /[^\\\\\\\\/:*?\\\"|<>]+/.test(value)\\n    }\"}],\"label\":\"多行文本\",\"sort\":2,\"type\":\"textarea\",\"selectInfo\":{\"condition\":\"_like\",\"type\":\"textarea\"},\"required\":false,\"attrs\":{\"rows\":4},\"layout\":24,\"vif\":true,\"field\":\"key_1677223792038\",\"operators\":[{\"label\":\"等于\",\"value\":\"equal\"},{\"label\":\"不等于\",\"value\":\"notequal\"},{\"component\":{\"type\":\"\"},\"label\":\"为空\",\"value\":\"null\"},{\"component\":{\"type\":\"\"},\"label\":\"不为空\",\"value\":\"notnull\"}],\"disabled\":false,\"group\":\"base\"},\"key_1675734745798\":{\"break\":\"0\",\"apptype\":\"input-app\",\"labelLayout\":4,\"dataType\":\"string\",\"columnConfig\":{\"field\":{\"conditon\":true,\"length\":255,\"type\":\"string\",\"key\":\"key_1675734745798\"}},\"rules\":[{\"trigger\":\"blur\",\"message\":\"不允许输入 \\\\ / : * ? \\\" | < > \",\"validatorFn\":\"function(value){\\n      if(value==null || value===undefined || value===''){\\n        return true\\n      }\\n      return /[^\\\\\\\\/:*?\\\"|<>]+/.test(value)\\n    }\"}],\"sort\":1,\"label\":\"单行文本\",\"type\":\"input\",\"required\":false,\"layout\":24,\"vif\":true,\"field\":\"key_1675734745798\",\"operators\":[{\"label\":\"等于\",\"value\":\"equal\"},{\"label\":\"不等于\",\"value\":\"notequal\"},{\"component\":{\"type\":\"\"},\"label\":\"为空\",\"value\":\"null\"},{\"component\":{\"type\":\"\"},\"label\":\"不为空\",\"value\":\"notnull\"}],\"disabled\":false,\"group\":\"base\"}},\"formLayout\":\"horizontal\",\"inline\":true,\"labelPosition\":\"left\",\"tableInfo\":{\"tableId\":\"1622772800236396546\",\"tableName\":\"zt_1072452310799482880\"},\"serviceKey\":\"1629018071253430274\",\"title\":[{\"isDisable\":false,\"childSelectLists\":[],\"name\":\"表单控件\",\"orderBy\":0,\"type\":\"control\",\"valueKey\":\"formControl\",\"value\":\"key_1675734745798\"}],\"span\":24,\"order\":[\"key_1675734745798\",\"key_1677223793225\",\"key_1677223792038\"]}",
        serialDesignMobile: '',
        updateBy: '10091',
        updateName: '',
        version: null
      },
      msg: 'success'
    }
  },
  // 列表设计
  'true#post#/list/getOneListDesign': ({ body, url }) => {
    return {
      code: 0,
      data: {
        id: '1625771498365005826',
        listDesign: "{\"columns\":[{\"type\":\"main\",\"title\":\"zt_1072452310799482880\",\"alias\":\"单行文本\",\"align\":\"center\",\"field\":\"key_1675734745798\",\"ordered\":\"0\",\"width\":\"120\",\"name\":\"单行文本\"},{\"type\":\"main\",\"title\":\"zt_1072452310799482880\",\"alias\":\"数字\",\"align\":\"center\",\"field\":\"key_1677223793225\",\"ordered\":\"0\",\"width\":\"120\",\"name\":\"数字\"},{\"type\":\"main\",\"title\":\"zt_1072452310799482880\",\"alias\":\"多行文本\",\"align\":\"center\",\"field\":\"key_1677223792038\",\"ordered\":\"0\",\"width\":\"120\",\"name\":\"多行文本\"}],\"tableSet\":{\"masterTable\":\"zt_1072452310799482880\",\"tableId\":\"1622772800236396546\",\"child\":[],\"key\":\"1629020929633857538\"},\"queryCondition\":[{\"component\":{\"break\":\"0\",\"apptype\":\"input-app\",\"labelLayout\":4,\"dataType\":\"string\",\"columnConfig\":{\"field\":{\"conditon\":true,\"length\":255,\"type\":\"string\",\"key\":\"key_1675734745798\"}},\"rules\":[{\"trigger\":\"blur\",\"message\":\"不允许输入 \\\\ / : * ? \\\" | < > \",\"validatorFn\":\"function(value){\\n      if(value==null || value===undefined || value===''){\\n        return true\\n      }\\n      return /[^\\\\\\\\/:*?\\\"|<>]+/.test(value)\\n    }\"}],\"sort\":1,\"label\":\"单行文本\",\"type\":\"input\",\"required\":false,\"layout\":24,\"vif\":true,\"field\":\"key_1675734745798\",\"operators\":[{\"label\":\"等于\",\"value\":\"equal\"},{\"label\":\"不等于\",\"value\":\"notequal\"},{\"component\":{\"type\":\"\"},\"label\":\"为空\",\"value\":\"null\"},{\"component\":{\"type\":\"\"},\"label\":\"不为空\",\"value\":\"notnull\"}],\"disabled\":false,\"group\":\"base\",\"attrs\":{\"placeholder\":\"请输入\"}},\"condition\":\"\",\"defaultValue\":\"\",\"field\":\"key_1675734745798\",\"hidden\":\"0\",\"name\":\"单行文本\",\"placeholder\":\"请输入\",\"tableId\":\"1622772800236396546\",\"tableName\":\"zt_1072452310799482880\",\"type\":\"main\"}],\"operationSet\":[{\"name\":\"详情\",\"link\":\"/details\",\"state\":true,\"type\":\"1\",\"permission\":{\"type\":1,\"detail\":{\"units\":[],\"roles\":[],\"users\":[]}},\"condition\":[{\"field\":\"\",\"tableName\":\"\",\"value\":\"\",\"condition\":\"\"}]},{\"name\":\"编辑\",\"link\":\"/edit\",\"state\":true,\"type\":\"2\",\"permission\":{\"type\":2,\"detail\":{\"units\":[],\"roles\":[],\"users\":[]}},\"condition\":[{\"field\":\"\",\"tableName\":\"\",\"value\":\"\",\"condition\":\"\"}]},{\"name\":\"删除\",\"link\":\"/delete\",\"state\":true,\"type\":\"3\",\"permission\":{\"type\":1,\"detail\":{\"units\":[],\"roles\":[],\"users\":[]}},\"condition\":[{\"field\":\"\",\"tableName\":\"\",\"value\":\"\",\"condition\":\"\"}]}],\"functionButton\":[{\"name\":\"新增\",\"linkType\":\"1\",\"formLink\":\"\",\"state\":true,\"link\":\"/add\",\"type\":\"1\",\"style\":\"1\",\"permission\":{\"type\":2,\"detail\":{\"units\":[],\"roles\":[],\"users\":[]}},\"condition\":[{\"tableName\":\"\",\"columnName\":\"\",\"calculate\":0,\"component\":{},\"value\":{}}]},{\"name\":\"导出\",\"linkType\":\"1\",\"formLink\":\"\",\"state\":true,\"link\":\"/add\",\"type\":\"2\",\"style\":\"1\",\"permission\":{\"type\":2,\"detail\":{\"units\":[],\"roles\":[],\"users\":[]}},\"condition\":[{\"tableName\":\"\",\"columnName\":\"\",\"calculate\":0,\"component\":{},\"value\":{}}]},{\"name\":\"批量删除\",\"linkType\":\"1\",\"formLink\":\"\",\"state\":true,\"link\":\"/add\",\"type\":\"3\",\"style\":\"1\",\"permission\":{\"type\":2,\"detail\":{\"units\":[],\"roles\":[],\"users\":[]}},\"condition\":[{\"tableName\":\"\",\"columnName\":\"\",\"calculate\":0,\"component\":{},\"value\":{}}]}],\"permission\":[{\"name\":\"管理员\",\"type\":\"1\",\"detail\":{\"units\":[],\"roles\":[],\"users\":[]},\"condition\":[{\"field\":\"\",\"tableName\":\"\",\"value\":\"\",\"condition\":\"\"}]},{\"name\":\"创建人\",\"type\":\"1\",\"detail\":{\"units\":[],\"roles\":[],\"users\":[]},\"condition\":[{\"field\":\"\",\"tableName\":\"\",\"value\":\"\",\"condition\":\"\"}]}],\"order\":[{\"field\":\"id\",\"type\":\"desc\",\"label\":\"id主键\",\"value\":\"id\",\"order\":\"asc\",\"alias\":\"id主键\"}],\"statisticData\":[]}",
        listDesignMobile: "{\"columns\":[{\"field\":\"key_1675734745798\",\"name\":\"(key_1675734745798)单行文本\",\"alias\":\"单行文本\",\"type\":\"main\",\"ordered\":\"0\",\"width\":\"120\",\"align\":\"center\",\"dataType\":\"string\",\"apptype\":\"input-app\",\"isSaved\":true,\"title\":\"zt_1072452310799482880\"}],\"tableSet\":{\"masterTable\":\"zt_1072452310799482880\",\"tableId\":\"1622772800236396546\",\"child\":[],\"key\":\"1629020929633857538\"},\"queryCondition\":[{\"component\":{\"break\":\"0\",\"apptype\":\"input-app\",\"labelLayout\":4,\"dataType\":\"string\",\"columnConfig\":{\"field\":{\"conditon\":true,\"length\":255,\"type\":\"string\",\"key\":\"key_1675734745798\"}},\"rules\":[{\"trigger\":\"blur\",\"message\":\"不允许输入 \\\\ / : * ? \\\" | < > \",\"validatorFn\":\"function(value){\\n      if(value==null || value===undefined || value===''){\\n        return true\\n      }\\n      return /[^\\\\\\\\/:*?\\\"|<>]+/.test(value)\\n    }\"}],\"sort\":1,\"label\":\"单行文本\",\"type\":\"input\",\"required\":false,\"layout\":24,\"vif\":true,\"field\":\"key_1675734745798\",\"operators\":[{\"label\":\"等于\",\"value\":\"equal\"},{\"label\":\"不等于\",\"value\":\"notequal\"},{\"component\":{\"type\":\"\"},\"label\":\"为空\",\"value\":\"null\"},{\"component\":{\"type\":\"\"},\"label\":\"不为空\",\"value\":\"notnull\"}],\"disabled\":false,\"group\":\"base\"},\"condition\":\"\",\"defaultValue\":\"\",\"field\":\"key_1675734745798\",\"hidden\":\"0\",\"name\":\"单行文本\",\"placeholder\":\"请输入\",\"tableId\":\"1622772800236396546\",\"tableName\":\"zt_1072452310799482880\",\"type\":\"main\"}],\"operationSet\":[{\"name\":\"详情\",\"link\":\"/details\",\"state\":true,\"type\":\"1\",\"permission\":{\"type\":1,\"detail\":{\"units\":[],\"roles\":[],\"users\":[]}},\"condition\":[{\"field\":\"\",\"tableName\":\"\",\"value\":\"\",\"condition\":\"\"}]},{\"name\":\"编辑\",\"link\":\"/edit\",\"state\":true,\"type\":\"2\",\"permission\":{\"type\":2,\"detail\":{\"units\":[],\"roles\":[],\"users\":[]}},\"condition\":[{\"field\":\"\",\"tableName\":\"\",\"value\":\"\",\"condition\":\"\"}]},{\"name\":\"删除\",\"link\":\"/delete\",\"state\":true,\"type\":\"3\",\"permission\":{\"type\":1,\"detail\":{\"units\":[],\"roles\":[],\"users\":[]}},\"condition\":[{\"field\":\"\",\"tableName\":\"\",\"value\":\"\",\"condition\":\"\"}]}],\"order\":[{\"field\":\"id\",\"type\":\"desc\",\"label\":\"id主键\",\"value\":\"id\",\"order\":\"asc\",\"alias\":\"id主键\"}],\"permission\":[{\"name\":\"管理员\",\"type\":\"1\",\"detail\":{\"units\":[],\"roles\":[],\"users\":[]},\"condition\":[{\"field\":\"\",\"tableName\":\"\",\"value\":\"\",\"condition\":\"\"}]},{\"name\":\"创建人\",\"type\":\"1\",\"detail\":{\"units\":[],\"roles\":[],\"users\":[]},\"condition\":[{\"field\":\"\",\"tableName\":\"\",\"value\":\"\",\"condition\":\"\"}]}]}",
        listId: '1622772723145048065',
        version: null
      },
      msg: 'success'
    }
  },
  'true#get#/app/user/list': ({ body, url }) => {
    return {
      code: 0,
      data: [
        {
          appUrl: '/app/application/detail/edit?applicationVersionId=1628676132146622465&isEdit=0&isPreview=true',
          deleted: 0,
          groupId: '1603607137936760833',
          icon: 'icon-form-shujutongji',
          iconColor: '#0089ff',
          iconType: '',
          id: '1603607685285040130',
          integrateType: '0',
          name: '二零二二年十二月',
          orderNo: 3685,
          remark: '十二月',
          status: 1,
          url: '/listPreview/1628676132322783233?menuId=1628676132322783233'
        }, {
          appUrl: '/app/application/detail/edit?applicationVersionId=1612994471199256577&isEdit=0&isPreview=true',
          deleted: 0,
          groupId: '1603607137936760833',
          icon: '/storage/download/1605096708117647361',
          iconColor: '#0089ff',
          iconType: '',
          id: '1605096721979867138',
          integrateType: '1',
          name: 'cz',
          orderNo: 1555,
          remark: '',
          status: 1,
          url: '/application/detail/edit?applicationVersionId=1612994471199256577&isEdit=0&isPreview=true'
        }, {
          appUrl: '/app/application/detail/edit?applicationVersionId=1605370239007129601&isEdit=0&isPreview=true',
          deleted: 0,
          groupId: '1603607137936760833',
          icon: '',
          iconColor: '',
          iconType: '',
          id: '1605140282750337025',
          integrateType: '1',
          name: '创建定时任务应用',
          orderNo: 375,
          remark: '',
          status: 1,
          url: '/application/detail/edit?applicationVersionId=1605370239007129601&isEdit=0&isPreview=true'
        }, {
          appUrl: '/app/application/detail/edit?applicationVersionId=1612706739700961282&isEdit=0&isPreview=true',
          deleted: 0,
          groupId: '1602537822986784769',
          icon: '/storage/download/1603664292811399169',
          iconColor: '#0089ff',
          iconType: '',
          id: '1603662712359899137',
          integrateType: '0',
          name: 'cz',
          orderNo: 1440,
          remark: '',
          status: 1,
          url: '/listPreview/1612706739763875842?menuId=1612706739763875842'
        }, {
          appUrl: '/app/application/detail/edit?applicationVersionId=1606110489996169218&isEdit=0&isPreview=true',
          deleted: 0,
          groupId: '1602537822986784769',
          icon: 'icon-form-shenfen',
          iconColor: '#0089ff',
          iconType: '',
          id: '1606108140321902594',
          integrateType: '0',
          name: '1223',
          orderNo: 555,
          remark: '',
          status: 0,
          url: '/listPreview/1606110490063278082?menuId=1606110490063278082'
        }, {
          appUrl: '/app/application/detail/edit?applicationVersionId=1612714236583153666&isEdit=0&isPreview=true',
          deleted: 0,
          groupId: '1602537822986784769',
          icon: 'icon-form-gongwen',
          iconColor: '#0089ff',
          iconType: '',
          id: '1607186834708762626',
          integrateType: '0',
          name: '1226测试应用',
          orderNo: 1455,
          remark: '1226测试应用1226测试应用1226测试应用1226测试应用1226测试应用1226测试应用1226测试应用',
          status: 1,
          url: '/listPreview/1612714236683816963?menuId=1612714236683816963'
        }, {
          appUrl: '/app/application/detail/edit?applicationVersionId=1626096263546023938&isEdit=0&isPreview=true',
          deleted: 0,
          groupId: '1603607137936760833',
          icon: 'icon-form-anquan',
          iconColor: '#0089ff',
          iconType: '',
          id: '1607278980824567809',
          integrateType: '1',
          name: '陈伟',
          orderNo: 3155,
          remark: '',
          status: 1,
          url: '/application/detail/edit?applicationVersionId=1626096263546023938&isEdit=0&isPreview=true'
        }, {
          appUrl: '/app/application/detail/edit?applicationVersionId=1615962251565527042&isEdit=0&isPreview=true',
          deleted: 0,
          groupId: '1603607137936760833',
          icon: 'icon-form-jihua',
          iconColor: '#0089ff',
          iconType: '',
          id: '1610148780055760898',
          integrateType: '0',
          name: '二零二三年一月',
          orderNo: 2230,
          remark: '二零二三年一月的魔粒测试',
          status: 1,
          url: '/listPreview/1615962251771047941?menuId=1615962251771047941'
        }, {
          appUrl: '/app/application/detail/edit?applicationVersionId=1613413495271976962&isEdit=0&isPreview=true',
          deleted: 0,
          groupId: '1602537822986784769',
          icon: 'icon-form-gongwen',
          iconColor: '#0089ff',
          iconType: '',
          id: '1612267842806280193',
          integrateType: '0',
          name: '20230109测试应用',
          orderNo: 1705,
          remark: '20230109测试应用20230109测试应用',
          status: 1,
          url: '/listPreview/1613413495448137730?menuId=1613413495448137730'
        }, {
          appUrl: '/app/application/detail/edit?applicationVersionId=1622892823890153474&isEdit=0&isPreview=true',
          deleted: 0,
          groupId: '1603607137936760833',
          icon: '',
          iconColor: '',
          iconType: '',
          id: '1612725278583361538',
          integrateType: '1',
          name: '测试默认图标',
          orderNo: 2685,
          remark: '',
          status: 1,
          url: '/application/detail/edit?applicationVersionId=1622892823890153474&isEdit=0&isPreview=true'
        }, {
          appUrl: '/app/application/detail/edit?applicationVersionId=1612993965466857473&isEdit=0&isPreview=true',
          deleted: 0,
          groupId: '1603607137936760833',
          icon: '',
          iconColor: '',
          iconType: '',
          id: '1612981541837803522',
          integrateType: '0',
          name: 'cly测试',
          orderNo: 1550,
          remark: '',
          status: 1,
          url: '/listPreview/1612993965546549249?menuId=1612993965546549249'
        }, {
          appUrl: '/app/application/detail/edit?applicationVersionId=1625340331362934785&isEdit=0&isPreview=true',
          deleted: 0,
          groupId: '1603607137936760833',
          icon: '',
          iconColor: '',
          iconType: '',
          id: '1620321592246566913',
          integrateType: '1',
          name: '定时任务应用',
          orderNo: 2980,
          remark: '',
          status: 1,
          url: '/application/detail/edit?applicationVersionId=1625340331362934785&isEdit=0&isPreview=true'
        }, {
          appUrl: '/app/application/detail/edit?applicationVersionId=1629033220513312770&isEdit=0&isPreview=true',
          deleted: 0,
          groupId: '1603607137936760833',
          icon: 'icon-form-renyuanguanli',
          iconColor: '#85c700',
          iconType: '',
          id: '1620706818927910914',
          integrateType: '1',
          name: '二零二三年二月',
          orderNo: 3775,
          remark: '新建任务',
          status: 1,
          url: '/application/detail/edit?applicationVersionId=1629033220513312770&isEdit=0&isPreview=true'
        }
      ],
      msg: 'success'
    }
  },
  'true#post#/tableColumn/list': ({ body, url }) => {
    return {
      code: 0,
      data: [
        {
          id: '1622772800496443394',
          isnull: '0',
          tableId: '1622772800236396546',
          tcChName: '主键',
          tcDatatype: 'bigint',
          tcEnName: 'id'
        }, {
          id: '1622772800584523777',
          isnull: '1',
          tableId: '1622772800236396546',
          tcChName: '单行文本0',
          tcDatatype: 'varchar',
          tcEnName: 'key_1675734745798'
        }, {
          id: '1625771223940198401',
          isnull: '1',
          tableId: '1622772800236396546',
          tcChName: '地址0',
          tcDatatype: 'varchar',
          tcEnName: 'key_1676449657292'
        }, {
          id: '1625771224015695873',
          isnull: '1',
          tableId: '1622772800236396546',
          tcChName: '地址1',
          tcDatatype: 'varchar',
          tcEnName: 'key_16764496572921'
        }, {
          id: '1626096113054511106',
          isnull: '1',
          tableId: '1622772800236396546',
          tcChName: '机构多选0',
          tcDatatype: 'varchar',
          tcEnName: 'key_1676527116028'
        }, {
          id: '1626096113113231361',
          isnull: '1',
          tableId: '1622772800236396546',
          tcChName: '机构多选1',
          tcDatatype: 'varchar',
          tcEnName: 'key_16765271160281'
        }, {
          id: '1626096113184534529',
          isnull: '1',
          tableId: '1622772800236396546',
          tcChName: '机构单选0',
          tcDatatype: 'varchar',
          tcEnName: 'key_1676527118642'
        }, {
          id: '1626096113255837697',
          isnull: '1',
          tableId: '1622772800236396546',
          tcChName: '机构单选1',
          tcDatatype: 'varchar',
          tcEnName: 'key_16765271186421'
        }, {
          id: '1629018071060492289',
          isnull: '1',
          tableId: '1622772800236396546',
          tcChName: '多行文本0',
          tcDatatype: 'varchar',
          tcEnName: 'key_1677223792038'
        }, {
          id: '1629018071169544193',
          isnull: '1',
          tableId: '1622772800236396546',
          tcChName: '数字0',
          tcDatatype: 'varchar',
          tcEnName: 'key_1677223793225'
        }
      ],
      msg: 'success'
    }
  },
  'true#post#/flowDesign/getByParam': ({ body, url }) => {
    return {
      code: 0,
      data: {
        deleted: '0',
        deploymentId: '1629048284221812738',
        flowId: '1629047330307059714',
        flowVO: {
          appUrl: '',
          belongApplication: '表单设计',
          belongOrgan: [],
          belongRole: [],
          code: '1622772722931138562',
          deleted: '0',
          id: '1629047330307059714',
          name: 'ttt',
          published: '1',
          publishedTime: null,
          version: 2,
          webUrl: ''
        },
        id: '1629048284003708929',
        published: '1',
        serialDesign: '{"id":"flow","containerid":"flowdesign_container","width":0,"height":0,"rootModels":{"proc_end_21_1677230460734":{"id":"proc_end_21_1677230460734","x":753,"y":615,"width":50,"height":50,"rotation":0,"scaleX":0,"scaleY":0,"text":"结束","modelType":"EndActivity","leftAnchorPoints":[{"id":"proc_end_21_1677230460734_left_anchor","x":742,"y":634,"type":4,"modelType":"AnchorModel","pModelId":"proc_end_21_1677230460734"}],"rightAnchorPoints":[{"id":"proc_end_21_1677230460734_right_anchor","x":802,"y":634,"type":2,"modelType":"AnchorModel","pModelId":"proc_end_21_1677230460734"}],"topAnchorPoints":[{"id":"proc_end_21_1677230460734_top_anchor","x":772,"y":604,"type":1,"modelType":"AnchorModel","pModelId":"proc_end_21_1677230460734","linkAnchorIds":["step_1677230694989655_end_anchor"]}],"bottomAnchorPoints":[{"id":"proc_end_21_1677230460734_bottom_anchor","x":772,"y":664,"type":3,"modelType":"AnchorModel","pModelId":"proc_end_21_1677230460734"}],"attrs":{"code":"end","id":"proc_end_21_1677230460734","text":" ","icon":"","width":60,"height":20,"borderWeight":1,"borderColor":"#AAAAAA","borderRadius":"35","borderDash":[],"selectedBorderWeight":1,"selectedBorderColor":"#111","selectedBorderRadius":"","selectedBorderDash":[],"fillColor":"#fff","selectedFillColor":"#fff","fontFamily":"PingFangSC-Light","fontColor":"#fff","fontSize":20,"selectedFontFamily":"PingFangSC-Light","selectedFontColor":"#AAAAAA","selectedFontSize":20,"anchorType":1,"anchorDistSize":5,"mouseoverlistener":"","mouseoutlistener":""},"controlType":"1000204","borderWeight":1,"borderColor":"#bdbdbd","fontColor":"#fff","fontSize":12,"fillColor":"#9fa2a8","borderRadius":"35","borderDash":[],"selectedBorderWeight":1,"selectedBorderColor":"#bdbdbd","selectedFontColor":"#fff","selectedFontSize":12,"selectedFillColor":"#9fa2a8","anchorDistSize":5},"proc_act33_1677230460733":{"id":"proc_act33_1677230460733","x":668,"y":120,"width":220,"height":80,"rotation":0,"scaleX":0,"scaleY":0,"text":"开始","modelType":"Activity3","leftAnchorPoints":[{"id":"proc_act33_1677230460733_left_anchor","x":657,"y":154,"type":4,"modelType":"AnchorModel","pModelId":"proc_act33_1677230460733"}],"rightAnchorPoints":[{"id":"proc_act33_1677230460733_right_anchor","x":887,"y":154,"type":2,"modelType":"AnchorModel","pModelId":"proc_act33_1677230460733"}],"topAnchorPoints":[{"id":"proc_act33_1677230460733_top_anchor","x":772,"y":109,"type":1,"modelType":"AnchorModel","pModelId":"proc_act33_1677230460733"}],"bottomAnchorPoints":[{"id":"proc_act33_1677230460733_bottom_anchor","x":772,"y":199,"type":3,"modelType":"AnchorModel","pModelId":"proc_act33_1677230460733","linkAnchorIds":["step_1619610869274_start_anchor"]}],"attrs":{"code":"","id":"1551899237091024898","text":"开始","time":"2020-1-2","width":220,"height":80,"borderWeight":0.2,"borderColor":"#AA78D1","borderRadius":"5","borderDash":[],"selectedBorderWeight":1,"selectedBorderColor":"#576A95","selectedBorderRadius":"5","selectedBorderDash":[],"fillColor":"#576A95","selectedFillColor":"#576A95","fontFamily":"PingFangSC-Light","fontColor":"#FFFFFF","fontSize":12,"selectedFontFamily":"PingFangSC-Light","selectedFontColor":"#FFFFFF","selectedFontSize":12,"anchorType":1,"operation":[{"type":"start","label":"开始流程","orderNo":1,"comment":[]}],"anchorDistSize":5,"captionIcon":"","formPermission":"","appUrl":"","belongApplication":"","belongOrgan":"","belongRole":"","deleted":"0","name":"","published":"1","publishedTime":null,"version":1,"webUrl":""},"controlType":"1000205","borderColor":"#AA78D1","fontColor":"#FFFFFF","fontSize":12,"fillColor":"#576A95","borderRadius":"5","borderDash":[],"selectedBorderWeight":1,"selectedBorderColor":"#576A95","selectedFontColor":"#FFFFFF","selectedFontSize":12,"selectedBorderRadius":"5","selectedFillColor":"#576A95","anchorDistSize":5},"btn_addact_41_1619610865513":{"id":"btn_addact_41_1619610865513","x":763,"y":265,"width":30,"height":30,"rotation":0,"scaleX":0,"scaleY":0,"text":"+","modelType":"AddActivityButton","leftAnchorPoints":[{"id":"btn_addact_41_1619610865513_left_anchor","x":752,"y":274,"type":4,"modelType":"AnchorModel","pModelId":"btn_addact_41_1619610865513"}],"rightAnchorPoints":[{"id":"btn_addact_41_1619610865513_right_anchor","x":792,"y":274,"type":2,"modelType":"AnchorModel","pModelId":"btn_addact_41_1619610865513"}],"topAnchorPoints":[{"id":"btn_addact_41_1619610865513_top_anchor","x":772,"y":254,"type":1,"modelType":"AnchorModel","pModelId":"btn_addact_41_1619610865513","linkAnchorIds":["step_1619610869274_end_anchor"]}],"bottomAnchorPoints":[{"id":"btn_addact_41_1619610865513_bottom_anchor","x":772,"y":294,"type":3,"modelType":"AnchorModel","pModelId":"btn_addact_41_1619610865513","linkAnchorIds":["step_16588918774939115_start_anchor"]}],"attrs":{"code":"addact","id":"btn_addact_41_1619610865513","text":"+","icon":"","width":60,"height":60,"borderWeight":1,"borderColor":"#187CFF","borderRadius":"","borderDash":[],"selectedBorderWeight":1,"selectedBorderColor":"#187CFF","selectedBorderRadius":"","selectedBorderDash":[],"fillColor":"#187CFF","selectedFillColor":"#187CFF","fontFamily":"PingFangSC-Light","fontColor":"#fff","fontSize":12,"selectedFontFamily":"PingFangSC-Light","selectedFontColor":"#fff","selectedFontSize":12,"anchorType":1,"anchorDistSize":5,"mouseoverlistener":"","mouseoutlistener":""},"controlType":"1000401","borderWeight":1,"borderColor":"#187CFF","fontColor":"#fff","fontSize":12,"fillColor":"#187CFF","borderDash":[],"selectedBorderWeight":1,"selectedBorderColor":"#187CFF","selectedFontColor":"#fff","selectedFontSize":12,"selectedFillColor":"#187CFF","anchorDistSize":5},"step_1619610869274":{"id":"step_1619610869274","anchorList":[{"id":"step_1619610869274_start_anchor","x":772,"y":199,"type":0,"modelType":"AnchorModel","pModelId":"step_1619610869274","relinkAnchorId":"proc_act33_1677230460733_bottom_anchor"},{"id":"btn_addact_41_1619610865513_mid","x":772,"y":199,"type":0,"modelType":"AnchorModel","pModelId":"step_1619610869274"},{"id":"step_1619610869274_end_anchor","x":772,"y":254,"type":0,"modelType":"AnchorModel","pModelId":"step_1619610869274","relinkAnchorId":"btn_addact_41_1619610865513_top_anchor"}],"modelType":"SequenceModel","lineType":1,"attrs":{"lineType":1},"controlType":"1000101"},"step_16588918774939115":{"id":"step_16588918774939115","text":" ","anchorList":[{"id":"step_16588918774939115_start_anchor","x":772,"y":294,"type":0,"modelType":"AnchorModel","pModelId":"step_16588918774939115","relinkAnchorId":"btn_addact_41_1619610865513_bottom_anchor"},{"id":"proc_act33_16588918762898886_mid","x":772,"y":294,"type":0,"modelType":"AnchorModel","pModelId":"step_16588918774939115"},{"id":"step_16588918774939115_end_anchor","x":772,"y":349,"type":0,"modelType":"AnchorModel","pModelId":"step_16588918774939115","relinkAnchorId":"proc_act33_16772306949806404_top_anchor"}],"modelType":"SequenceModel","lineType":1,"selectedLineColor":"#AAAAAA","attrs":{"lineType":1,"code":"step_16588918774939115","id":"step_16588918774939115","text":" ","lineWeight":2,"lineColor":"#8bc34a","selectedLineColor":"#AAAAAA","fillColor":"#AAAAAA","selectedFillColor":"#AAAAAA","fontFamily":"PingFangSC-Light","fontColor":"#999999","fontSize":12,"selectedFontFamily":"PingFangSC-Light","selectedFontColor":"#FFFFFF","selectedFontSize":12,"lineDash":[],"lineOpacity":1,"descText":"","Text1":"3","setTime":""},"controlType":"1000101","fontColor":"#999999","fontSize":12,"fillColor":"#AAAAAA","selectedFontColor":"#FFFFFF","selectedFontSize":12,"selectedFillColor":"#AAAAAA"},"proc_act33_16772306949806404":{"id":"proc_act33_16772306949806404","x":668,"y":360,"width":220,"height":80,"rotation":0,"scaleX":0,"scaleY":0,"text":"审批","modelType":"Activity3","leftAnchorPoints":[{"id":"proc_act33_16772306949806404_left_anchor","x":657,"y":394,"type":4,"modelType":"AnchorModel","pModelId":"proc_act33_16772306949806404"}],"rightAnchorPoints":[{"id":"proc_act33_16772306949806404_right_anchor","x":887,"y":394,"type":2,"modelType":"AnchorModel","pModelId":"proc_act33_16772306949806404"}],"topAnchorPoints":[{"id":"proc_act33_16772306949806404_top_anchor","x":772,"y":349,"type":1,"modelType":"AnchorModel","pModelId":"proc_act33_16772306949806404","linkAnchorIds":["step_16588918774939115_end_anchor"]}],"bottomAnchorPoints":[{"id":"proc_act33_16772306949806404_bottom_anchor","x":772,"y":439,"type":3,"modelType":"AnchorModel","pModelId":"proc_act33_16772306949806404","linkAnchorIds":["step_16772306949868619_start_anchor"]}],"attrs":{"undefined":"","width":220,"height":80,"borderWeight":0.2,"borderColor":"#AA78D1","borderRadius":"5","borderDash":[],"selectedBorderWeight":1,"selectedBorderColor":"#1890FF","selectedBorderRadius":"5","selectedBorderDash":[],"fillColor":"#1890FF","selectedFillColor":"#1890FF","fontFamily":"PingFangSC-Light","fontColor":"#FFFFFF","fontSize":12,"selectedFontFamily":"PingFangSC-Light","selectedFontColor":"#FFFFFF","selectedFontSize":12,"anchorType":1,"anchorDistSize":5,"captionIcon":"","baseGrup":"","text":"审批","code":"task_1677230697154","customCode":"","scope":[{"type":"user","typeName":"人员","users":[{"id":"10091","name":"系统管理员5","orderNo":1,"def":false,"locked":false}],"orderNo":1}],"scopeText":"处理人","operation":[{"type":"next","label":"同意","comment":[{"label":"文本意见","type":"text","required":false,"tag":"意见","orderNo":1}],"copyInfo":{"open":true,"copyScope":[],"copyScopeText":"抄送人","copyCommont":[]},"orderNo":1}],"businessRuleGrup":"","businessRule":"","taskFromJurisdiction":""},"controlType":"1000206","borderColor":"#AA78D1","fontColor":"#FFFFFF","fontSize":12,"fillColor":"#1890FF","borderRadius":"5","borderDash":[],"selectedBorderWeight":1,"selectedBorderColor":"#1890FF","selectedFontColor":"#FFFFFF","selectedFontSize":12,"selectedBorderRadius":"5","selectedFillColor":"#1890FF","anchorDistSize":5},"btn_addact_41_1677230694984103":{"id":"btn_addact_41_1677230694984103","x":763,"y":505,"width":30,"height":30,"rotation":0,"scaleX":0,"scaleY":0,"text":"+","modelType":"AddActivityButton","leftAnchorPoints":[{"id":"btn_addact_41_1677230694984103_left_anchor","x":752,"y":514,"type":4,"modelType":"AnchorModel","pModelId":"btn_addact_41_1677230694984103"}],"rightAnchorPoints":[{"id":"btn_addact_41_1677230694984103_right_anchor","x":792,"y":514,"type":2,"modelType":"AnchorModel","pModelId":"btn_addact_41_1677230694984103"}],"topAnchorPoints":[{"id":"btn_addact_41_1677230694984103_top_anchor","x":772,"y":494,"type":1,"modelType":"AnchorModel","pModelId":"btn_addact_41_1677230694984103","linkAnchorIds":["step_16772306949868619_end_anchor"]}],"bottomAnchorPoints":[{"id":"btn_addact_41_1677230694984103_bottom_anchor","x":772,"y":534,"type":3,"modelType":"AnchorModel","pModelId":"btn_addact_41_1677230694984103","linkAnchorIds":["step_1677230694989655_start_anchor"]}],"attrs":{"code":"addact","id":"btn_addact_41_1677230694984103","text":"+","icon":"","width":30,"height":30,"borderWeight":1,"borderColor":"#187CFF","borderRadius":"","borderDash":[],"selectedBorderWeight":1,"selectedBorderColor":"#187CFF","selectedBorderRadius":"","selectedBorderDash":[],"fillColor":"#187CFF","selectedFillColor":"#187CFF","fontFamily":"PingFangSC-Light","fontColor":"#fff","fontSize":12,"selectedFontFamily":"PingFangSC-Light","selectedFontColor":"#fff","selectedFontSize":12,"anchorType":1,"anchorDistSize":5,"mouseoverlistener":"","mouseoutlistener":""},"controlType":"1000401","borderWeight":1,"borderColor":"#187CFF","fontColor":"#fff","fontSize":12,"fillColor":"#187CFF","borderDash":[],"selectedBorderWeight":1,"selectedBorderColor":"#187CFF","selectedFontColor":"#fff","selectedFontSize":12,"selectedFillColor":"#187CFF","anchorDistSize":5},"step_16772306949868619":{"id":"step_16772306949868619","text":" ","anchorList":[{"id":"step_16772306949868619_start_anchor","x":772,"y":439,"type":0,"modelType":"AnchorModel","pModelId":"step_16772306949868619","relinkAnchorId":"proc_act33_16772306949806404_bottom_anchor"},{"id":"btn_addact_41_1619610865513_mid","x":772,"y":439,"type":0,"modelType":"AnchorModel","pModelId":"step_16772306949868619"},{"id":"step_16772306949868619_end_anchor","x":772,"y":494,"type":0,"modelType":"AnchorModel","pModelId":"step_16772306949868619","relinkAnchorId":"btn_addact_41_1677230694984103_top_anchor"}],"modelType":"SequenceModel","lineType":1,"selectedLineColor":"#AAAAAA","attrs":{"lineType":1,"code":"step_16772306949868619","id":"step_16772306949868619","text":" ","lineWeight":2,"lineColor":"#8bc34a","selectedLineColor":"#AAAAAA","fillColor":"#AAAAAA","selectedFillColor":"#AAAAAA","fontFamily":"PingFangSC-Light","fontColor":"#999999","fontSize":12,"selectedFontFamily":"PingFangSC-Light","selectedFontColor":"#FFFFFF","selectedFontSize":12,"lineDash":[],"lineOpacity":1,"descText":"","Text1":"3","setTime":""},"controlType":"1000101","fontColor":"#999999","fontSize":12,"fillColor":"#AAAAAA","selectedFontColor":"#FFFFFF","selectedFontSize":12,"selectedFillColor":"#AAAAAA"},"step_1677230694989655":{"id":"step_1677230694989655","text":" ","anchorList":[{"id":"step_1677230694989655_start_anchor","x":772,"y":534,"type":0,"modelType":"AnchorModel","pModelId":"step_1677230694989655","relinkAnchorId":"btn_addact_41_1677230694984103_bottom_anchor"},{"id":"btn_addact_41_1619610865513_mid","x":772,"y":534,"type":0,"modelType":"AnchorModel","pModelId":"step_1677230694989655"},{"id":"step_1677230694989655_end_anchor","x":772,"y":604,"type":0,"modelType":"AnchorModel","pModelId":"step_1677230694989655","relinkAnchorId":"proc_end_21_1677230460734_top_anchor"}],"modelType":"SequenceModel","lineType":1,"selectedLineColor":"#AAAAAA","attrs":{"lineType":1,"code":"step_1677230694989655","id":"step_1677230694989655","text":" ","lineWeight":2,"lineColor":"#8bc34a","selectedLineColor":"#AAAAAA","fillColor":"#AAAAAA","selectedFillColor":"#AAAAAA","fontFamily":"PingFangSC-Light","fontColor":"#999999","fontSize":12,"selectedFontFamily":"PingFangSC-Light","selectedFontColor":"#FFFFFF","selectedFontSize":12,"lineDash":[],"lineOpacity":1,"descText":"","Text1":"3","setTime":""},"controlType":"1000101","fontColor":"#999999","fontSize":12,"fillColor":"#AAAAAA","selectedFontColor":"#FFFFFF","selectedFontSize":12,"selectedFillColor":"#AAAAAA"}},"attrs":{"id":"1629047330307059714","text":"","canvasBgColor":"#F5F5F7","canvasWidth":2000,"canvasHeight":10000,"graphdata":"{\\"id\\":\\"proc_act33_1677230460733\\",\\"type\\":\\"begin\\",\\"nexts\\":[{\\"id\\":\\"btn_addact_41_1619610865513\\",\\"type\\":\\"addactivity\\",\\"nexts\\":[{\\"data\\":\\"审批\\",\\"id\\":\\"proc_act33_16772306949806404\\",\\"type\\":\\"activity\\",\\"nexts\\":[{\\"data\\":\\"＋\\",\\"id\\":\\"btn_addact_41_1677230694984103\\",\\"type\\":\\"addactivity\\",\\"nexts\\":[{\\"id\\":\\"proc_end_21_1677230460734\\",\\"type\\":\\"end\\",\\"nexts\\":[]}]}]}]}]}","quickmenuconfig":"{\\"weight\\":100,\\"col\\":3,\\"menus\\":[{\\"name\\":\\"普通任务\\",\\"icon\\":\\"flow-menu-human-noselect\\",\\"selectedIcon\\":\\"flow-menu-human-selected\\",\\"action\\":\\"quickCreateProcessActivity\\",\\"fillColor\\":\\"#FFFFFF\\",\\"selectedFillColor\\":\\"#3296FA\\"},{\\"name\\":\\"会签任务\\",\\"icon\\":\\"flow-menu-human-noselect\\",\\"selectedIcon\\":\\"flow-menu-human-selected\\",\\"action\\":\\"quickCreateCountersignActivity\\",\\"fillColor\\":\\"#FFFFFF\\",\\"selectedFillColor\\":\\"#3296FA\\"},{\\"name\\":\\"抄送任务\\",\\"icon\\":\\"flow-menu-human-noselect\\",\\"selectedIcon\\":\\"flow-menu-human-selected\\",\\"action\\":\\"quickCreateCopyTaskActivity\\",\\"fillColor\\":\\"#FFFFFF\\",\\"selectedFillColor\\":\\"#3296FA\\"},{\\"name\\":\\"条件分支\\",\\"icon\\":\\"flow-menu-notice-noselect\\",\\"selectedIcon\\":\\"flow-menu-notice-selected\\",\\"action\\":\\"quickCreateSwitchActivity\\",\\"fillColor\\":\\"#FFFFFF\\",\\"selectedFillColor\\":\\"#3296FA\\"},{\\"name\\":\\"子流程\\",\\"icon\\":\\"flow-menu-human-noselect\\",\\"selectedIcon\\":\\"flow-menu-human-selected\\",\\"action\\":\\"quickCreateSubprocessActivity\\",\\"fillColor\\":\\"#FFFFFF\\",\\"selectedFillColor\\":\\"#3296FA\\"}]}","belongApplication":"表单设计","appUrl":"","belongOrgan":"","belongRole":"","code":"1622772722931138562","deleted":"0","name":"ttt","published":"1","publishedTime":null,"version":1,"webUrl":"","describe":""},"controlType":"999"}',
        version: 2
      }
    }
  },
  'true#post#/formDesign/saveNew' ({ body, url }) {
    return {
      code: 0,
      data: '12345678'
    }
  },
  'true#post#/table/getDataByPage' ({ body, url }) {
    return {
      code: 0,
      data: {
        countId: '',
        current: '1',
        hitCount: false,
        maxLimit: null,
        optimizeCountSql: true,
        orders: [],
        pages: '1',
        records: [{
          zt_record_createunitid: '1463836065650294785',
          zt_1072452310799482880_key_1676527116028: ',1463836065650294785,1602880495845175298,1602538318413778945,',
          zt_1072452310799482880_key_16764496572921: '北京市 / 市辖区 / 东城区',
          zt_record_createname: '系统管理员5',
          zt_record_createdeptid: '1463836065650294785',
          zt_record_recordid: '1626096451790696450',
          zt_1072452310799482880_id: '1626096451790696450',
          zt_record_createtime: '2023-02-16 14:57:41.0',
          zt_record_id: '1626096451836833794',
          zt_record_createunitname: '重庆市公安局',
          zt_record_createpid: '0',
          zt_record_createby: '10091',
          zt_1072452310799482880_key_1675734745798: '66',
          zt_1072452310799482880_key_16765271160281: ',重庆市公安局,南华中天,NjTest,',
          zt_1072452310799482880_key_16765271186421: ',,,,NjTest,,,,',
          zt_record_createdeptname: '重庆市公安局',
          zt_record_tablename: 'zt_1072452310799482880',
          zt_record_datatitle: '66',
          zt_1072452310799482880_key_1676527118642: ',1613466706934611970,',
          zt_1072452310799482880_key_1676449657292: '{"regions":[{"code":"110000000000","pcode":"0","name":"北京市"},{"code":"110100000000","pcode":"110000000000","name":"市辖区"},{"code":"110101000000","pcode":"110100000000","name":"东城区"}]}'
        }, {
          zt_record_createunitid: '1463836065650294785',
          zt_1072452310799482880_key_16764496572921: '天津市 / 市辖区 / 河西区',
          zt_record_createname: '系统管理员5',
          zt_record_createdeptid: '1463836065650294785',
          zt_record_recordid: '1625773928708435969',
          zt_1072452310799482880_id: '1625773928708435969',
          zt_record_createtime: '2023-02-15 16:28:05.0',
          zt_record_id: '1625773928767156225',
          zt_record_createunitname: '重庆市公安局',
          zt_record_createpid: '0',
          zt_record_createby: '10091',
          zt_1072452310799482880_key_1675734745798: 'uuu8',
          zt_record_createdeptname: '重庆市公安局',
          zt_record_tablename: 'zt_1072452310799482880',
          zt_record_datatitle: 'uuu8',
          zt_1072452310799482880_key_1676449657292: '{"regions":[{"code":"120000000000","pcode":"0","name":"天津市"},{"code":"120100000000","pcode":"120000000000","name":"市辖区"},{"code":"120103000000","pcode":"120100000000","name":"河西区"}]}'
        }, {
          zt_record_createunitid: '1463836065650294785',
          zt_record_createname: '系统管理员5',
          zt_record_createdeptid: '1463836065650294785',
          zt_record_recordid: '1625772751568629762',
          zt_1072452310799482880_id: '1625772751568629762',
          zt_record_createtime: '2023-02-15 16:23:24.0',
          zt_record_id: '1625772751581212674',
          zt_record_createunitname: '重庆市公安局',
          zt_record_createpid: '0',
          zt_record_createby: '10091',
          zt_1072452310799482880_key_1675734745798: '66',
          zt_record_createdeptname: '重庆市公安局',
          zt_record_tablename: 'zt_1072452310799482880',
          zt_record_datatitle: '66',
          zt_1072452310799482880_key_1676449657292: '{"regions":[{"code":"130000000000","pcode":"0","name":"河北省"},{"code":"130300000000","pcode":"130000000000","name":"秦皇岛市"},{"code":"130303000000","pcode":"130300000000","name":"山海关区"}]}'
        }, {
          zt_record_createunitid: '1463836065650294785',
          zt_record_createname: '系统管理员5',
          zt_record_createdeptid: '1463836065650294785',
          zt_record_recordid: '1625772109408104449',
          zt_1072452310799482880_id: '1625772109408104449',
          zt_record_createtime: '2023-02-15 16:20:51.0',
          zt_record_id: '1625772109420687362',
          zt_record_createunitname: '重庆市公安局',
          zt_record_createpid: '0',
          zt_record_createby: '10091',
          zt_1072452310799482880_key_1675734745798: 'uuu',
          zt_record_createdeptname: '重庆市公安局',
          zt_record_tablename: 'zt_1072452310799482880',
          zt_record_datatitle: 'uuu',
          zt_1072452310799482880_key_1676449657292: '{"regions":[{"code":"120000000000","pcode":"0","name":"天津市"},{"code":"120100000000","pcode":"120000000000","name":"市辖区"},{"code":"120102000000","pcode":"120100000000","name":"河东区"}]}'
        }, {
          zt_record_createunitid: '1463836065650294785',
          zt_record_createname: '系统管理员5',
          zt_record_createdeptid: '1463836065650294785',
          zt_record_recordid: '1625771940671254529',
          zt_1072452310799482880_id: '1625771940671254529',
          zt_record_createtime: '2023-02-15 16:20:11.0',
          zt_record_id: '1625771940792889346',
          zt_record_createunitname: '重庆市公安局',
          zt_record_createpid: '0',
          zt_record_createby: '10091',
          zt_1072452310799482880_key_1675734745798: 'ii',
          zt_record_createdeptname: '重庆市公安局',
          zt_record_tablename: 'zt_1072452310799482880',
          zt_record_datatitle: 'ii',
          zt_1072452310799482880_key_1676449657292: '{"regions":[{"code":"110000000000","pcode":"0","name":"北京市"},{"code":"110100000000","pcode":"110000000000","name":"市辖区"},{"code":"110102000000","pcode":"110100000000","name":"西城区"}]}'
        }, {
          zt_record_createunitid: '1463836065650294785',
          zt_1072452310799482880_key_16764496572921: '北京市 / 市辖区 / 东城区',
          zt_record_createname: '系统管理员5',
          zt_record_createdeptid: '1463836065650294785',
          zt_record_recordid: '1625771637649567746',
          zt_1072452310799482880_id: '1625771637649567746',
          zt_record_createtime: '2023-02-15 16:18:58.0',
          zt_record_id: '1625771637695705090',
          zt_record_createunitname: '重庆市公安局',
          zt_record_createpid: '0',
          zt_record_createby: '10091',
          zt_1072452310799482880_key_1675734745798: '435',
          zt_record_createdeptname: '重庆市公安局',
          zt_record_tablename: 'zt_1072452310799482880',
          zt_record_datatitle: '435',
          zt_1072452310799482880_key_1676449657292: '{"regions":[{"code":"110000000000","pcode":"0","name":"北京市"},{"code":"110100000000","pcode":"110000000000","name":"市辖区"},{"code":"110101000000","pcode":"110100000000","name":"东城区"}]}'
        }
        ],
        searchCount: true,
        size: '10',
        total: '6'
      },
      msg: 'success'
    }
  },
  'true#post#/formDesign/getByParam' (body, url) {
    return {
      code: 0,
      data: {
        createBy: '1569682398222860290',
        createName: '',
        deleted: '0',
        formId: '1572396470533902337',
        id: '1572420177771937793',
        published: '0',
        recycleMobile: "[{\"_colAttrs\":{\"span\":24},\"_required\":false,\"rules\":[{\"trigger\":\"blur\",\"message\":\"不允许输入 \\\\ / : * ? \\\" | < > \",\"validatorFn\":\"function(value){\\n      if(value==null || value===undefined || value===''){\\n        return true\\n      }\\n      return /[^\\\\\\\\/:*?\\\"|<>]+/.test(value)\\n    }\"}],\"_attrs\":{\"rows\":4},\"type\":\"textarea\",\"selectInfo\":{\"condition\":\"_like\",\"type\":\"textarea\"},\"required\":false,\"_label\":\"多行文本\",\"_disabled\":false,\"vif\":true,\"operators\":[{\"label\":\"等于\",\"value\":\"equal\"},{\"label\":\"不等于\",\"value\":\"notequal\"},{\"component\":{\"type\":\"\"},\"label\":\"为空\",\"value\":\"null\"},{\"component\":{\"type\":\"\"},\"label\":\"不为空\",\"value\":\"notnull\"}],\"_optionsLinkageFields\":[],\"disabled\":false,\"group\":\"base\",\"_oldValue\":{},\"break\":\"0\",\"apptype\":\"textarea-app\",\"dataType\":\"string\",\"columnConfig\":{\"field\":{\"conditon\":false,\"length\":255,\"type\":\"string\",\"key\":\"key_1666255816708\"}},\"_type\":\"yf-textarea-app\",\"label\":\"多行文本\",\"sort\":2,\"attrs\":{\"rows\":4},\"layout\":24,\"field\":\"key_1666255816708\",\"_vif\":true}]",
        recyclePC: '[]',
        serialDesign: "{\"formDesc\":{\"key_1676616954901\":{\"break\":\"0\",\"apptype\":\"radio-app\",\"labelLayout\":4,\"dataType\":\"string\",\"columnConfig\":{\"field\":{\"component\":{\"type\":\"yf-select\"},\"conditon\":true,\"length\":10,\"type\":\"string\",\"key\":\"key_1676616954901\"},\"displayField\":{\"conditon\":false,\"valueName\":\"rawValue\",\"length\":255,\"type\":\"string\",\"list\":true,\"key\":\"key_16766169549011\"}},\"label\":\"单选\",\"sort\":9,\"type\":\"radio\",\"required\":false,\"attrs\":{\"direction\":\"horizontal\"},\"layout\":24,\"vif\":true,\"field\":\"key_1676616954901\",\"operators\":[{\"label\":\"等于\",\"value\":\"equal\"},{\"label\":\"不等于\",\"value\":\"notequal\"},{\"component\":{\"type\":\"\"},\"label\":\"为空\",\"value\":\"null\"},{\"component\":{\"type\":\"\"},\"label\":\"不为空\",\"value\":\"notnull\"}],\"options\":[{\"checked\":true,\"text\":\"选项1\",\"value\":\"1\"},{\"checked\":false,\"text\":\"选项2\",\"value\":\"2\"},{\"checked\":false,\"text\":\"选项3\",\"value\":\"3\"}],\"disabled\":false,\"group\":\"base\"},\"key_1676619064140\":{\"break\":\"0\",\"apptype\":\"input-app\",\"labelLayout\":4,\"dataType\":\"string\",\"columnConfig\":{\"field\":{\"conditon\":true,\"length\":255,\"type\":\"string\",\"key\":\"key_1676619064140\"}},\"rules\":[{\"trigger\":\"blur\",\"message\":\"不允许输入 \\\\ / : * ? \\\" | < > \",\"validatorFn\":\"function(value){\\n      if(value==null || value===undefined || value===''){\\n        return true\\n      }\\n      return /[^\\\\\\\\/:*?\\\"|<>]+/.test(value)\\n    }\"}],\"sort\":1,\"label\":\"单行文本\",\"type\":\"input\",\"required\":false,\"layout\":24,\"vif\":true,\"field\":\"key_1676619064140\",\"operators\":[{\"label\":\"等于\",\"value\":\"equal\"},{\"label\":\"不等于\",\"value\":\"notequal\"},{\"component\":{\"type\":\"\"},\"label\":\"为空\",\"value\":\"null\"},{\"component\":{\"type\":\"\"},\"label\":\"不为空\",\"value\":\"notnull\"}],\"disabled\":false,\"group\":\"base\"},\"key_1676619755092\":{\"break\":\"0\",\"apptype\":\"select-app\",\"labelLayout\":4,\"dataType\":\"string\",\"columnConfig\":{\"field\":{\"conditon\":true,\"length\":255,\"type\":\"string\",\"key\":\"key_1676619755092\"},\"displayField\":{\"conditon\":false,\"valueName\":\"rawValue\",\"length\":255,\"type\":\"string\",\"list\":true,\"key\":\"key_16766197550921\"}},\"label\":\"下拉单选\",\"sort\":11,\"type\":\"select\",\"required\":false,\"layout\":24,\"vif\":true,\"field\":\"key_1676619755092\",\"operators\":[{\"label\":\"等于\",\"value\":\"equal\"},{\"label\":\"不等于\",\"value\":\"notequal\"},{\"component\":{\"type\":\"\"},\"label\":\"为空\",\"value\":\"null\"},{\"component\":{\"type\":\"\"},\"label\":\"不为空\",\"value\":\"notnull\"}],\"options\":[{\"checked\":true,\"text\":\"选项1\",\"value\":\"1\"},{\"checked\":false,\"text\":\"选项2\",\"value\":\"2\"},{\"checked\":false,\"text\":\"选项3\",\"value\":\"3\"}],\"disabled\":false,\"group\":\"base\"},\"key_1676619752909\":{\"break\":\"0\",\"apptype\":\"checkbox-app\",\"labelLayout\":4,\"dataType\":\"array\",\"columnConfig\":{\"field\":{\"component\":{\"type\":\"yf-select-checkbox\",\"attrs\":{\"maxTagCount\":1,\"maxTagTextLength\":2}},\"conditon\":true,\"length\":255,\"type\":\"string\",\"key\":\"key_1676619752909\"},\"displayField\":{\"conditon\":false,\"valueName\":\"rawValue\",\"length\":2000,\"type\":\"string\",\"list\":true,\"key\":\"key_16766197529091\"}},\"label\":\"复选\",\"sort\":10,\"type\":\"checkbox\",\"required\":false,\"attrs\":{\"direction\":\"horizontal\"},\"layout\":24,\"vif\":true,\"field\":\"key_1676619752909\",\"operators\":[{\"label\":\"等于\",\"value\":\"equal\"},{\"label\":\"不等于\",\"value\":\"notequal\"},{\"label\":\"属于\",\"value\":\"like\"},{\"label\":\"不属于\",\"value\":\"notlike\"},{\"label\":\"包含\",\"value\":\"inc\"},{\"label\":\"不包含\",\"value\":\"notinc\"},{\"component\":{\"type\":\"\"},\"label\":\"为空\",\"value\":\"null\"},{\"component\":{\"type\":\"\"},\"label\":\"不为空\",\"value\":\"notnull\"}],\"options\":[{\"checked\":true,\"text\":\"选项1\",\"value\":\"1\"},{\"checked\":false,\"text\":\"选项2\",\"value\":\"2\"},{\"checked\":false,\"text\":\"选项3\",\"value\":\"3\"}],\"disabled\":false,\"dataConverter\":\"array\",\"group\":\"base\"},\"key_1676619758700\":{\"break\":\"0\",\"apptype\":\"select-checkbox-app\",\"labelLayout\":4,\"dataType\":\"array\",\"columnConfig\":{\"field\":{\"component\":{\"type\":\"yf-select-checkbox\",\"attrs\":{\"maxTagCount\":1,\"maxTagTextLength\":2}},\"conditon\":true,\"length\":255,\"type\":\"string\",\"key\":\"key_1676619758700\"},\"displayField\":{\"conditon\":false,\"valueName\":\"rawValue\",\"length\":255,\"type\":\"string\",\"list\":true,\"key\":\"key_16766197587001\"}},\"label\":\"下拉多选\",\"sort\":12,\"type\":\"select-checkbox\",\"required\":false,\"layout\":24,\"vif\":true,\"field\":\"key_1676619758700\",\"operators\":[{\"label\":\"等于\",\"value\":\"equal\"},{\"label\":\"属于\",\"value\":\"like\"},{\"label\":\"不属于\",\"value\":\"notlike\"},{\"label\":\"包含\",\"value\":\"inc\"},{\"label\":\"不包含\",\"value\":\"notinc\"},{\"component\":{\"type\":\"\"},\"label\":\"为空\",\"value\":\"null\"},{\"component\":{\"type\":\"\"},\"label\":\"不为空\",\"value\":\"notnull\"}],\"options\":[{\"checked\":true,\"text\":\"选项1\",\"value\":\"1\"},{\"checked\":false,\"text\":\"选项2\",\"value\":\"2\"},{\"checked\":false,\"text\":\"选项3\",\"value\":\"3\"}],\"disabled\":false,\"dataConverter\":\"array\",\"group\":\"base\"}},\"formLayout\":\"horizontal\",\"inline\":true,\"labelPosition\":\"left\",\"tableInfo\":{\"tableId\":\"1626490758636847105\",\"tableName\":\"zt_1076170268306571264\"},\"serviceKey\":\"1626490759639285762\",\"title\":[{\"isDisable\":false,\"childSelectLists\":[],\"name\":\"表单控件\",\"orderBy\":0,\"type\":\"control\",\"valueKey\":\"formControl\",\"value\":\"key_1676619064140\"}],\"span\":24,\"order\":[\"key_1676619064140\",\"key_1676616954901\",\"key_1676619752909\",\"key_1676619755092\",\"key_1676619758700\"]}",
        serialDesignMobile: '',
        updateBy: '1569682398222860290',
        updateName: '',
        version: null
      },
      msg: 'success'
    }
  }
}