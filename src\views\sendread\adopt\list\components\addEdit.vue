<template>
  <div>
    <a-modal
      :visible="visible"
      :destroyOnClose="true"
      title="信息采编维护"
      width="1300px"
      @ok="handleOk"
      @cancel="() => cancel()"
      :bodyStyle="{ overflow: 'auto', height: '68vh' }"
      :footer="false"
    >
      <a-form :form="form" :labelCol="labelCol" :wrapperCol="wrapperCol" @submit="handleOk">
        <div style="pointer-events: none">
          <a-form-item label="报送标题">
            <a-input
              v-decorator="[
                'title',
                {
                  rules: [{ required: true, message: '请输入' }],
                },
              ]"
              placeholder="请输入"
            />
          </a-form-item>
          <a-row>
            <a-col :span="12">
              <a-form-item label="报送时间" :labelCol="{ span: 8 }" :wrapperCol="{ span: 12 }">
                <a-date-picker
                  v-decorator="[
                    'reportTime',
                    {
                      rules: [{ required: true, message: '请输入' }],
                    },
                  ]"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="报送类型" :labelCol="{ span: 4 }" :wrapperCol="{ span: 12 }">
                <zh-dict codeType="CPPCC_BSLX" v-decorator="['reportType', { rules: [{ required: true, message: '请输入' }] }]" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="12" v-if="formState.reportType == 3">
              <a-form-item label="参加单位名称" :labelCol="{ span: 8 }" :wrapperCol="{ span: 12 }">
                <a-input
                  v-decorator="[
                    'joinUnitName',
                    {
                      rules: [{ required: true, message: '请输入' }],
                    },
                  ]"
                  placeholder="请输入"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12" v-if="formState.reportType == 2">
              <a-form-item label="报送单位" :labelCol="{ span: 8 }" :wrapperCol="{ span: 12 }">
                <z-select-unit
                  v-decorator="[
                    'reportUnitId',
                    {
                      rules: [{ required: true, message: '请输入' }],
                    },
                  ]"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item
                label="接收单位"
                :labelCol="{
                  span: formState.reportType == 2 || formState.reportType == 3 ? 4 : 8,
                }"
                :wrapperCol="{ span: 12 }"
              >
                <z-select-unit
                  v-decorator="[
                    'receiverUnit',
                    {
                      rules: [{ required: true, message: '请输入' }],
                    },
                  ]"
                />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="8">
              <a-form-item label="撰稿人" :labelCol="{ span: 12 }" :wrapperCol="{ span: 10 }">
                <a-input
                  v-decorator="[
                    'copywriter',
                    {
                      rules: [{ required: true, message: '请输入' }],
                    },
                  ]"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="撰稿电话" :labelCol="{ span: 8 }" :wrapperCol="{ span: 12 }">
                <a-input
                  v-decorator="[
                    'copywriterPhone',
                    {
                      rules: [{ required: true, message: '请输入' }],
                    },
                  ]"
                  placeholder="请输入"
                />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="审核人" :labelCol="{ span: 6 }" :wrapperCol="{ span: 12 }" v-if="formState.reportType != 1 && formState.reportType != 4">
                <a-input
                  v-decorator="[
                    'auditorId',
                    {
                      rules: [{ required: true, message: '请输入' }],
                    },
                  ]"
                  placeholder="请输入"
                />
              </a-form-item>
            </a-col>
          </a-row>
          <a-form-item label="上报内容">
            <div
              style="border: 1px solid #ccc"
              v-decorator="[
                'reportContent',
                {
                  rules: [{ required: false, message: '请输入' }],
                },
              ]"
            >
              <Toolbar style="border-bottom: 1px solid #ccc" :editor="editor" :defaultConfig="toolbarConfig" :mode="mode" />
              <Editor style="height: 300px; overflow-y: auto" v-model="html" :defaultConfig="editorConfig" :mode="mode" @onCreated="onCreated" />
            </div>
          </a-form-item>
        </div>

        <a-form-item label="上传定稿件">
          <z-file-uploader
            v-decorator="['makeFileList']"
            :limit="1"
            accept=".pdf,.ofd,.Pdf,.Ofd,.PDF,.OFDF"
            configGroup="zx-dg"
            :showUploadList="false"
            :parentId="formState.id"
            @input="handleChange"
            v-model="formState.makeFileList"
            ref="fileUploader"
          />
        </a-form-item>

        <a-form-item label=" " :colon="false">
          <a-table :dataSource="formState.makeFileList" :columns="columns" :pagination="false">
            <span slot="name" slot-scope="text, record">
              {{ record["name"] || record["fileName"] }}
            </span>
            <span slot="type" slot-scope="text, record">
              {{ (record["name"] || record["fileName"]).split(".").pop() }}
            </span>
            <span slot="configGroup"> 定稿件 </span>
            <span slot="action" slot-scope="text, record">
              <a @click="down(record)">查看</a>
              <a-divider type="vertical" />
              <a style="color: red" @click="del(record)">删除</a>
            </span>
          </a-table>
        </a-form-item>

        <a-form-item label=" " :colon="false">
          <a-table :dataSource="formState.fileList" :columns="columns" :pagination="false">
            <span slot="name" slot-scope="text, record">
              {{ record["name"] || record["fileName"] }}
            </span>
            <span slot="type" slot-scope="text, record">
              {{ (record["name"] || record["fileName"]).split(".").pop() }}
            </span>
            <span slot="configGroup"> 原稿件 </span>

            <span slot="action" slot-scope="text, record">
              <a @click="down(record)">查看</a>
              <a-divider type="vertical" />
              <a @click="down(record, true)">下载</a>
            </span>
          </a-table>
        </a-form-item>

        <a-form-item label="审批意见">
          <a-textarea
            :autosize="{ minRows: 4, maxRows: 8 }"
            v-decorator="[
              'approvalOpinion',
              {
                rules: [{ required: true, message: '请输入' }],
              },
            ]"
            placeholder="请输入"
          />
        </a-form-item>
        <a-row>
          <a-col :span="12">
            <a-form-item label="得分项" :labelCol="{ span: 8 }" :wrapperCol="{ span: 12 }">
              <a-input
                v-decorator="[
                  'scoreItem',
                  {
                    rules: [{ required: false, message: '请输入' }],
                  },
                ]"
                style="width: 100%"
              >
              </a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="分值" :labelCol="{ span: 4 }" :wrapperCol="{ span: 12 }">
              <a-input
                readonly
                v-decorator="[
                  'scoreItemValue',
                  {
                    rules: [{ required: false, message: '请输入' }],
                  },
                ]"
                placeholder="请输入"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="12">
            <a-form-item label="加分项" :labelCol="{ span: 8 }" :wrapperCol="{ span: 12 }">
              <a-select
                v-decorator="[
                  'bonusType',
                  {
                    rules: [{ required: false, message: '请输入' }],
                  },
                ]"
                style="width: 100%"
                @change="scoreChange"
              >
                <a-select-option v-for="(item, index) in scoreList" :key="index" :value="`${item.type}`" :dataRef="item">
                  {{ item.type }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="分值" :labelCol="{ span: 4 }" :wrapperCol="{ span: 12 }">
              <a-input
                readonly
                v-decorator="[
                  'score',
                  {
                    rules: [{ required: false, message: '请输入' }],
                  },
                ]"
                placeholder="请输入"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="12">
            <a-form-item label="领导批示（人次）" :labelCol="{ span: 8 }" :wrapperCol="{ span: 12 }">
              <a-input-number
                v-decorator="[
                  'personCount',
                  {
                    rules: [{ required: false, message: '请输入' }],
                  },
                ]"
                style="width: 100%"
              >
              </a-input-number>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="年号" :labelCol="{ span: 4 }" :wrapperCol="{ span: 12 }">
              <a-input-number
                v-decorator="[
                  'year',
                  {
                    rules: [{ required: true, message: '请输入' }],
                  },
                ]"
                placeholder="请输入"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="12">
            <a-form-item label="期号" :labelCol="{ span: 8 }" :wrapperCol="{ span: 12 }">
              <a-input-number
                v-decorator="[
                  'issue',
                  {
                    rules: [{ required: true, message: '请输入' }],
                  },
                ]"
                style="width: 100%"
              >
              </a-input-number>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="领域分类" :labelCol="{ span: 4 }" :wrapperCol="{ span: 12 }">
              <a-select
                v-decorator="[
                  'fieldType',
                  {
                    rules: [{ required: false, message: '请输入' }],
                    initialValue: formState['fieldTypeName'],
                  },
                ]"
                style="width: 100%"
              >
                <a-select-option v-for="(item, index) in lyList" :key="index" :value="`${item.id}`" :dataRef="item">
                  {{ item.name }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <div style="text-align: center">
          <a-space>
            <a-button type="primary" style="margin-right: 36px" @click="handleOk()"> 保存修改 </a-button>
            <a-button type="primary" style="margin-right: 36px" @click="handleOk('1')"> 取消采编 </a-button>
            <a-button type="primary" @click="cancel"> 取消 </a-button>
          </a-space>
        </div>
      </a-form>
    </a-modal>
  </div>
</template>

<script>
import { auth as http } from "@zh/common-utils/http";
import dayjs from "dayjs";
// 文件、图片类型
import MimeType from "@zh/common-utils/file/mime-type";
import { Editor, Toolbar } from "@wangeditor/editor-for-vue";

import { toolbarConfig, editorConfig } from "@/utils/common";

export default {
  components: {
    Editor,
    Toolbar,
  },
  props: {},
  data() {
    return {
      isDetail: false,
      form: this.$form.createForm(this, { name: "report_update" }),
      visible: false,
      labelCol: { span: 4 },
      wrapperCol: { span: 16 },
      mimeType: MimeType,
      formState: {},
      columns: [
        {
          title: "文件名称",
          scopedSlots: { customRender: "name" },
        },
        {
          title: "文件类型",
          scopedSlots: { customRender: "type" },
        },
        {
          title: "文稿类型",
          scopedSlots: { customRender: "configGroup" },
        },
        {
          title: "操作",
          width: 120,
          scopedSlots: { customRender: "action" },
        },
      ],
      editor: null,
      html: "",
      toolbarConfig: toolbarConfig,
      editorConfig: editorConfig,
      mode: "default", // or 'simple'
      scoreList: [],
      lyList: [],
    };
  },
  methods: {
    onCreated(editor) {
      this.editor = Object.seal(editor); // 一定要用 Object.seal() ，否则会报错
    },
    open(id, reportId) {
      if (id) {
        this.getDetail(id, reportId);
      }
      this.visible = true;
    },
    getDetail(id, reportId) {
      if (id) {
        http.get(`/syCollect/find?id=${id}`).then((res) => {
          this.visible = true;
          this.formState = {
            ...res,
            scoreItem: "采编",
            scoreItemValue: "5",
          };
          this.editor.setHtml(res["reportContent"]);
          http
            .post(`/system/SysFileRelation/list`, {
              businessId: reportId,
              fileType: "zx",
            })
            .then((res) => {
              console.log("🚀 ~ file: addEdit.vue:253 ~ .then ~ res:", res);
              this.formState.fileList = res;
            });
          http
            .post(`/system/SysFileRelation/list`, {
              businessId: id,
              fileType: "zx-dg",
            })
            .then((res) => {
              console.log("🚀 ~ file: addEdit.vue:253 ~ .then ~ res:", res);
              this.formState.makeFileList = res;
              console.log("🚀 ~ file: addEdit.vue:483 ~ .then ~ formState:", this.formState);
            });
          setTimeout(() => {
            let temp = { ...this.formState };
            delete temp["fieldType"];
            this.form.setFieldsValue({ ...temp });
          }, 200);
        });

        http
          .post(`/bonusFactor/list`, {
            pageNum: 1,
            pageSize: 999,
          })
          .then((res) => {
            this.scoreList = res["list"].filter((obj) => obj["isEnable"] != "0");
          });

        http
          .post(`/syDict/list`, {
            pageNum: 1,
            pageSize: 999,
          })
          .then((res) => {
            console.log("🚀 ~ file: addEdit.vue:414 ~ .then ~ res:", res);
            this.lyList = res;
          });
      } else {
        this.formState = {};
        this.form.resetFields();
      }
    },
    cancel() {
      this.visible = false;
      this.formState = {};
      this.form.resetFields();
    },
    handleOk(type) {
      this.form.setFieldsValue({ reportContent: this.editor.getHtml() });

      let reg = new RegExp("[\\u4E00-\\u9FFF]+", "g");

      this.form.validateFields((err, value) => {
        if (!err) {
          console.info("success", this.img);
          Object.keys(value).forEach((key) => {
            if (key.includes("Date")) {
              value[key] = dayjs(value[key]).valueOf();
            }
          });
          if (reg.test(value["fieldType"])) {
            delete value["fieldType"];
          }
          let url = "/syCollect/update";
          if (type) {
            url = "/syReport/collectEdit";
            value["type"] = type;
          }
          http
            .post(url, {
              ...this.formState,
              ...value,
              isUpload: value.makeFileList && value.makeFileList.length > 0 ? 1 : 0,
            })
            .then((res) => {
              this.cancel();
              this.$emit("callBack");
            });
        }
      });
      // this.cancel();
    },
    scoreChange(val, option) {
      this.form.setFieldsValue({
        score: option?.data?.attrs?.dataRef?.score,
      });
    },
    down(record, isDown) {
      if (isDown) {
        window.location.href = process.env.VUE_APP_API_BASE_URL + `/storage/download/${record["fileId"] || record["uid"]}`;
      } else {
        window.open(
          process.env.VUE_APP_READ +
            `?file=` +
            process.env.VUE_APP_READ_BASE_URL +
            `/sk/file/getJson?fileId=${record["fileId"] || record["uid"]}` +
            `%26serviceUrl=${process.env.VUE_APP_READ_BASE_URL}`
        );
      }
    },
    del(record) {
      this.$refs.fileUploader.handleFileRemoveDefault({
        ...record,
      });
    },
    // 修改附件
    handleChange(event, status) {
      console.log("🚀 ~ file: index.vue:275 ~ handleChange ~ event:", event);
      this.form.setFieldsValue({ makeFileList: event });
    },
    // 删除附件
    handleRemove(info) {
      console.log("🚀 ~ file: index.vue:280 ~ handleRemove ~ info:", info);
      this.form.setFieldsValue({
        makeFileList: undefined,
      });
    },
    // bsChange(val) {
    //   this.formState.reportType = val;
    //   this.formState.copywriter = undefined;
    //   this.formState.copywriterPhone = undefined;
    //   this.formState.reportUnitId = undefined;
    //   if (val == 4) {
    //     this.formState.copywriter = this.user.userName;
    //     this.formState.copywriterPhone = this.user.tel;
    //   } else if (val == 2) {
    //     this.formState.reportUnitId = [this.user.depts[0].unitId];
    //     setTimeout(() => {
    //       this.form.setFieldsValue({ ...this.formState });
    //     }, 200);
    //   } else if (val == 3) {
    //     this.formState.joinUnitName = this.unitName;
    //     setTimeout(() => {
    //       this.form.setFieldsValue({ ...this.formState });
    //     }, 200);
    //   }
    //   this.form.setFieldsValue({ ...this.formState });
    // },
    // getUnit() {
    //   http
    //     .get(`/syReport/getUserJoinUnit?userId=${this.user.id}`)
    //     .then((res) => {
    //       this.unitName = res;
    //     });
    // },
  },
};
</script>
<style src="@wangeditor/editor/dist/css/style.css"></style>

<style scoped lang="less">
@import url("./addEdit.less");
</style>
