<template>
  <div>
    <a-modal
      :visible="visible"
      :destroyOnClose="true"
      title="信息发布详情"
      width="1200px"
      @ok="handleOk"
      @cancel="() => cancel()"
      :bodyStyle="{ overflow: 'auto' }"
      :footer="false"
    >
      <a-form :form="form" :labelCol="labelCol" :wrapperCol="wrapperCol" @submit="handleOk">
        <a-form-item label="发布标题">
          <a-input
            v-decorator="[
              'oaTitle',
              {
                rules: [{ required: true, message: '请输入' }],
              },
            ]"
            placeholder="请输入"
          />
        </a-form-item>
        <a-row>
          <a-col :span="10">
            <a-col span="15" style="padding-left: 12px">
              <a-form-item label="年号" :labelCol="{ span: 15 }" :wrapperCol="{ span: 8 }">
                <a-input
                  v-decorator="[
                    'oaYear',
                    {
                      rules: [{ required: true, message: '请输入' }],
                    },
                  ]"
                  placeholder="请选择"
                  style="width: 100%"
                >
                  <a-select-option v-for="(item, index) in yearList" :key="index" :value="`${item}`">
                    {{ item }}
                  </a-select-option>
                </a-input>
              </a-form-item>
            </a-col>
            <a-col span="9">
              <a-form-item label="期号" :labelCol="{ span: 7 }" :wrapperCol="{ span: 12 }">
                <a-input
                  v-decorator="[
                    'oaIssue',
                    {
                      rules: [{ required: true, message: '请输入' }],
                    },
                  ]"
                  placeholder="请输入"
                />
              </a-form-item>
            </a-col>
          </a-col>
          <a-col :span="7">
            <a-form-item label="发布时间" :labelCol="{ span: 6 }" :wrapperCol="{ span: 12 }">
              <a-date-picker
                v-decorator="[
                  'reportDate',
                  {
                    rules: [{ required: true, message: '请输入' }],
                  },
                ]"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="7">
            <a-form-item label="采用时间" :labelCol="{ span: 6 }" :wrapperCol="{ span: 11 }">
              <a-date-picker
                v-decorator="[
                  'acceptDate',
                  {
                    rules: [{ required: true, message: '请输入' }],
                  },
                ]"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="24">
            <a-form-item label="阅读范围">
              <!-- <z-select-user
                :limit="99999"
                @change="rpChange"
                v-decorator="[
                  'unitList',
                  {
                    rules: [{ required: true, message: '请选择' }],
                  },
                ]"
                placeholder="请选择"
              /> -->
              <zh-work-group
                v-decorator="[
                  'unitList',
                  {
                    rules: [{ required: true, message: '请选择' }],
                  },
                ]"
                @change="rpChange"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col>
            <a-form-item label="" style="display: none">
              <a-input v-decorator="['bizId']" />
            </a-form-item>
            <a-form-item label="采编">
              <a-input
                @click="openMs"
                :readonly="true"
                v-decorator="[
                  'bizTitle',
                  {
                    rules: [{ required: true, message: '请选择' }],
                  },
                ]"
                placeholder="请选择"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <a-form-item label=" " :colon="false">
          <a-table :dataSource="formState.syncOaFile" :columns="columns" :pagination="false">
            <span slot="name" slot-scope="text, record">
              {{ record["name"] || record["fileName"] }}
            </span>
            <span slot="type" slot-scope="text, record">
              {{ (record["name"] || record["fileName"]).split(".").pop() }}
            </span>
            <span slot="action" slot-scope="text, record">
              <a @click="down(record)">查看</a>
              <a-divider type="vertical" />
              <a @click="down(record, true)">下载</a>
            </span>
          </a-table>
        </a-form-item>

        <div style="text-align: center">
          <a-space>
            <a-button type="primary" style="margin-right: 36px" @click="handleOk">
              {{ isPublish ? "发布" : "修改" }}
            </a-button>
            <a-button type="primary" @click="cancel"> 取消 </a-button>
          </a-space>
        </div>
      </a-form>
    </a-modal>
    <MsList ref="mslistRef" @change="msChange" />
  </div>
</template>

<script>
import { auth as http } from "@zh/common-utils/http";
import dayjs from "dayjs";
// 文件、图片类型
import MimeType from "@zh/common-utils/file/mime-type";
import { Editor, Toolbar } from "@wangeditor/editor-for-vue";
import { toolbarConfig, editorConfig } from "@/utils/common";
import MsList from "./mslist";
export default {
  components: {
    Editor,
    Toolbar,
    MsList,
  },
  props: {
    isPublish: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    let year = dayjs().year();
    let yearList = new Array(5).fill("").map((val, index) => year - index);
    return {
      isDetail: false,
      form: this.$form.createForm(this, { name: "report_update" }),
      visible: false,
      labelCol: { span: 4 },
      wrapperCol: { span: 18 },
      mimeType: MimeType,
      yearList,
      tempState: [],
      formState: {},
      columns: [
        {
          title: "文件名称",
          scopedSlots: { customRender: "name" },
        },
        {
          title: "文件类型",
          scopedSlots: { customRender: "type" },
        },
        {
          title: "操作",
          width: 120,
          scopedSlots: { customRender: "action" },
        },
      ],
      editor: null,
      html: "",
      toolbarConfig: toolbarConfig,
      editorConfig: editorConfig,
      mode: "default", // or 'simple'
    };
  },
  methods: {
    open(id) {
      if (id) {
        this.getDetail(id);
      }
      this.visible = true;
    },
    getDetail(id) {
      if (id) {
        http.get(`/workOa/find?id=${id}`).then((res) => {
          this.tempState = res["unitList"] || [];
          this.formState = {
            ...res,
            reportDate: res["reportDate"] || dayjs(),
            unitList: this.tempState.map((obj) => {
              return obj["unitId"];
            }),
            syncOaFile: [res["syncOaFile"]],
          };
          setTimeout(() => {
            this.form.setFieldsValue({ ...this.formState });
          }, 200);
        });
        console.log("🚀 ~ file: addEdit.vue:250 ~ http.get ~ formState:", this.formState);
      } else {
        this.formState = {};
        this.form.resetFields();
      }
    },
    cancel() {
      this.visible = false;
      this.formState = {};
      this.form.resetFields();
    },
    handleOk() {
      this.form.validateFields((err, value) => {
        console.log("🚀 ~ file: addEdit.vue:242 ~ this.form.validateFields ~ value:", value);
        if (!err) {
          console.info("success", this.img);
          Object.keys(value).forEach((key) => {
            if (key.includes("Date") && value[key]) {
              value[key] = dayjs(value[key]).valueOf();
            }
          });
          if (value["unitList"]) {
            value["unitList"] = this.tempState;
          }
          let url = "";
          let params = {
            ...this.formState,
            ...value,
            oaFile: undefined,
          };
          if (this.formState["id"]) {
            url = "/workOa/update";
            delete params["oaFile"];
          }
          http.post(url, { ...params }).then((res) => {
            this.cancel();
            this.$emit("callBack");
          });
        }
      });
      // this.cancel();
    },
    rpChange(ids, units) {
      console.log("🚀 ~ file: addEdit.vue:298 ~ rpChange ~ units:", units);
      this.tempState = units.map((obj) => {
        return {
          type: 1,
          unitId: obj["id"],
          unitLevelCode: obj["levelCode"],
          unitName: obj["name"],
          unitLevelCode: obj["levelCode"],
        };
      });
    },
    down(record, isDown = false) {
      console.log("🚀 ~ file: addEdit.vue:317 ~ down ~ record:", record);
      if (isDown) {
        this.base64ToFile(record["file"], record["fileName"]);
      } else {
        window.open(
          process.env.VUE_APP_READ +
            `?file=` +
            process.env.VUE_APP_READ_BASE_URL +
            `/sk/file/getOaJson?fileId=${this.formState["id"] || record["fileId"] || record["id"]}` +
            `%26serviceUrl=${process.env.VUE_APP_READ_BASE_URL}`
        );
      }
    },
    base64ToFile(fileByte, fileName) {
      // 下载文件
      var bstr = atob(fileByte);
      var n = bstr.length;
      var u8arr = new Uint8Array(n);
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n);
      }
      let blob = new Blob([u8arr], {
        type: "",
      });
      if (window.navigator && window.navigator.msSaveOrOpenBlob) {
        window.navigator.msSaveOrOpenBlob(blob, fileName);
      } else {
        let downloadElement = document.createElement("a");
        let href = window.URL.createObjectURL(blob);
        downloadElement.href = href;
        downloadElement.download = fileName;
        document.body.appendChild(downloadElement);
        downloadElement.click();
        document.body.removeChild(downloadElement);
        window.URL.revokeObjectURL(href);
      }
    },

    del(record) {
      this.$refs.fileUploader.handleFileRemoveDefault({
        ...record,
      });
    },
    openMs() {
      this.$refs.mslistRef.open();
    },
    msChange(data = []) {
      console.log("🚀 ~ file: addEdit.vue:311 ~ msChange ~ data:", data);
      this.form.setFieldsValue({ bizId: data[0]?.id, bizTitle: data[0]?.titel });
    },
  },
};
</script>
<style src="@wangeditor/editor/dist/css/style.css"></style>

<style scoped lang="less">
@import url("./addEdit.less");
</style>
