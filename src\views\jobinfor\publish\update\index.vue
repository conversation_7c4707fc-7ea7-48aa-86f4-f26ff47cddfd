<template>
  <div class="cp-page">
    <a-row gutter="50">
      <a-col span="8" class="sear">
        <span>报送标题：</span>
        <a-input v-model="search.title" placeholder="请输入" />
      </a-col>
      <a-col span="8" class="sear">
        <span>年度：</span>
        <a-input-number v-model="search.reportYear" placeholder="请输入" />
      </a-col>
    </a-row>
    <div style="text-align: center; margin-bottom: 16px">
      <a-space>
        <a-button type="primary" @click="getList()">查询</a-button>
        <a-button
          type="primary"
          @click="
            () => {
              search = {
                reportYear: dayjs().year(),
                auditStatus: 1,
              };
              getList();
            }
          "
          >重置</a-button
        >
      </a-space>
    </div>
    <a-table
      :dataSource="dataSource"
      :columns="columns"
      :pagination="pagination"
      @change="
        (obj) => {
          getList(obj.current, obj.pageSize);
        }
      "
    >
      <a slot="name" slot-scope="text, record" @click="addOpen(record)">
        {{ record ? record["titel"] : "" }}
      </a>
      <span slot="reportDate" slot-scope="text">
        {{ text ? dayjs(text).format("YYYY-MM-DD") : text }}
      </span>
      <span slot="oaYear" slot-scope="text, record"> {{ text ? text + "年" : "" }}{{ record["oaIssue"] ? `第${record["oaIssue"]}期` : "" }} </span>
      <span slot="accpetDate" slot-scope="text">
        {{ text ? dayjs(text).format("YYYY-MM-DD") : text }}
      </span>
      <span slot="action" slot-scope="text, record">
        <a-space>
          <a @click="addOpen(record)">详情</a>
          <a-popconfirm title="是否确认删除？" @confirm="del(record)">
            <a style="color: red">删除</a>
          </a-popconfirm>
        </a-space>
      </span>
    </a-table>
    <AddEdit ref="addEditRef" @callBack="getList" />
  </div>
</template>

<script>
import { auth as http } from "@zh/common-utils/http";
import AddEdit from "../update/components/addEdit.vue";
import dayjs from "dayjs";
export default {
  components: { AddEdit },
  data() {
    return {
      dayjs,
      dataSource: [{ id: "123456", name: 1231 }],
      columns: [
        {
          title: "报送标题",
          dataIndex: "titel",
          scopedSlots: { customRender: "name" },
        },
        {
          title: "报送时间",
          dataIndex: "reportDate",
          key: "reportDate",
          scopedSlots: { customRender: "reportDate" },
        },
        {
          title: "报送单位",
          dataIndex: "reportUnitName",
          key: "reportUnitName",
        },
        {
          title: "采用情况",
          dataIndex: "oaYear",
          key: "oaYear",
          scopedSlots: { customRender: "oaYear" },
        },
        {
          title: "采编状态",
          dataIndex: "auditStatusName",
          key: "auditStatusName",
        },
        {
          title: "采用时间",
          dataIndex: "accpetDate",
          key: "accpetDate",
          scopedSlots: { customRender: "accpetDate" },
        },
        {
          title: "操作",
          title: "操作",
          key: "action",
          width: 120,
          scopedSlots: { customRender: "action" },
        },
      ],
      search: {
        reportYear: dayjs().year(),
        auditStatus: 1,
      },
      keyword: undefined,
      pageNum: 1,
      pageSize: 10,
      labelCol: { span: 3 },
      wrapperCol: { span: 20 },
      pagination: {},
    };
  },
  // mounted() {
  //   this.getList();
  // },
  activated() {
    this.getList();
  },
  methods: {
    getList(pageNum = 1, pageSize = 10) {
      let temp = { ...this.search };
      Object.keys(temp).forEach((key) => {
        if (key.includes("Time") && temp[key]) {
          temp[key] = dayjs(temp[key]).valueOf();
        }
      });
      http
        .post("/workReport/list", {
          ...temp,
          pageNum: pageNum || this.pageNum,
          pageSize: pageSize || this.pageSize,
          pageType: 1,
          menuCode: "work:report:update",
        })
        .then((res) => {
          const { currPage, pageSize, list, totalCount } = res;
          this.dataSource = list;
          this.pageNum = currPage;
          this.pageSize = pageSize;
          this.pagination = {
            current: currPage,
            total: totalCount,
          };
        });
    },
    addOpen(record) {
      this.$refs.addEditRef.open(record["id"]);
    },
    del(record) {
      http.get(`/workReport/delete?id=${record['id']}`).then(() => {
        this.getList();
      });
    },
  },
};
</script>

<style scoped lang="less">
.sear {
  display: flex;
  align-items: center;
  font-size: 14px;
  & > span:first-child {
    width: 80px;
    text-align: right;
  }
  & > :last-child {
    flex: 1;
  }
}
.ant-row {
  margin-bottom: 16px;
}
</style>
