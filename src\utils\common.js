export const editorConfig = {
    placeholder: "请输入内容...",
    MENU_CONF: {
        uploadImage: {
            // 自定义插入图片
            customInsert(res, insertFn) {
                // 从 res 中找到 url alt href ，然后插入图片
                insertFn(
                    process.env.VUE_APP_API_BASE_URL +
                    // `/storage/download/` +
                    res["data"]["path"],
                    res["data"]["fileName"],
                    process.env.VUE_APP_API_BASE_URL +
                    // `/storage/download/` +
                    res["data"]["path"]
                );
            },
            fieldName: "file",
            meta: {
                chunked: false,
                bucketName: "zongheng",
                systemName: "zongheng",
                markText: "markText",
            },
            // 跨域是否传递 cookie ，默认为 false
            withCredentials: true,
            server: process.env.VUE_APP_API_BASE_URL + "/file/upload",
        },
    },
}
export const toolbarConfig = {
    excludeKeys: [
        "insertImage",
        "group-video",
        "insertVideo",
        "emotion",
        "insertLink",
    ],
}