<template>
  <div class="cp-page">
    <a-form :form="form" :labelCol="labelCol" :wrapperCol="wrapperCol" @submit="handleOk">
      <a-form-item label="约稿主题">
        <a-input
          v-decorator="[
            'ygTheme',
            {
              rules: [{ required: true, message: '请输入' }],
            },
          ]"
          placeholder="请输入"
        />
      </a-form-item>
      <a-form-item label="约稿时间">
        <a-date-picker
          v-decorator="[
            'ygTime',
            {
              rules: [{ required: true, message: '请选择' }],
            },
          ]"
          style="width: 100%"
        />
      </a-form-item>
      <a-form-item label="接收人">
        <!-- <z-select-user
          v-decorator="[
            'receiverId',
            {
              rules: [{ required: true, message: '请选择' }],
            },
          ]"
          :limit="99999"
        /> -->
        <zh-work-group
          v-decorator="[
            'receiverId',
            {
              rules: [{ required: true, message: '请选择' }],
            },
          ]"
        />
      </a-form-item>
      <a-form-item label="约稿内容">
        <div
          style="border: 1px solid #ccc"
          v-decorator="[
            'ygContent',
            {
              rules: [{ required: true, message: '请输入' }],
            },
          ]"
        >
          <Toolbar style="border-bottom: 1px solid #ccc" :editor="editor" :defaultConfig="toolbarConfig" :mode="mode" />
          <Editor style="height: 300px; overflow-y: auto" v-model="html" :defaultConfig="editorConfig" :mode="mode" @onCreated="onCreated" />
        </div>
      </a-form-item>
      <a-form-item label="文件上传">
        <z-file-uploader
          v-decorator="[
            'fileList',
            {
              rules: [{ required: true, message: '请上传' }],
            },
          ]"
          accept=".pdf,.ofd,.Pdf,.Ofd,.PDF,.OFDF"
          :limit="1"
          configGroup="zx"
          :showUploadList="false"
          :parentId="formState.fileId"
          @input="handleChange"
          @FileBeforeUpload="FileBeforeUpload"
          @FileSuccessUpload="FileSuccessUpload"
          :handleRemove="handleRemove"
          v-model="formState.fileList"
          ref="fileUploader"
        />
      </a-form-item>

      <a-form-item label=" " :colon="false">
        <zh-progress :loading="loading">
          <a-table :dataSource="formState.fileList" :columns="columns">
            <span slot="type" slot-scope="text, record">
              {{ (record["name"] || record["fileName"]).split(".").pop() }}
            </span>

            <span slot="action" slot-scope="text, record">
              <a @click="down(record)">查看</a>
              <a-divider type="vertical" />
              <!-- <a-popconfirm
              title="是否确认删除？"
              @confirm="del(record)"
              @cancel="cancel"
            >
              <a style="color: red">删除</a>
            </a-popconfirm> -->
              <a style="color: red" @click="del(record)">删除</a>
            </span>
          </a-table>
        </zh-progress>
      </a-form-item>

      <div style="text-align: center">
        <a-space>
          <a-button type="primary" style="margin-right: 36px" @click="handleOk"> 发布 </a-button>
          <a-button type="primary" @click="cancel"> 取消 </a-button>
        </a-space>
      </div>
    </a-form>
  </div>
</template>

<script>
import { auth as http } from "@zh/common-utils/http";
import dayjs from "dayjs";
import { Editor, Toolbar } from "@wangeditor/editor-for-vue";
// 文件、图片类型
import MimeType from "@zh/common-utils/file/mime-type";
import { toolbarConfig, editorConfig } from "@/utils/common";
export default {
  components: { Editor, Toolbar },

  props: {
    pId: String,
    callBack: Function,
    session: Object,
  },
  data() {
    return {
      isDetail: false,
      form: this.$form.createForm(this, { name: "report" }),
      visible: false,
      mimeType: MimeType,
      labelCol: { span: 4 },
      wrapperCol: { span: 16 },
      formState: {
        fileId: new Date().valueOf().toString(),
      },
      columns: [
        {
          title: "文件名称",
          dataIndex: "name",
          key: "name",
        },
        {
          title: "文件类型",
          scopedSlots: { customRender: "type" },
        },
        {
          title: "操作",
          width: 120,
          scopedSlots: { customRender: "action" },
        },
      ],
      editor: null,
      html: "",
      toolbarConfig: toolbarConfig,
      editorConfig: editorConfig,
      mode: "default", // or 'simple'
    };
  },
  beforeDestroy() {
    const editor = this.editor;
    if (editor == null) return;
    editor.destroy(); // 组件销毁时，及时销毁编辑器
  },
  methods: {
    onCreated(editor) {
      this.editor = Object.seal(editor); // 一定要用 Object.seal() ，否则会报错
    },
    open() {
      this.visible = true;
    },
    cancel() {
      this.visible = false;
      this.html = "";
      this.formState = {};
      this.form.resetFields();
    },
    handleOk() {
      this.form.setFieldsValue({ ygContent: this.editor.getHtml() });
      console.log("🚀 ~ file: index.vue:254 ~ handleOk ~ ygContent:", this.editor.getHtml());
      this.form.validateFields((err, value) => {
        if (!err) {
          console.info("success", this.img);
          Object.keys(value).forEach((key) => {
            if (key.includes("Date")) {
              value[key] = dayjs(value[key]).valueOf();
            }
          });
          let url = "/ygNotice/save";
          if (this.formState["id"]) {
            url = "/ygNotice/update";
          }
          let bool = true;
          if (value.fileList) {
            value.fileList.forEach((obj) => {
              if (obj["status"] != "done") {
                bool = false;
                return this.$notify.warn("文件正在上传中，请稍后进行保存");
              }
            });
          }
          if (bool) {
            http
              .post(url, {
                ...this.formState,
                ...value,
                receiver: value["receiverId"],
              })
              .then((res) => {
                this.cancel();
                this.editor.setHtml("");
                this.$emit("callBack");
              });
          }
        }
      });
      // this.cancel();
    },
    // 修改附件
    handleChange(event, status) {
      console.log("🚀 ~ file: index.vue:275 ~ handleChange ~ event:", event);
      this.form.setFieldsValue({ fileList: event });
    },
    FileBeforeUpload(event, status) {
      this.loading = true;
    },
    FileSuccessUpload(event) {
      this.loading = false;
    },
    // 删除附件
    handleRemove(info) {
      console.log("🚀 ~ file: index.vue:280 ~ handleRemove ~ info:", info);
      this.form.setFieldsValue({
        fileList: undefined,
      });
    },
    down(record) {
      window.open(
        process.env.VUE_APP_READ +
          `?file=` +
          process.env.VUE_APP_READ_BASE_URL +
          `/sk/file/getJson?fileId=${record["fileId"] || record["uid"]}` +
          `%26serviceUrl=${process.env.VUE_APP_READ_BASE_URL}`
      );
    },
    del(record) {
      if (record["status"] != "done") {
        this.formState.fileId = new Date().valueOf();
      } else {
        this.$refs.fileUploader.handleFileRemoveDefault({
          ...record,
        });
      }
    },
  },
};
</script>
<style src="@wangeditor/editor/dist/css/style.css"></style>

<style scoped lang="less">
@import url("./index.less");
</style>
