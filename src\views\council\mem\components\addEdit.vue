<template>
  <div>
    <a-modal
      :visible="visible"
      :destroyOnClose="true"
      title="委员基本信息"
      width="1400px"
      @ok="handleOk"
      @cancel="cancel"
      :bodyStyle="{ overflow: 'auto', height: '68vh' }"
    >
      <Detail v-if="isDetail" :formState="formState" :imageUrl="imageUrl" />
      <div v-else class="page">
        <div>
          <a-form :form="form" :labelCol="labelCol" :wrapperCol="wrapperCol">
            <a-row>
              <a-col :span="8">
                <a-form-item label="委员证号">
                  <a-input
                    v-decorator="[
                      'memNum',
                      {
                        rules: [{ required: false, message: '请输入' }]
                      }
                    ]"
                    placeholder="请输入"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="姓名/性别">
                  <a-input
                    style="width: 100px"
                    v-decorator="[
                      'name',
                      {
                        rules: [{ required: true, message: '请输入' }]
                      }
                    ]"
                    placeholder="请输入"
                  />
                  <a-form-item label="" style="display: inline-block; margin-left: 4px;margin-bottom: 0;">
                    <zh-dict
                    :parentDisabled="true"
                      style="width: 100px"
                      codeType="SEXCODE"
                      v-decorator="['sexCode', { rules: [{ required: true, message: '请输入' }] }]"
                      placeholder="请输入"
                    />
                  </a-form-item>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="民族/组别">
                  <zh-dict
                    :parentDisabled="true"
                    style="width: 100px"
                    codeType="gb3304"
                    v-decorator="[
                      'nationCode',
                      {
                        rules: [{ required: true, message: '请点击选择' }]
                      }
                    ]"
                    @change="
                      (val, obj) => {
                        formState.nationName = obj['codeName']
                      }
                    "
                  >
                  </zh-dict>
                  <a-form-item label="" style="display: inline-block; margin-left: 4px;margin-bottom: 0;">
                    <zh-dict
                    :parentDisabled="true"
                      style="width: 100px"
                      codeType="WYSSZB"
                      v-decorator="[
                        'memGroupPartCode',
                        {
                          rules: [{ required: false, message: '请输入' }]
                        }
                      ]"
                    />
                  </a-form-item>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="继任委员证号">
                  <a-input
                    v-decorator="[
                      'lastMemNum',
                      {
                        rules: [{ required: false, message: '请输入' }]
                      }
                    ]"
                    placeholder="请输入"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="身份证号码">
                  <a-input
                    v-decorator="[
                      'idCard',
                      {
                        rules: [
                          {
                            required: false,
                            message: '请输入正确号码',
                            pattern: /^([1-9]\d{5})(\d{4})(\d{2})(\d{2})(\d{3})(\d|X)$/
                          }
                        ]
                      }
                    ]"
                    placeholder="请输入"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="籍贯">
                  <a-input
                    v-decorator="[
                      'nativePlace',
                      {
                        rules: [{ required: false, message: '请输入' }]
                      }
                    ]"
                    placeholder="请输入"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="出生地">
                  <a-input
                    v-decorator="[
                      'birthplace',
                      {
                        rules: [{ required: false, message: '请输入' }]
                      }
                    ]"
                    placeholder="请输入"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="健康状况">
                  <zh-dict
                  :parentDisabled="true"
                    codeType="gb22613"
                    v-decorator="[
                      'healthCode',
                      {
                        rules: [{ required: false, message: '请输入' }]
                      }
                    ]"
                    placeholder="请输入"
                    @change="
                      (val, obj) => {
                        formState.healthName = obj['codeName']
                      }
                    "
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="有何专长">
                  <a-input
                    v-decorator="[
                      'speciality',
                      {
                        rules: [{ required: false, message: '请输入' }]
                      }
                    ]"
                    placeholder="请输入"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="政治面貌">
                  <zh-dict
                    :parentDisabled="true"
                    codeType="gb4762"
                    v-decorator="[
                      'politicalAffiliationCode',
                      {
                        rules: [{ required: false, message: '请点击选择' }]
                      }
                    ]"
                    @change="
                      (val, obj) => {
                        formState.politicalAffiliationName = obj['codeName']
                      }
                    "
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="党派">
                  <zh-dict
                     :parentDisabled="true"
                    codeType="gb4762"
                    v-decorator="[
                      'politicalCode',
                      {
                        rules: [{ required: false, message: '请点击选择' }]
                      }
                    ]"
                    @change="
                      (val, obj) => {
                        formState.politicalName = obj['codeName']
                      }
                    "
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="入党时间">
                  <a-date-picker
                    v-decorator="[
                      'politicalDate',
                      {
                        rules: [{ required: false, message: '请输入' }]
                      }
                    ]"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="职称">
                  <zh-dict
                    :parentDisabled="true"
                    codeType="zb094"
                    v-decorator="[
                      'rankCode',
                      {
                        rules: [{ required: false, message: '请输入' }]
                      }
                    ]"
                    @change="
                      (val, obj) => {
                        formState.rankName = obj['codeName']
                      }
                    "
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="第二党派">
                  <zh-dict
                    :parentDisabled="true"
                    codeType="gb4762"
                    v-decorator="[
                      'politicalTwoCode',
                      {
                        rules: [{ required: false, message: '请输入' }]
                      }
                    ]"
                    placeholder="请输入"
                    @change="
                      (val, obj) => {
                        formState.politicalTwoName = obj['codeName']
                      }
                    "
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="第二党派入党时间">
                  <a-date-picker
                    v-decorator="[
                      'politicalTwoDate',
                      {
                        rules: [{ required: false, message: '请输入' }]
                      }
                    ]"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="委员类型">
                  <zh-dict
                    @change="oldMemTypeChange"
                    codeType="SJZXWYLX"
                    v-decorator="[
                      'oldMemType',
                      {
                        rules: [{ required: true, message: '请输入' }]
                      }
                    ]"
                    placeholder="请输入"
                    disabled
                  />
             
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="委员界别" >
                    <zh-dict
                    :parentDisabled="true"
                      codeType="tb02"
                      v-decorator="[
                        'jbCode',
                        {
                          rules: [{ required: true, message: '请输入' }]
                        }
                      ]"
                      @change="
                        (val, obj) => {
                          formState.jbName = obj['codeName']
                        }
                      "
                    />
                  </a-form-item>
              </a-col>
              <!-- 委员类型oldMemType 选全国时显示 是否兼任市政协委员： 1-全国兼任市政协 -->
                <!-- 委员类型oldMemType 选择市政协时显示 是否兼任区县政协委员： 2-市政协兼任区县 -->
              <a-col :span="8" v-if="listobj.wytype=='1'">
                <a-form-item :label="theLabel">
                  <a-select  v-decorator="[
                      'memTypeAll',
                      {
                        rules: [{ required: true, message: '请选择' }]
                      }
                    ]">
                    <a-select-option value="1"> 是 </a-select-option>
                    <a-select-option value="0"> 否 </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
           
              <a-col :span="8">
                <a-form-item label="委员职务">
                  <zh-dict
                  :parentDisabled="true"
                    codeType="zb14"
                    v-decorator="[
                      'committeePostStatus',
                      {
                        rules: [{ required: true, message: '请输入' }]
                      }
                    ]"
                    placeholder="请输入"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="委员任职时间">
                  <a-date-picker
                    v-decorator="[
                      'committeePostDate',
                      {
                        rules: [{ required: false, message: '请输入' }]
                      }
                    ]"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="24">
                <a-form-item
                  label="委员任免记录"
                  :labelCol="{ span: 3 }"
                  :wrapperCol="{ span: 21 }"
                >
                  <a-textarea
                    :maxLength="500"
                    show-count
                    :autosize="{ minRows: 2, maxRows: 8 }"
                    v-decorator="[
                      'removalRecord',
                      {
                        rules: [{ required: false, message: '请输入' }]
                      }
                    ]"
                    placeholder="请输入"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="常委组成人员">
                  <zh-dict
                  :parentDisabled="true"
                    codeType="kz15"
                    v-decorator="[
                      'oftenMemPost',
                      {
                        rules: [{ required: false, message: '请输入' }]
                      }
                    ]"
                    placeholder="请输入"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="常委任职时间">
                  <a-date-picker
                    v-decorator="[
                      'oftenMemPostDate',
                      {
                        rules: [{ required: false, message: '请输入' }]
                      }
                    ]"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="常委离任时间">
                  <a-date-picker
                    v-decorator="[
                      'oftenMemPostOutDate',
                      {
                        rules: [{ required: false, message: '请输入' }]
                      }
                    ]"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="24">
                <a-form-item
                  label="常委任免记录"
                  :labelCol="{ span: 3 }"
                  :wrapperCol="{ span: 21 }"
                >
                  <a-textarea
                    :maxLength="500"
                    show-count
                    :autosize="{ minRows: 2, maxRows: 8 }"
                    v-decorator="[
                      'oftenMemRemovalRecord',
                      {
                        rules: [{ required: false, message: '请输入' }]
                      }
                    ]"
                    placeholder="请输入"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="专委会名称">
                  <zh-dict
                  :parentDisabled="true"
                    codeType="tb04"
                    v-decorator="[
                      'specialCode',
                      {
                        rules: [{ required: false, message: '请输入' }]
                      }
                    ]"
                    placeholder="请输入"
                    @change="
                      (val, obj) => {
                        formState.specialName = obj['codeName']
                      }
                    "
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="专委会职务">
                  <zh-dict
                  :parentDisabled="true"
                    codeType="tb04_zw"
                    v-decorator="[
                      'specialPostCode',
                      {
                        rules: [{ required: false, message: '请输入' }]
                      }
                    ]"
                    placeholder="请输入"
                    @change="
                      (val, obj) => {
                        formState.specialPostName = obj['codeName']
                      }
                    "
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="专委会任职时间">
                  <a-date-picker
                    v-decorator="[
                      'specialPostDate',
                      {
                        rules: [{ required: false, message: '请输入' }]
                      }
                    ]"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="专委会离任时间">
                  <a-date-picker
                    v-decorator="[
                      'specialPostOutDate',
                      {
                        rules: [{ required: false, message: '请输入' }]
                      }
                    ]"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <!-- <a-col :span="8">
                <a-form-item label="区县政协职务">
                  <zh-dict
                    codeType="wydqxz_zw"
                    v-decorator="[
                      'regionalPost',
                      {
                        rules: [{ required: false, message: '请输入' }]
                      }
                    ]"
                    placeholder="请输入"
                    @change="
                      (val, obj) => {
                        formState.regionalPostName = obj['codeName']
                      }
                    "
                  />
                </a-form-item>
              </a-col> -->
              <a-col :span="8">
                <a-form-item label="区县政协">
                  <a-select
                    allowClear
                    v-decorator="[
                      'districtsCode',
                      {
                        rules: [{ required: false, message: '请选择' }]
                      }
                    ]"
                    placeholder="请选择"
                  >
                    <a-select-option
                      v-for="(item, index) in qhData"
                      :key="index"
                      :value="item.code"
                      :dataRef="item"
                    >
                      {{ item.name }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="区县政协职务">
                  <zh-dict
                  :parentDisabled="true"
                    codeType="wydqxz_zw"
                    v-decorator="[
                      'regionalPost',
                      {
                        rules: [{ required: false, message: '请输入' }]
                      }
                    ]"
                    placeholder="请输入"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="24">
                <a-form-item
              label="专委会任免记录"
              :labelCol="{ span: 3 }"
              :wrapperCol="{ span: 21 }"
            >
              <a-textarea
                    :maxLength="500"
                    show-count
                    :autosize="{ minRows: 2, maxRows: 8 }"
                    v-decorator="[
                      'specialRemovalRecord',
                      {
                        rules: [{ required: false, message: '请输入' }]
                      }
                    ]"
                    placeholder="请输入"
                  />
            </a-form-item>
              </a-col>
              <a-col :span="24">
                <a-form-item
                  label=" 区县政协安排记录"
                  :labelCol="{ span: 3 }"
                  :wrapperCol="{ span: 21 }"
                >
                  <a-textarea
                    :maxLength="500"
                    show-count
                    :autosize="{ minRows: 2, maxRows: 8 }"
                    v-decorator="[
                      'regionalRecord',
                      {
                        rules: [{ required: false, message: '请输入' }]
                      }
                    ]"
                    placeholder="请输入"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="全日制学历">
                  <zh-dict
                  :parentDisabled="true"
                    codeType="qrz_xl"
                    v-decorator="[
                      'fullEducationCode',
                      {
                        rules: [{ required: false, message: '请输入' }]
                      }
                    ]"
                    placeholder="请输入"
                    @change="
                      (val, obj) => {
                        formState.fullEducationName = obj['codeName']
                      }
                    "
                    :filter="(data)=>{
                      return data.filter(item=>['01','02','03','04','05','06','07','08'].includes(item.code))
                    }"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="全日制学位">
                  <a-input
                    v-decorator="[
                      'fullEducationDegreeCode',
                      {
                        rules: [{ required: false, message: '请输入' }]
                      }
                    ]"
                    placeholder="请输入"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="全日制院校">
                  <a-input
                    v-decorator="[
                      'fullEducationSchool',
                      {
                        rules: [{ required: false, message: '请输入' }]
                      }
                    ]"
                    placeholder="请输入"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="全日制专业">
                  <a-input
                    v-decorator="[
                      'fullEducationSpecialty',
                      {
                        rules: [{ required: false, message: '请输入' }]
                      }
                    ]"
                    placeholder="请输入"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="在职学历">
                  <zh-dict
                  :parentDisabled="true"
                    codeType="qrz_xl"
                    v-decorator="[
                      'workEducationCode',
                      {
                        rules: [{ required: false, message: '请输入' }]
                      }
                    ]"
                    placeholder="请输入"
                    @change="
                      (val, obj) => {
                        formState.workEducationName = obj['codeName']
                      }
                    "
                    :filter="(data)=>{
                      return data.filter(item=>['1','2','3','4','5','6'].includes(item.code))
                    }"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="在职学位">
                  <a-input
                    v-decorator="[
                      'workEducationDegreeCode',
                      {
                        rules: [{ required: false, message: '请输入' }]
                      }
                    ]"
                    placeholder="请输入"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="在职院校">
                  <a-input
                    v-decorator="[
                      'workEducationSchool',
                      {
                        rules: [{ required: false, message: '请输入' }]
                      }
                    ]"
                    placeholder="请输入"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="在职专业">
                  <a-input
                    v-decorator="[
                      'workEducationSpecialty',
                      {
                        rules: [{ required: false, message: '请输入' }]
                      }
                    ]"
                    placeholder="请输入"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="参加工作时间">
                  <a-date-picker
                    v-decorator="[
                      'workDate',
                      {
                        rules: [{ required: false, message: '请输入' }]
                      }
                    ]"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="16">
                <a-form-item label="单位及职务" :labelCol="{ span: 5 }" :wrapperCol="{ span: 19 }">
                  <a-input
                    :maxLength="200"
                    v-decorator="[
                      'workAndPost',
                      {
                        rules: [{ required: true, message: '请输入' }]
                      }
                    ]"
                    placeholder="请输入"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="单位电话">
                  <a-input
                    v-decorator="[
                      'workUnitPhone',
                      {
                        rules: [
                          {
                            required: false,
                            message: '请输入',
                            pattern: /^[0-9]*$/
                          }
                        ]
                      }
                    ]"
                    placeholder="请输入"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="兼任职务">
                  <a-input
                    v-decorator="[
                      'otherWorkPost',
                      {
                        rules: [{ required: false, message: '请输入' }]
                      }
                    ]"
                    placeholder="请输入"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="电子邮箱">
                  <a-input
                    v-decorator="[
                      'email',
                      {
                        rules: [{ required: false, message: '请输入' }]
                      }
                    ]"
                    placeholder="请输入"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="移动电话">
                  <a-input
                    v-decorator="[
                      'phone',
                      {
                        rules: [
                          {
                            required: false,
                            message: '请输入正确号码',
                            pattern: /^[0-9]*$/
                          }
                        ]
                      }
                    ]"
                    placeholder="请输入"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="24">
                <a-form-item label="联系地址" :labelCol="{ span: 3 }" :wrapperCol="{ span: 21 }">
                  <a-input
                    :maxLength="100"
                    :showCount="true"
                    v-decorator="[
                      'address',
                      {
                        rules: [{ required: false, message: '请输入' }]
                      }
                    ]"
                    placeholder="请输入"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="24">
                <a-form-item label="个人简历" :labelCol="{ span: 3 }" :wrapperCol="{ span: 21 }">
                  <a-textarea
                    :autosize="{ minRows: 4, maxRows: 8 }"
                    v-decorator="[
                      'resume',
                      {
                        rules: [{ required: false, message: '请输入' }]
                      }
                    ]"
                    placeholder="请输入"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="24">
                <a-form-item label="主要表现" :labelCol="{ span: 3 }" :wrapperCol="{ span: 21 }">
                  <a-textarea
                    :autosize="{ minRows: 2, maxRows: 8 }"
                    v-decorator="[
                      'expression',
                      {
                        rules: [{ required: false, message: '请输入' }]
                      }
                    ]"
                    placeholder="请输入"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="24">
                <a-form-item label="主要成就" :labelCol="{ span: 3 }" :wrapperCol="{ span: 21 }">
                  <a-textarea
                    :autosize="{ minRows: 2, maxRows: 8 }"
                    v-decorator="[
                      'achievement',
                      {
                        rules: [{ required: false, message: '请输入' }]
                      }
                    ]"
                    placeholder="请输入"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="24">
                <a-form-item label="备注" :labelCol="{ span: 3 }" :wrapperCol="{ span: 21 }">
                  <a-textarea
                    :autosize="{ minRows: 2, maxRows: 8 }"
                    v-decorator="[
                      'remark',
                      {
                        rules: [{ required: false, message: '请输入' }]
                      }
                    ]"
                    placeholder="请输入"
                  />
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </div>

        <!-- <div>
          <z-image-uploader
            :parentId="formState.id"
            :limit="1"
            v-model="formState.fileList"
            configGroup="zx"
            ref="imgUploader"
          />
        </div> -->
        <div class="upimg">
          <a-upload
            :maxCount="1"
            :file-list="fileList"
            name="file"
            list-type="picture-card"
            class="avatar-uploader"
            :show-upload-list="false"
            :action="`${baseUrl}/mem/upload`"
            @change="handleChange"
            :data="handleData"
            :headers="{
              Token: tokenObj.value
            }"
          >
            <img v-if="imageUrl" :src="imageUrl" alt="头像" class="img" />
            <div v-else>
              <div class="add">+</div>
              <div class="ant-upload-text">上传头像</div>
            </div>
          </a-upload>
          <div class="img-text">2寸近期免冠照片</div>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script>
import { auth as http } from '@zh/common-utils/http'
import Detail from './detail.vue'
import dayjs from 'dayjs'

// 文件、图片类型
import MimeType from '@zh/common-utils/file/mime-type'
import { message } from 'ant-design-vue'


export default {
  created() {
    // 在created中初始化form实例
    this.form = this.$form.createForm(this, { 
      name: 'council_mem'
    })
  },
  components: {
    Detail
  },
  props: {
    pId: String,
    callBack: Function,
    session: Object
  },
  data() {
    return {
      baseUrl: process.env.VUE_APP_API_BASE_URL,
      isDetail: false,
      visible: false,
      labelCol: { span: 9 },
      wrapperCol: { span: 15 },
      mimeType: MimeType,
      qhData: [],
      formState: {
        name: undefined,
        region: undefined,
        date1: undefined,
        delivery: undefined,
        type: undefined,
        resource: undefined,
        desc: undefined,
      },
      imgLoading: false,
      imageUrl: '',
      imageUrlFilePath: '',
      fileList: [],
      tokenObj: JSON.parse(localStorage.getItem('access-token')),
      listobj:{},
      theLabel: '是否兼任市政协委员',
    }
  },

  methods: {
    open(record, isDetail = false) {
      this.visible = true
      this.isDetail = isDetail
      this.listobj = record
      this.$nextTick(() => {
        if (record.id) {
        this.getDetail(record.id)
      }
      if(record.wytype&&record.wytype !='999') {
        console.log('record.wytype====', record);
        if(record.wytype == '1') {
          this.theLabel = '是否兼任市政协委员'
        }
        // if(record.wytype == '2') {
        //   this.theLabel = '是否兼任全国政协委员'
        // }
        this.form.setFieldsValue({ oldMemType: record.wytype,memTypeAll: '0' })
      }
      })
   
    
      // 委员职务 committeePostStatus 默认在任
      const v = this.form.getFieldValue('committeePostStatus')
      if(!v){
        this.form.setFieldsValue({ committeePostStatus: 1 })
      }
    },
    handleData(file) {
      return {
        module: 'custom'
      }
    },
    getDetail(id) {
      if (id) {
        http.get(`/mem/find?id=${id}`).then((res) => {
          console.log('res=====',res);
          this.visible = true
          this.formState = res
          if(res.pic){
            // 下载头像
          this.getImg(res.pic)
          }else{
            this.imageUrl = ''
          }
          setTimeout(() => {
            this.form.setFieldsValue({ ...res })
          }, 200)
        })
      } else {
        this.formState = {}
        this.form.resetFields()
      }
    },
    // 下载头像
    getImg(param) {
      if (param) {
        http.post(`/mem/downloadByPathBase64`, { filePath: param }).then((imgRes) => {
          // this.imageUrl = URL.createObjectURL(res)
          if(imgRes){
            this.imageUrl = `data:image/png;base64,${imgRes}`
          }else{
            this.imageUrl = ''
          }
        })
      } else {
        this.imageUrl = ''
      }
    },
    handleChange(info) {
      this.fileList = info.fileList
      if (info.file.status === 'uploading') {
        this.imgLoading = true
        return
      }
      if (info.file.status === 'done') {
        const fileObj = info.file
        const { response: { code = 500, data = '' } = {} } = fileObj
        if (code == 0) {
          this.getImg(data)
          this.imageUrlFilePath = data
        }
      }
      if (info.file.status === 'error') {
        this.imgLoading = false
        message.error('upload error')
      }
    },
    cancel() {
      this.visible = false
      this.formState = {}
      this.form.resetFields()
      ;(this.fileList = []), (this.imageUrl = ''), (this.this.imageUrlFilePath = '')
    },
    valChange(val) {
      console.log('🚀 ~ file: addEdit.vue:1369 ~ valChange ~ val:', val)
    },
    handleOk() {
      this.form.validateFields((err, value) => {
        if (!err) {
          console.info('success', this.img)
          Object.keys(value).forEach((key) => {
            if (key.includes('Date') && value[key]) {
              value[key] = dayjs(value[key]).valueOf()
            }
          })
          let url = '/mem/save'
          if (this.formState['id']) {
            url = '/mem/update'
          }
          http
            .post(url, {
              sessionCode: this.listobj['sessionCode'],
              ...this.formState,
              ...value,
              pic: this.imageUrlFilePath || undefined
            })
            .then((res) => {
              this.cancel()
              this.$emit('callBack')
            })
        } else {
          console.error('error', err)
          message.error('请完善表单内容')
        }
      })
      // this.cancel();
    },
    // 获取区划数据
    getQhData() {
      http.get(`/cppccSession/getUnitInfo?needQx=1`).then((res) => {
        this.qhData = res
      })
    },
    oldMemTypeChange(val) {
      console.log('oldMemTypeChange====', val);
      console.log('formState====', this.form.getFieldValue('oldMemType') );
    }
  },
  mounted() {
    this.getQhData()
  }
}
</script>

<style scoped lang="less">
@import url('./addEdit.less');

.tit {
  border-left: 6px solid #0da1ff;
  height: 21px;
  line-height: 21px;
  color: #333;
  font-size: 18px;
  padding-left: 9px;
  margin: 0px 0px 30px 10px;
  text-align: left;
}

.upimg {
  .add {
    font-size: 20px;
  }

  .img {
    // position: absolute;
    // right: 30px;
    // top: 80px;
    width: 110px;
    height: 120px;
  }

  .img-text {
    width: 126px;
    text-align: center;
    color: red;
    font-size: 12px;
    text-align: center;
  }
}
</style>
