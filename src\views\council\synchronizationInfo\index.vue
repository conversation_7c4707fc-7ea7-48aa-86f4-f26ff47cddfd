<template>
  <div>
    <a-tabs v-model="active">
      <a-tab-pane key="1" tab="届次同步"></a-tab-pane>
      <a-tab-pane key="2" tab="委员同步"></a-tab-pane>
    </a-tabs>
    <a-table
      :rowKey="(record) => record.id"
      :dataSource="dataSource"
      :columns="columns"
      :pagination="pagination"
      @change="
        (obj) => {
          getList(obj.current, obj.pageSize);
        }
      "
    >
      <span slot="createdTime" slot-scope="text">
        {{ text ? dayjs(text).format("YYYY-MM-DD") : text }}
      </span>
      <span slot="action" slot-scope="text, record">
        <a v-if="record.status == 2" @click="retry(record)">重试</a>
      </span>
    </a-table>
  </div>
</template>

<script>
import { auth as http } from "@zh/common-utils/http";
import dayjs from "dayjs";

export default {
  data() {
    return {
      dayjs,
      hasCurrTerm: "1",
      dataSource: [],
      columns: [
        {
          title: "名称",
          dataIndex: "bizName",
          key: "bizName",
          width: 120,
        },
        {
          title: "同步系统",
          dataIndex: "systemCodeName",
          key: "systemCodeName",
          width: 120,
        },
        {
          title: "同步时间",
          dataIndex: "createdTime",
          key: "createdTime",
          width: 100,
          ellipsis: { showTitle: true },
        },
        {
          title: "同步结果",
          dataIndex: "statusName",
          key: "statusName",
          width: 400,
        },
        {
          title: "操作",
          width: 200,
          scopedSlots: { customRender: "action" },
        },
      ],
      pageNum: 1,
      pageSize: 10,
      pagination: {},
      active: "1",
    };
  },
  mounted() {
    this.getList();
  },
  methods: {
    getList(pageNum = 1, pageSize = 10) {
      http
        .post("/other/perform/sync-mem-log/page", {
          pageNum: pageNum || this.pageNum,
          pageSize: pageSize || this.pageSize,
          bizType: this.active === "1" ? "session" : "mem",
        })
        .then((res) => {
          const { currPage, pageSize, list, totalCount } = res;
          this.dataSource = list;
          this.pageNum = currPage;
          this.pageSize = pageSize;
          this.pagination = {
            current: currPage,
            total: totalCount,
          };
        });
    },
    retry(record){
      http.get(`/other/perform/sync-mem-log/retry?id=${record.id}`).then((res) => {
        if (!res.data) {
          this.message.error(res.msg || "重试失败");
          return;
        }
        this.message.success("重试成功");
        this.getList();
      }).catch(() => {
        this.message.error("重试失败");
      });
    }
  },
  watch: {
    active: {
      handler(nval) {
        this.getList();
      },
      immediate: true,
    },
  },
};
</script>

<style lang="less" scoped>
.sear {
  margin-bottom: 18px;
}
</style>
