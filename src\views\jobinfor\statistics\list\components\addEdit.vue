<template>
  <div>
    <a-modal
      :visible="visible"
      :destroyOnClose="true"
      width="1400px"
      @ok="handleOk"
      @cancel="() => cancel()"
      :bodyStyle="{
        overflow: 'auto',
        'max-height': '68vh',
        'min-height': '500px',
      }"
      :footer="false"
    >
      <div class="title">
        <div>
          <p style="font-size: 24px">
            <span style="font-weight: bold"> {{ propObj.unitName }} </span>
            <!-- 信息被采用情况 -->
            采用信息查询
          </p>
          <p style="font-size: 18px">
            总上报 {{ pagination.total || 0 }} 条
            <!-- ，被采用
            {{ formState.totalCollect }} 条 -->
          </p>
        </div>
        <a-space class="btn">
          <a-button type="primary" @click="down"> 导出Excel </a-button>
        </a-space>
      </div>
      <table class="table">
        <colgroup width="400"></colgroup>
        <colgroup width="400"></colgroup>
        <colgroup width="400"></colgroup>
        <colgroup width="400"></colgroup>
        <colgroup width="400"></colgroup>
        <colgroup width="400"></colgroup>
        <!-- <colgroup width="400"></colgroup>
        <colgroup width="400"></colgroup> -->
        <thead>
          <tr style="font-size: 16px; font-weight: bold">
            <td>采用标题</td>
            <td>采用时间</td>
            <td>报送时间</td>
            <td>发布时间</td>
            <td>期号</td>
            <!-- <td>加分类型</td> -->
            <td>报送单位</td>
            <!-- <td>得分</td> -->
          </tr>
        </thead>
        <tbody>
          <tr v-for="(item, index) in data" :key="index">
            <td style="text-align: left; padding-left: 4px">{{ item["titel"] }}</td>
            <td>
              {{ item["accpetDate"] ? dayjs(item["accpetDate"]).format("YYYY-MM-DD") : undefined }}
            </td>
            <td>
              {{ item["reportDate"] ? dayjs(item["reportDate"]).format("YYYY-MM-DD") : undefined }}
            </td>
            <td>
              {{ item["publishDate"] ? dayjs(item["publishDate"]).format("YYYY-MM-DD") : undefined }}
            </td>
            <td>{{ item["oaYear"] ? `${item["oaYear"]}年` : "" }}{{ item["oaIssue"] ? `第${item["oaIssue"]}期` : "" }}</td>
            <!-- <td>
              {{ item["bonusType"] }}
            </td> -->
            <td>{{ item["reportUnitName"] }}</td>
            <!-- <td>{{ item["score"] }}</td> -->
          </tr>
        </tbody>
      </table>
    </a-modal>
  </div>
</template>

<script>
import { auth as http } from "@zh/common-utils/http";
import dayjs from "dayjs";
import qs from "qs";
export default {
  components: {},
  props: {},
  data() {
    return {
      dayjs,
      visible: false,
      propObj: {},
      formState: {},
      data: [],
      pageNum: 1,
      pageSize: 10,
      pagination: {},
    };
  },
  methods: {
    open(record) {
      if (record) {
        this.propObj = { ...record };
        this.getDetail();
      }
      this.visible = true;
    },
    getDetail() {
      http
        .post("/workReport/list", {
          ...this.propObj,
          pageNum: this.pageNum,
          pageSize: this.pageSize,
          pageType: 3,
        })
        .then((res) => {
          const { currPage, pageSize, list, totalCount } = res;
          this.data = list;
          this.pageNum = currPage;
          this.pageSize = pageSize;
          this.pagination = {
            current: currPage,
            total: totalCount,
          };
        });
    },
    cancel() {
      this.visible = false;
      this.formState = {};
      this.propObj = {};
      this.data = [];
    },
    handleOk() {
      this.cancel();
    },
    down() {
      http
        .post(
          `/workReport/listExport`,
          {
            pageNum: 1,
            pageSize: 9999,
            pageType: 3,
            ...this.propObj,
          },
          { responseType: "blob" }
        )
        .then((res) => {
          console.log("🚀 ~ file: addEdit.vue:124 ~ .then ~ res:", res);
          const blob = res.data;
          let contentDisposition = decodeURI(res.headers["content-disposition"]) || "";
          const url = window.URL.createObjectURL(blob);

          let link = document.createElement("a");

          link.style.display = "none";

          link.href = url;

          link.setAttribute("download", contentDisposition.substring(20)); //文件名后缀记得添加，我写的zip

          document.body.appendChild(link);

          link.click();

          document.body.removeChild(link); //下载完成移除元素

          window.URL.revokeObjectURL(url); //释放掉blob对象
        });
    },
  },
};
</script>

<style scoped lang="less">
.title {
  display: flex;
  position: relative;
  margin: 40px auto;
  p {
    margin: 0;
  }
  & > div:first-child {
    width: 100%;
    text-align: center;
  }
  .btn {
    position: absolute;
    right: 26px;
    top: 12px;
  }
}
.table {
  margin: auto;
  table {
    table-layout: fixed;
  }

  td {
    border: 1px solid #b7b7b7;
    text-align: center;
    height: 46px;
  }
  thead > tr > td {
    height: 60px;
  }
}
</style>
