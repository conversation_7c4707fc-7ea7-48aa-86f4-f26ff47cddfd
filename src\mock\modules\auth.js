import Mock from 'mockjs2'
const random = Mock.Random

export default {
  // key: enable#method#url
  'false#post#/sys/sso/login': ({ body, url }) => {
    return {
      code: 0,
      message: 'success',
      data: {
        lastLoginTime: '2022-08-04 16:29:41',
        validTime: '7200',
        token: 'mockjs-token'
      }
    }
  },
  'false#get#/sys/sso/getUserSimleInfo': ({ body, url }) => {
    return {
      code: 0,
      message: 'success',
      data: {
        userName: '系统管理员5',
        roleIds: '1457346550614200322,1496047830395891713,1506450252801007617',
        id: '10092',
        unitUserId: '10092',
        depts: [{
          deptName: '市公安局',
          isDefault: true,
          unitName: '市公安局',
          deptId: '1463836065650294785',
          unitId: '1463836065650294785',
          unitUserId: '10092'
        }]
      }
    }
  },
  // key: enable#method#url
  'false#get#/sys/sso/userMenu': ({ body, url }) => {
    return {
      code: 0,
      data: getMenuList()
    }
  }
}

function getMenuList () {
  const parentId = 0, initId = 1, list = []
  const tree = require('./menu.json')
  tree.map((node, index) => {
    node.id = `${index + 1}`
    node.parentId = `${parentId}`
    _getMenuList(node, list)
  })
  return list
}

function _getMenuList (node, list) {
  node.children?.map((child, index) => {
    child.id = `${node.id}-${index + 1}`
    child.parentId = `${node.id}`
    _getMenuList(child, list)
  })
  list.push(node)
}