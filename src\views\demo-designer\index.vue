<template>
  <div>
    <a-row>
      <a-col span="5">
        <a-radio-group v-model="page"
                       button-style="solid">
          <!-- <% if (modules.includes('yunfeng')) { %> -->
          <a-radio-button value="form">
            表单设计
          </a-radio-button>
          <a-radio-button value="table">
            列表设计
          </a-radio-button>
          <!-- <% } %> -->
          <!-- <% if (modules.includes('feiliu')) { %> -->
          <a-radio-button value="flow">
            流程设计
          </a-radio-button>
          <!-- <% } %> -->
        </a-radio-group>
      </a-col>
      <a-button @click="onSave">保存</a-button>
    </a-row>

    <!-- <% if (modules.includes('yunfeng')) { %> -->
    <Form v-if="page=='form'"
          ref="form"
          @formDesignData="handleFormDesignData"
          @serialDesign="handleSerialDesign"
          @formId="handleFormId" />

    <Table v-if="page=='table'"
           ref="table"
           :formDesignData="formDesignData" />
    <!-- <% } %> -->

    <!-- <% if (modules.includes('feiliu')) { %> -->
    <Flow v-if="page=='flow'"
          ref="flow"
          :serialDesign="serialDesign"
          :formId="formId" />
    <!-- <% } %> -->
  </div>
</template>

<script>
// <% if (modules.includes('yunfeng')) { %>
import Form from './form.vue'
import Table from './table.vue'
// <% } -%>
// <% if (modules.includes('feiliu')) { -%>
import Flow from './flow.vue'
// <% } -%>
const components = {}
// <% if (modules.includes('yunfeng')) { -%>
components.Form = Form
components.Table = Table
// <% } -%>
// <% if (modules.includes('feiliu')) { -%>
components.Flow = Flow
// <% } -%>

export default {
  components,
  data () {
    return {
      page: 'form',
      formDesignData: {},
      serialDesign: {},
      formId: ''
    }
  },
  created () { },
  methods: {
    handleFormDesignData (formDesignData) {
      this.formDesignData = formDesignData
    },
    handleSerialDesign (serialDesign) {
      this.serialDesign = serialDesign
    },
    handleFormId (formId) {
      this.formId = formId
    },
    onSave () {
      this.$refs[this.page].save()
    }
  }
}
</script>

<style lang="less" scoped>
</style>