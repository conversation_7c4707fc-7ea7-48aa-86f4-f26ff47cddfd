<template>
  <div>
    <a-modal :visible="visible" :destroyOnClose="true" title="信息审核" width="1300px" @ok="handleOk" @cancel="() => cancel()" :bodyStyle="{ overflow: 'auto' }" :footer="false">
      <a-form :form="form" :labelCol="labelCol" :wrapperCol="wrapperCol" @submit="handleOk">
        <a-form-item label="报送标题">
          <a-input
            v-decorator="[
              'titel',
              {
                rules: [{ required: true, message: '请输入' }],
              },
            ]"
            placeholder="请输入"
          />
        </a-form-item>
        <a-row>
          <a-col :span="8">
            <a-form-item label="报送时间" :labelCol="{ span: 12 }" :wrapperCol="{ span: 12 }">
              <a-date-picker
                v-decorator="[
                  'reportDate',
                  {
                    rules: [{ required: true, message: '请输入' }],
                  },
                ]"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="报送单位" :labelCol="{ span: 10 }" :wrapperCol="{ span: 10 }">
              <z-select-unit
                v-if="isSel"
                @change="rpChange"
                v-decorator="[
                  'reportUnitId',
                  {
                    rules: [{ required: true, message: '请输入' }],
                  },
                ]"
              />
              <a-input
                v-else
                v-decorator="[
                  'reportUnitName',
                  {
                    rules: [{ required: true, message: '请输入' }],
                  },
                ]"
              />
            </a-form-item>
            <a-button type="primary" class="cut" @click="selChange">
              {{ isSel ? "填写" : "选择" }}
            </a-button>
          </a-col>
          <a-col :span="8">
            <a-form-item label="接收单位" :labelCol="{ span: 6 }" :wrapperCol="{ span: 12 }">
              <z-select-unit
                @change="acChange"
                v-decorator="[
                  'acceptUnitId',
                  {
                    rules: [{ required: true, message: '请输入' }],
                  },
                ]"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="8">
            <a-form-item label="撰稿人" :labelCol="{ span: 12 }" :wrapperCol="{ span: 12 }">
              <a-input
                v-decorator="[
                  'writerName',
                  {
                    rules: [{ required: true, message: '请输入' }],
                  },
                ]"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="撰稿电话" :labelCol="{ span: 10 }" :wrapperCol="{ span: 12 }">
              <a-input
                v-decorator="[
                  'writerPhone',
                  {
                    rules: [{ required: true, message: '请输入' }],
                  },
                ]"
                placeholder="请输入"
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="审核人" :labelCol="{ span: 6 }" :wrapperCol="{ span: 12 }">
              <a-input
                v-decorator="[
                  'checkName',
                  {
                    rules: [{ required: true, message: '请输入' }],
                  },
                ]"
                placeholder="请输入"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-form-item label="上报内容">
          <div
            style="border: 1px solid #ccc"
            v-decorator="[
              'content',
              {
                rules: [{ required: true, message: '请输入' }],
              },
            ]"
          >
            <Toolbar style="border-bottom: 1px solid #ccc" :editor="editor" :defaultConfig="toolbarConfig" :mode="mode" />
            <Editor style="height: 300px; overflow-y: auto" v-model="html" :defaultConfig="editorConfig" :mode="mode" @onCreated="onCreated" />
          </div>
        </a-form-item>
        <a-form-item label="文件上传">
          <z-file-uploader
            v-decorator="[
              'fileList',
              {
                rules: [{ required: true, message: '请输入' }],
              },
            ]"
            :limit="1"
            accept=".doc,.docx,.wps,.Doc,.Docx,.Wps,.DOC,.DOCX,.WPS"
            configGroup="zx"
            :showUploadList="false"
            :parentId="formState.id"
            @input="handleChange"
            @FileBeforeUpload="FileBeforeUpload"
            @FileSuccessUpload="FileSuccessUpload"
            :handleRemove="handleRemove"
            v-model="formState.fileList"
            ref="fileUploader"
          />
        </a-form-item>

        <a-form-item label=" " :colon="false">
          <zh-progress :loading="loading">
            <a-table :dataSource="formState.fileList" :columns="columns">
              <span slot="name" slot-scope="text, record">
                {{ record["name"] || record["fileName"] }}
              </span>
              <span slot="type" slot-scope="text, record">
                {{ (record["name"] || record["fileName"]).split(".").pop() }}
              </span>
              <span slot="configGroup"> 原稿件 </span>
              <span slot="action" slot-scope="text, record">
                <a @click="down(record)">查看</a>
                <a-divider type="vertical" />
                <a style="color: red" @click="down(record, true)"> 下载 </a>
              </span>
            </a-table>
          </zh-progress>
        </a-form-item>

        <a-form-item label="退回意见">
          <div class="thyj" v-for="item in formState.auditList" :key="item">
            <div>{{ item.opinion }}</div>
            <div>{{ item.opinionDate }}</div>
          </div>
          <a-textarea
            :rows="4"
            v-decorator="[
              'opinion',
              {
                rules: [{ required: false, message: '请输入' }],
              },
            ]"
            placeholder="请输入"
          />
        </a-form-item>

        <div style="text-align: center">
          <a-space>
            <a-button type="primary" style="margin-right: 36px" @click="handleOk('2')"> 退回修改 </a-button>
            <a-button type="primary" style="margin-right: 36px" @click="handleOk('3')"> 归档 </a-button>
            <a-button type="primary" style="margin-right: 36px" @click="handleOk('4')"> 采编中 </a-button>
            <a-button type="primary" style="margin-right: 36px" @click="handleOk('1')"> 归档激活 </a-button>
            <a-button type="primary" @click="cancel"> 取消 </a-button>
          </a-space>
        </div>
      </a-form>
    </a-modal>
  </div>
</template>

<script>
import { auth as http } from "@zh/common-utils/http";
import dayjs from "dayjs";
// 文件、图片类型
import MimeType from "@zh/common-utils/file/mime-type";
import { Editor, Toolbar } from "@wangeditor/editor-for-vue";
import { message } from "ant-design-vue";
import { toolbarConfig, editorConfig } from "@/utils/common";

export default {
  components: {
    Editor,
    Toolbar,
  },
  props: {},
  data() {
    return {
      isDetail: false,
      isSel: true,
      form: this.$form.createForm(this, { name: "report_update" }),
      visible: false,
      labelCol: { span: 4 },
      wrapperCol: { span: 18 },
      mimeType: MimeType,
      tempState: {},
      formState: {},
      columns: [
        {
          title: "文件名称",
          scopedSlots: { customRender: "name" },
        },
        {
          title: "文件类型",
          scopedSlots: { customRender: "type" },
        },
        {
          title: "操作",
          width: 120,
          scopedSlots: { customRender: "action" },
        },
      ],

      editor: null,
      html: "",
      toolbarConfig: toolbarConfig,
      editorConfig: editorConfig,
      mode: "default", // or 'simple'
      unitName: "",
    };
  },
  methods: {
    onCreated(editor) {
      this.editor = Object.seal(editor); // 一定要用 Object.seal() ，否则会报错
    },
    open(id, isDetail = false) {
      if (id) {
        this.getDetail(id);
      }
      this.visible = true;
      this.isDetail = isDetail;
    },
    getDetail(id) {
      if (id) {
        http.get(`/workReport/find?id=${id}`).then((res) => {
          this.formState = res;
          this.isSel = this.formState["isSelect"] == 1;
          this.editor.setHtml(res["content"]);
          ["acceptUnitId", "reportUnitId"].forEach((key) => {
            if (this.formState[key]) {
              this.formState[key] = this.formState[key].split(",");
            }
          });
          http
            .post(`/system/SysFileRelation/list`, {
              businessId: id,
              fileType: "zx",
            })
            .then((res) => {
              console.log("🚀 ~ file: addEdit.vue:253 ~ .then ~ res:", res);
              this.formState.fileList = res;
            });
          setTimeout(() => {
            this.form.setFieldsValue({ ...res });
          }, 200);
        });
      } else {
        this.formState = {};
        this.form.resetFields();
      }
    },
    cancel() {
      this.visible = false;
      this.formState = {};
      this.form.resetFields();
    },
    selChange() {
      this.isSel = !this.isSel;
      if (this.isSel) {
        this.form.setFieldsValue({
          reportUnitName: undefined,
        });
      } else {
        this.form.setFieldsValue({
          reportUnitId: undefined,
        });
      }
    },
    acChange(ids, units) {
      this.tempState = {
        ...this.tempState,
        acceptUnitName: units.map((obj) => obj["name"]),
      };
    },
    rpChange(ids, units) {
      this.tempState = {
        ...this.tempState,
        reportUnitName: units.map((obj) => obj["name"]),
      };
    },
    handleOk(type) {
      this.form.validateFields((err, value) => {
        if (!err) {
          if (type == "2" && !value["opinion"]) {
            message.info("请填写退回意见");
            return;
          }
          console.info("success", this.img);
          Object.keys(value).forEach((key) => {
            if (key.includes("Date") && value[key]) {
              value[key] = dayjs(value[key]).valueOf();
            }
          });
          ["acceptUnitId", "reportUnitId"].forEach((key) => {
            if (value[key]) {
              value[key] = value[key].join(",");
            }
          });
          value["isSelect"] = this.isSel ? 1 : 0;
          value["auditStatus"] = type;
          let url = "/workReport/save";
          if (this.formState["id"]) {
            url = "/workReport/update";
          }
          http
            .post(url, {
              ...this.formState,
              ...value,
            })
            .then((res) => {
              this.cancel();
              this.$emit("callBack");
            });
        }
      });
      // this.cancel();
    },
    // 修改附件
    handleChange(event, status) {
      console.log("🚀 ~ file: index.vue:275 ~ handleChange ~ event:", event);
      this.form.setFieldsValue({ fileList: event });
    },
    FileBeforeUpload(event, status) {
      this.loading = true;
    },
    FileSuccessUpload(event) {
      this.loading = false;
    },
    // 删除附件
    handleRemove(info) {
      console.log("🚀 ~ file: index.vue:280 ~ handleRemove ~ info:", info);
      this.form.setFieldsValue({
        fileList: undefined,
      });
    },
    down(record, isDown) {
      if (isDown) {
        window.location.href = process.env.VUE_APP_API_BASE_URL + `/storage/download/${record["fileId"] || record["uid"]}`;
      } else {
        window.open(
          process.env.VUE_APP_READ +
            `?file=` +
            process.env.VUE_APP_READ_BASE_URL +
            `/sk/file/getJson?fileId=${record["fileId"] || record["uid"]}` +
            `%26serviceUrl=${process.env.VUE_APP_READ_BASE_URL}`
        );
      }
    },
    del(record) {
      this.$refs.fileUploader.handleFileRemoveDefault({
        ...record,
      });
    },
  },
};
</script>
<style src="@wangeditor/editor/dist/css/style.css"></style>

<style scoped lang="less">
@import url("./addEdit.less");
.cut {
  position: absolute;
  right: 0;
  top: 4px;
}
.thyj {
  & > div {
    vertical-align: top;
    display: inline-block;
  }
  & > div:first-child {
    width: 80%;
  }
  & > div:last-child {
    width: 20%;
    text-align: right;
  }
}
</style>
