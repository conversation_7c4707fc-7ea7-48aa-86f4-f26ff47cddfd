<template>
  <div class="cp-page">
    <div class="sear">
      <div style="font-size: 22px; margin-bottom: 20px; font-weight: bold">
        {{ areaName }}
      </div>
      <a-space>
        <div style="margin-right: 16px">
          <span>区划：</span>
          <a-space>
            <a-select
              v-model="area"
              placeholder="请输入"
              style="width: 100px"
              @change="qhChange"
            >
              <a-select-option
                v-for="(item, index) in treeData"
                :key="index"
                :value="item.sessionCode"
                :dataRef="item"
              >
                {{ item.name }}
              </a-select-option>
            </a-select>
          </a-space>
        </div>
        <div style="margin-right: 16px">
          <span>界别：</span>
          <a-space>
            <zh-dict
              v-model="jbCodeList"
              style="width: 140px"
              codeType="tb02"
            />
          </a-space>
        </div>
        <div style="margin-right: 16px">
          <span>关键字：</span>
          <a-input
            v-model="keyword"
            style="width: 120px"
            placeholder="请输入"
          />
        </div>
        <div style="margin-right: 16px">
          <span>绑定：</span>
          <a-space>
            <a-select
              allowClear
              v-model="binding"
              style="width: 100px"
              placeholder="请选择"
            >
              <a-select-option :value="1"> 是 </a-select-option>
              <a-select-option :value="0"> 否 </a-select-option>
            </a-select>
          </a-space>
        </div>
        <div style="margin-right: 16px">
          <span>党派：</span>
          <a-space>
            <zh-dict
              :parentDisabled="true"
              v-model="politicalCodeList"
              style="width: 140px"
              codeType="gb4762"
            />
          </a-space>
        </div>
        <div style="margin-right: 16px">
          <span>是否常委：</span>
          <a-space>
            <a-select
              allowClear
              v-model="isOftenMem"
              style="width: 100px"
              placeholder="请选择"
            >
              <a-select-option value="1"> 是 </a-select-option>
              <a-select-option value="0"> 否 </a-select-option>
            </a-select>
          </a-space>
        </div>
        <div style="margin-right: 16px">
          <span>是否查询往届：</span>
          <a-space>
            <a-select
              allowClear
              v-model="otherSession"
              style="width: 100px"
              placeholder="请选择"
            >
              <a-select-option value="1"> 是 </a-select-option>
              <a-select-option value="0"> 否 </a-select-option>
            </a-select>
          </a-space>
        </div>
        <a-button type="primary" @click="() => getList()">查询</a-button>
        <a-button
          type="primary"
          @click="
            () => {
              jbCodeList = undefined;
              politicalCodeList = undefined;
              isOftenMem = undefined;
              binding = undefined;
              keyword = undefined;
              otherSession = undefined;
              area = undefined;
              getList();
            }
          "
          >重置</a-button
        >

        <a-button type="primary" @click="addOpen">新增</a-button>
      </a-space>
    </div>
    <z-select-user
      :key="key"
      style="display: none"
      :limit="1"
      @change="change"
      ref="userRef"
      :topUnitId="unitId"
    />

    <a-table
      :dataSource="dataSource"
      :columns="columns"
      :pagination="pagination"
      @change="
        (obj) => {
          getList(obj.current, obj.pageSize);
        }
      "
    >
      <span slot="name" slot-scope="text, record">
        <a @click="addOpen(record, true)">{{ record["name"] }}</a>
      </span>
      <span slot="action" slot-scope="text, record">
        <a v-if="record.isBind == 0" @click="bind(record)">绑定</a>
        <!-- <a-popconfirm  title="是否确认解除绑定？" @confirm="ubind(record)">
       
        </a-popconfirm> -->
        <a v-else style="color: red" @click="ubind(record)">解绑</a>
        <a-divider type="vertical" />
        <a @click="addOpen(record, true)">详情</a>
        <a-divider type="vertical" />
        <a @click="addOpen(record)">修改</a>
        <a-divider type="vertical" />
        <a-popconfirm title="是否确认删除？" @confirm="del(record)">
          <a style="color: red">删除</a>
        </a-popconfirm>
      </span>
    </a-table>
    <AddEdit
      ref="addEditRef"
      :pId="pId"
      :session="session"
      @callBack="getList"
    />

    <a-modal
      :visible="visible"
      :destroyOnClose="true"
      title="解绑人员信息"
      width="800px"
      @cancel="cancel"
      :bodyStyle="{ overflow: 'auto' }"
    >
      <div slot="footer">
        <a-button @click="cancel"> 取消 </a-button>
        <a-popconfirm title="是否确认解除绑定？" @confirm="ubindsure">
          <a-button type="primary"> 确认 </a-button>
        </a-popconfirm>
      </div>
      <div>
        <a-form :form="form" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-row>
            <a-col :span="12">
              <a-form-item label="姓名">
                <a-input
                  v-decorator="[
                    'memName',
                    {
                      rules: [{ required: false, message: '请输入' }],
                    },
                  ]"
                  placeholder="请输入"
                />
              </a-form-item>
            </a-col>
          </a-row>

          <a-row>
            <a-col :span="12">
              <a-form-item label="用户名">
                <a-input
                  v-decorator="[
                    'userName',
                    {
                      rules: [{ required: false, message: '请输入' }],
                    },
                  ]"
                  placeholder="请输入"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="账号">
                <a-input
                  v-decorator="[
                    'userAccount',
                    {
                      rules: [{ required: false, message: '请输入' }],
                    },
                  ]"
                  placeholder="请输入"
                />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </a-modal>
  </div>
</template>

<script>
import { auth as http } from "@zh/common-utils/http";
import AddEdit from "./components/addEdit.vue";

export default {
  components: {
    AddEdit,
  },
  data() {
    return {
      dataSource: [],
      columns: [
        {
          title: "姓名",
          width: 100,
          scopedSlots: { customRender: "name" },
        },
        // {
        //   title: "用户名",
        //   dataIndex: "age",
        //   key: "age",
        // },
        {
          title: "委员编号",
          dataIndex: "memNum",
          key: "memNum",
          width: 100,
          ellipsis: { showTitle: true },
        },
        {
          title: "工作单位职务",
          dataIndex: "workAndPost",
          key: "workAndPost",
          ellipsis: { showTitle: true },
        },
        {
          title: "性别",
          dataIndex: "sexName",
          key: "sexName",
          width: 100,
        },
        {
          title: "出生年月",
          dataIndex: "birthdate",
          key: "birthdate",
          width: 100,
        },
        {
          title: "民族",
          dataIndex: "nationName",
          key: "nationName",
          width: 100,
        },
        {
          title: "籍贯",
          dataIndex: "nativePlace",
          key: "nativePlace",
          width: 100,
        },
        {
          title: "学历",
          dataIndex: "fullEducationName",
          key: "fullEducationName",
          width: 100,
        },
        {
          title: "界别",
          dataIndex: "jbName",
          key: "jbName",
          width: 100,
        },
        {
          title: "党派",
          dataIndex: "politicalName",
          key: "politicalName",
          width: 100,
        },
        // {
        //   title: "专委会",
        //   dataIndex: "specialCode",
        //   key: "specialCode",
        //   width: 100,
        // },
        {
          title: "操作",
          width: 200,
          scopedSlots: { customRender: "action" },
        },
      ],
      key: new Date().valueOf(),
      session: undefined,
      keyword: undefined,
      pageNum: 1,
      pageSize: 10,
      pId: undefined,
      isOftenMem: undefined,
      binding: undefined,
      keyword: undefined,
      otherSession: undefined,
      jbCodeList: undefined,
      politicalCodeList: undefined,
      bindObj: {},
      isBind: false,
      pagination: {},
      area: undefined,
      areaName: undefined,
      visible: false,
      labelCol: { span: 6 },
      wrapperCol: { span: 14 },
      form: this.$form.createForm(this, { name: "c_mem" }),
      unbindObj: {},
      wytype: "",
      unitId: '',
    };
  },
  mounted() {
    this.getQx();
    // this.getList();
  },
  activated() {
    this.getList();
  },
  methods: {
    getList(pageNum = 1, pageSize = 10) {
      this.areaName =
        this.treeData.find((item) => item.sessionCode == this.area)
          .sessionName || "";
      this.pId = undefined;
      http.get("/cppccSession/getCurrTerm").then((res) => {
        this.session = res;
        if (!this.area) {
          this.area = res["id"];
        }
        http
          .post("/mem/list", {
            sessionCode: this.area,
            pageNum: pageNum || this.pageNum,
            pageSize: pageSize || this.pageSize,
            isOftenMem: this.isOftenMem,
            binding: this.binding,
            keyword: this.keyword,
            otherSession: this.otherSession,
            politicalCodeList: this.politicalCodeList,
            jbCodeList: this.jbCodeList,
            type: this.otherSession == "1" ? 2 : 1,
          })
          .then((res) => {
            const { currPage, pageSize, list, totalCount } = res;
            this.dataSource = list;
            this.pageNum = currPage;
            this.pageSize = pageSize;
            this.pagination = {
              current: currPage,
              total: totalCount,
            };
          });
      });
    },
    getQx() {
      http.get("/cppccSession/getUnitInfo").then((res) => {
        this.treeData = res.filter((item) => item.name != "区县");
        this.unitId = res[0]["unitId"];
        this.area = res[0]["sessionCode"];
        this.areaName = res[0]["sessionName"];
        if (res[0]["name"] == "全国") {
          this.wytype = '1';
        } else if (res[0]["name"] == "重庆市") {
          this.wytype = '2';
        } else {
          this.wytype = '999';
        }
        this.getList();
      });
    },
    del(record) {
      http.get(`/mem/delete?id=${record["id"]}`).then((res) => {
        this.getList();
      });
    },
    addOpen(record, isDetail = false) {
      this.$refs.addEditRef.open({ ...record, wytype: this.wytype,sessionCode: this.area }, isDetail);
    },
    bind(record) {
      this.bindObj = record;
      this.$refs.userRef.openDialog();
    },
    ubind(record) {
      this.visible = true;
      this.unbindObj = record;
      http
        .post("/memUser/findBind", {
          memId: record["id"],
        })
        .then((res) => {
          this.form.setFieldsValue({
            ...res,
          });
        });
    },
    ubindsure() {
      http
        .post("/memUser/unbind", {
          sessionCode: this.session.id,
          userId: this.unbindObj.bindUserId,
          memId: this.unbindObj.id,
        })
        .then((res) => {
          this.cancel();
          this.getList();
        });
    },
    cancel() {
      this.visible = false;
      this.unbindObj = {};
    },
    change(val) {
      console.log("🚀 ~ file: index.vue:208 ~ change ~ val:", val);
      if (val.length > 0) {
        http
          .post("/memUser/bind", {
            sessionCode: this.session.id,
            userId: val[0],
            memId: this.bindObj.id,
          })
          .then((res) => {
            this.getList();
            this.key = new Date().valueOf();
          });
      }
    },
    qhChange(val) {
      console.log("val===", val);
      let find = this.treeData.find((item) => item.sessionCode == val);
      this.unitId = find["unitId"];
      console.log("find===", find);
      if (find["name"] == "全国") {
        this.wytype = '1';
      } else if (find["name"] == "重庆市") {
        this.wytype = '2';
      } else {
        this.wytype = '999';
      }
      this.jbCodeList = undefined;
      this.keyword = undefined;
      this.binding = undefined;
      this.politicalCodeList = undefined;
      this.isOftenMem = undefined;
      this.otherSession = undefined;
      this.$nextTick(()=>{
        this.getList();
      })
    },
  },
};
</script>

<style lang="less" scoped>
.sear {
  margin-bottom: 18px;
}
</style>
