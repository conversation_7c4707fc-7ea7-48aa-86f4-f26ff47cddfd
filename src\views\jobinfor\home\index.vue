<template>
  <div class="cp-page page">
    <div style="width: 50%">
      <div class="tt">
        政协工作信息
        <div class="sel">
          <a-select allowClear v-model="hotCountType" @change="getList()" style="width: 120px" placeholder="请选择">
            <a-select-option value="1"> 阅读次数 </a-select-option>
            <a-select-option value="2"> 下载次数 </a-select-option>
          </a-select>
        </div>
      </div>
      <!-- <a-table
        size="small"
        :dataSource="dataSource"
        :columns="columns"
        :customRow="
          (record) => {
            return {
              on: {
                click: () => {
                  clickR(record);
                },
              },
            };
          }
        "
      >
        <span slot="createTime" slot-scope="text">
          {{ text ? dayjs(text).format("YYYY-MM-DD") : text }}
        </span>
      </a-table> -->
      <div style="height: 200px; overflow: auto">
        <table class="table">
          <colgroup width="70"></colgroup>
          <colgroup width="260"></colgroup>
          <colgroup width="135"></colgroup>
          <colgroup width="115"></colgroup>
          <colgroup width="190"></colgroup>
          <!-- <colgroup width="400"></colgroup>
        <colgroup width="400"></colgroup> -->
          <tbody>
            <tr v-for="(item, index) in dataSource" :key="index" @click="clickR(item)">
              <td>{{ index + 1 }}</td>
              <td :title="item.oaTitle" style="text-align: left">
                <div class="ov-title" style="width: 260px">{{ item["oaTitle"] }}</div>
              </td>
              <td>{{ item["oaYear"] ? `${item["oaYear"]}年` : "" }}{{ item["oaIssue"] ? `第${item["oaIssue"]}期` : "" }}</td>
              <td>{{ item["reportDate"] ? dayjs(item["reportDate"]).format("YYYY-MM-DD") : undefined }}</td>
              <td>{{ item["showUnitName"] }}</td>
            </tr>
          </tbody>
        </table>
      </div>
      <div class="tt">政协工作信息采用情况</div>
      <div class="zc">
        <div>
          <v-echarts key="updateKeysmall" style="padding: 0; height: 180px; width: 200px" :Option="getOptionZC()" @click="mapClick" />
        </div>
        <v-echarts key="updateKey" style="padding: 0; height: 440px" :Option="getOption()" @click="mapClick" />
      </div>
      <a-modal
        :visible="visible"
        :destroyOnClose="true"
        :title="`${mapObj.name}工作信息`"
        width="1200px"
        @ok="handleOk"
        @cancel="() => cancel()"
        :bodyStyle="{ overflow: 'auto' }"
        :footer="false"
      >
        <a-table
          :dataSource="dataSource2"
          :columns="columns2"
          :customRow="
            (record) => {
              return {
                on: {
                  click: () => {
                    visible = false;
                    clickR(record);
                  },
                },
              };
            }
          "
        >
          <span slot="num" slot-scope="text, record, index">
            {{ (pagination.current - 1) * 10 + (index + 1) }}
          </span>
          <span slot="oaYear" slot-scope="text, record"> {{ text ? text + "年" : "" }}{{ record["oaIssue"] ? `第${record["oaIssue"]}期` : "" }} </span>
          <span slot="createTime" slot-scope="text">
            {{ text ? dayjs(text).format("YYYY-MM-DD") : text }}
          </span>
        </a-table>
      </a-modal>
    </div>

    <div style="width: 60%">
      <iframe :src="ifSrc" style="position: relative; width: 100%; height: 76vh; border: none" />
    </div>
  </div>
</template>

<script>
import { auth as http } from "@zh/common-utils/http";
import dayjs from "dayjs";
import * as echarts from "echarts";
import cq from "@/assets/json/cqmap.json";
import cqzc from "@/assets/json/cqzc.json";
export default {
  components: {},
  data() {
    const user = this.$store.getLoginUser();
    console.log("🚀 ~ file: index.vue:115 ~ data ~ user:", user);
    return {
      user,
      dayjs,
      dataSource: [],
      dataSource2: [],
      columns: [
        {
          title: "发布标题",
          dataIndex: "title",
          key: "title",
        },
        {
          title: "年号",
          dataIndex: "year",
          key: "year",
        },
        {
          title: "期号",
          dataIndex: "issue",
          key: "issue",
        },
        {
          title: "发布时间",
          dataIndex: "createTime",
          key: "createTime",
          width: 120,
          scopedSlots: { customRender: "createTime" },
        },
      ],
      columns2: [
        {
          title: "序号",
          dataIndex: "num",
          scopedSlots: { customRender: "num" },
        },
        {
          title: "发布标题",
          dataIndex: "oaTitle",
        },
        {
          title: "期号",
          dataIndex: "oaYear",
          scopedSlots: { customRender: "oaYear" },
        },
        {
          title: "发布时间",
          dataIndex: "createTime",
          key: "createTime",
          width: 120,
          scopedSlots: { customRender: "createTime" },
        },
      ],
      visible: false,
      ifSrc: undefined,
      pageNum: 1,
      pageSize: 5,
      labelCol: { span: 3 },
      wrapperCol: { span: 20 },
      hotCountType: undefined,
      mapObj: {},
      pagination: {
        current: 1,
      },
      areaData: [],
    };
  },
  activated() {
    this.getList();
  },
  methods: {
    clickR(record) {
      if (record) {
        console.log("🚀 ~ file: index.vue:186 ~ clickR ~ record:", record);
        this.ifSrc =
          process.env.VUE_APP_READ +
          `?file=` +
          process.env.VUE_APP_READ_BASE_URL +
          `/sk/file/getOaJson?fileId=${record["fileId"] || record["id"]}` +
          `%26serviceUrl=${process.env.VUE_APP_READ_BASE_URL}`;
      }
    },
    getList(pageNum = 1, pageSize = 5) {
      let temp = { ...this.search };
      Object.keys(temp).forEach((key) => {
        if (key.includes("Date") && temp[key]) {
          temp[key] = dayjs(temp[key]).valueOf();
        }
      });
      http
        .post("/workReport/homeHotCount", {
          ...temp,
          pageType: 3,
          hotCountNum: 5,
          oaYear: 2023,
          hotCountType: this.hotCountType,
          pageNum: pageNum,
          pageSize: pageSize,
        })
        .then((res) => {
          if (res.length > 0) {
            this.clickR(res[0]);
          }
          this.dataSource = res;
        });
      http
        .post("/workReport/homeAreaCount", {
          pageNum: pageNum,
          pageSize: 9999,
          userId: this.user.unitUserId,
        })
        .then((res) => {
          this.areaData = res;
        });
    },
    getOption() {
      echarts.registerMap("china", cq); //注册可用的地图
      // console.log("🚀 ~ file: index.vue:185 ~ getOption ~ cq:", cq);
      let top = 200,
        left = -50,
        zoom = 5;
      let center = [106.5049, 29]; // 地图中心位置， 此处的纬度与下面的center相差1度是形成阴影的距离，可自己随意调整
      let map = "china",
        aspectScale = 1.1;
      return {
        tooltip: {
          formatter: (params, ticket, callback) => {
            let { name, value } = params;
            return name;
          },
        },
        series: [
          {
            name: "重庆",
            type: "map", //配置显示方式为用户自定义
            map,
            zoom, // 地图比例
            zlevel: 100,
            center, // 地图中心位置， 此处的纬度与下面的center相差1度是形成阴影的距离，可自己随意调整
            aspectScale,
            itemStyle: {
              color: "#2EADD9",
              areaColor: "#2EADD9",
              borderWidth: 1, //设置外层边框
              // borderColor: 'rgba(255,255,255,0.6)',
              borderColor: "white",
            },
            label: {
              show: true,
            },
            select: {
              disabled: true,
            },
            // emphasis: {
            //   itemStyle: {
            //     areaColor: "red",
            //   },
            // },
            // select: {
            //   //这个就是鼠标点击后，地图想要展示的配置
            //   disabled: false, //可以被选中
            //   itemStyle: {
            //     //相关配置项很多，可以参考echarts官网
            //     areaColor: "red", //选中
            //   },
            // },
            top,
            left,
            data: cq.features.map((item) => {
              let itemStyle = {
                color: "#2EADD9",
                areaColor: "#2EADD9",
              };
              let name = item.properties.name;
              let show = true;
              if (["渝中区", "大渡口区", "江北区", "沙坪坝区", "九龙坡区", "南岸区", "北碚区", "渝北区", "巴南区", "两江新区", "高新区"].includes(name)) {
                show = false;
              }
              let find = this.areaData.find((obj) => obj["unitLevelCode"] == item.properties.id);
              if (find) {
                itemStyle = {
                  color: "#2EADD9",
                  areaColor: "#0075FF",
                };
              }
              return {
                name: item.properties.name,
                id: (item.properties && item.properties.id) || item.id,
                label: {
                  show,
                  color: "white",
                },
                itemStyle,
              };
            }),
          },
        ],
      };
    },
    getOptionZC() {
      echarts.registerMap("cqzc", cqzc); //注册可用的地图
      console.log("🚀 ~ file: index.vue:185 ~ getOption ~ cqzc:", cqzc);
      let top = 125,
        left = 20,
        zoom = 1.2;
      let center = [106.5049, 29]; // 地图中心位置， 此处的纬度与下面的center相差1度是形成阴影的距离，可自己随意调整
      let map = "cqzc",
        aspectScale = 1.1;
      return {
        tooltip: {
          formatter: (params, ticket, callback) => {
            let { name, value } = params;
            return name;
          },
        },
        series: [
          {
            name: "重庆",
            type: "map", //配置显示方式为用户自定义
            map,
            zoom, // 地图比例
            zlevel: 100,
            center, // 地图中心位置， 此处的纬度与下面的center相差1度是形成阴影的距离，可自己随意调整
            aspectScale,
            itemStyle: {
              color: "#2EADD9",
              areaColor: "#2EADD9",
              borderWidth: 1, //设置外层边框
              // borderColor: 'rgba(255,255,255,0.6)',
              borderColor: "white",
            },
            label: {
              show: true,
            },
            select: {
              disabled: true,
            },
            top,
            left,
            data: cqzc.features.map((item) => {
              let name = item.properties.name;
              let offset = [0, 0];
              if (["沙坪坝区"].includes(name)) {
                offset = [-10, 0];
              }
              if (["九龙坡区"].includes(name)) {
                offset = [-10, -10];
              }
              if (["大渡口区"].includes(name)) {
                offset = [0, 5];
              }
              if (["江北区"].includes(name)) {
                offset = [10, 0];
              }
              let itemStyle = {
                color: "#2EADD9",
                areaColor: "#2EADD9",
              };
              let find = this.areaData.find((obj) => obj["unitLevelCode"] == item.properties.id);
              if (find) {
                itemStyle = {
                  color: "#2EADD9",
                  areaColor: "#0075FF",
                };
              }
              return {
                name: item.properties.name,
                id: (item.properties && item.properties.id) || item.id,
                itemStyle,
                label: {
                  offset, //是否对文字进行偏移。默认不偏移。例如：[30, 40] 表示文字在横向上偏移 30，纵向上偏移 40。
                  show: true,
                  color: "white",
                },
              };
            }),
          },
        ],
      };
    },
    mapClick(param) {
      console.log("🚀 ~ file: index.vue:173 ~ mapClick ~ param:", param);
      this.mapObj = param["data"];
      if (this.mapObj["id"]) {
        this.visible = true;
        this.getMapList();
      }
    },
    getMapList(page = 1, pageSize = 10) {
      // if (this.mapObj["id"]) {
      http
        .post("/workReport/homeAreaCountPage", {
          // currUnitLevelCode: "001200030009",
          currUnitLevelCode: this.mapObj["id"],
          pageNum: page,
          pageSize: pageSize,
        })
        .then((res) => {
          const { current, records, total } = res;
          this.dataSource2 = records;
          this.pagination = {
            current: current,
            total: total,
          };
        });
      // }
    },
    cancel() {
      this.visible = false;
    },
    handleOk() {
      this.cancel();
    },
    addOpen(record) {
      this.$refs.addEditRef.open(record["id"]);
    },
  },
};
</script>

<style scoped lang="less">
.sear {
  & > div {
    display: inline-block;
    margin-right: 2%;
    margin-bottom: 18px;
    white-space: nowrap;
  }
}
.page {
  padding: 20px;
  display: flex;
  width: 100%;
  & > div:first-child {
    margin-right: 20px;
  }
}
.tt {
  background: #168bd5;
  text-align: center;
  font-size: 20px;
  font-weight: bold;
  color: white;
  padding: 6px;
  border-radius: 4px;
  position: relative;
}
.zc {
  position: relative;
  & > div:first-child {
    position: absolute;
    left: 60px;
    top: 0;
    z-index: 99;
  }
}
.table {
  margin: auto;
  table {
    table-layout: fixed;
    height: 500px;
  }
  tr {
    cursor: pointer;
  }
  td {
    // border: 1px solid #b7b7b7;
    text-align: center;
    height: 40px;
    font-size: 16px;
  }
  thead > tr > td {
    height: 60px;
  }
}
.sel {
  position: absolute;
  right: 10px;
  top: 4px;
  display: inline-block;
}
.ov-title {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
