
import { store, globalComponent } from '@zh/base-ant'
import Header from "@/views/components/header";
import themes from '@zh/base-ant/framework/themes'
import UserInfoHeader from "../layouts/UserInfoHeader.vue";
const install = (Vue) => {
  globalComponent.setHeaderToolbar(Header);
  globalComponent.setUserInfoHeader(UserInfoHeader);
  //个性化设置
  Vue.use(themes, { page: 'multi', navTheme: 'light', })
  // let name = '统一认证中心'
  let name = ''
  if (process.env.NODE_ENV == 'mem') {
    name = '政协委员信息'
  }
  if (process.env.NODE_ENV == 'work') {
    name = '政协工作信息'
  }
  if (process.env.NODE_ENV == 'send') {
    // name = '政协送阅'
  }
  if (install.installed) {
    return
  }

  /**
 * ##################################
 * 全局配置
 * ##################################
 */
  store.setAppConfig({
    // 应用名称，改名称会在浏览器标签显示
    _appName: name,
    // 客户端类型, 1-管理端 | 2-用户端 | 3-移动端
    _clientType: '1',
    // 左侧菜单头部标题(默认 _appName)
    _menuHeadName: '重庆数字政协',
    // 左侧菜单头部图标（需用require加载logo）
    _menuHeadLogo: require('@/assets/logo.png'),
    // 隐藏用户工具栏图标
    _hideUserHeadBar: true,
    _syncOff: 1
    // 验证码
    // _loginCaptcha: true,
    // 加密传输工具类
    // _encryptor: encryptor.setSecretKey('Aa123456!654321!')
  })
}

export default {
  install
}