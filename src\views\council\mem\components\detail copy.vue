<template>
  <div class="page">
    <div>
      <table>
        <div>
          <h3 class="tit" style="font-size: 18px; font-weight: bold">
            基本信息
          </h3>
        </div>
        <tr>
          <th>姓名</th>
          <td>{{ formState["name"] }}</td>
          <th>用户名</th>
          <td>{{ formState["userName"] }}</td>
          <th>性别</th>
          <td>{{ formState["sexCode"] == "1" ? "男" : "女" }}</td>
        </tr>

        <tr>
          <th>出生日期</th>
          <td>{{ formState["birthdate"] }}</td>
          <th>流水号</th>
          <td>{{ formState["serialNum"] }}</td>
          <th>身份证号码</th>
          <td>{{ formState["idCard"] }}</td>
        </tr>

        <tr>
          <th>委员编号</th>
          <td>{{ formState["memNum"] }}</td>
          <th>民族</th>
          <td>{{ formState["nationName"] }}</td>
          <th>籍贯</th>
          <td>{{ formState["nativePlace"] }}</td>
        </tr>

        <tr>
          <th>界别</th>
          <td>{{ formState["jbName"] }}</td>
          <th>党派</th>
          <td>{{ formState["politicalName"] }}</td>
          <th>第二党派</th>
          <td>{{ formState["politicalTwoName"] }}</td>
        </tr>

        <tr>
          <th>全日制学历</th>
          <td>{{ formState["fullEducationName"] }}</td>
          <th>全日制学位</th>
          <td>{{ formState["fullEducationDegreeCode"] }}</td>
          <th>全日制专业</th>
          <td>{{ formState["fullEducationSpecialty"] }}</td>
        </tr>

        <tr>
          <th>推荐单位</th>
          <td>{{ formState["recommendUnitName"] }}</td>
          <th>提名单位</th>
          <td>{{ formState["nominateUnitName"] }}</td>
          <th>加入时间</th>
          <td>{{ formState["unitJoinDate"] }}</td>
        </tr>
        <tr>
          <th>委员类型</th>
          <td>{{ formState["oldMemType"] }}</td>
          <th>特约监督员</th>
          <td>{{ formState["oldSupervisionUnitCode"] }}</td>
          <th>委员进社区</th>
          <td>{{ formState["oldCommunityUnitCode"] }}</td>
        </tr>
        <tr>
          <th>单位及职务</th>
          <td colspan="3">{{ formState["workAndPost"] }}</td>
          <th>单位电话</th>
          <td>{{ formState["workUnitPhone"] }}</td>
        </tr>
        <tr>
          <th>是否违纪违法</th>
          <td colspan="5">{{ formState["oldIllegalRecord"] }}</td>
        </tr>

        <div>
          <h3 class="tit" style="font-size: 18px; font-weight: bold">
            届内情况
          </h3>
        </div>

        <tr>
          <th>入选时间</th>
          <td>{{ formState["memJoinDate"] }}</td>
          <th>离任时间</th>
          <td>{{ formState["memLeaveDate"] }}</td>
          <th>所属组别</th>
          <td>{{ formState["memGroupPartCode"] }}</td>
        </tr>

        <tr>
          <th>是否连任</th>
          <td>{{ formState["isTermLimits"] == "1" ? "是" : "否" }}</td>
          <th>常委会组成人员</th>
          <td>{{ formState["isOftenMem"] == "1" ? "是" : "否" }}</td>
          <th>全国政协委员</th>
          <td>{{ formState["isNationwideMem"] == "1" ? "是" : "否" }}</td>
        </tr>

        <tr>
          <th>委员编组</th>
          <td>{{ formState["memGroupBzCode"] }}</td>
          <th>召集人</th>
          <td>{{ formState["isConveneMem"] == "1" ? "是" : "否" }}</td>
          <th>企业家联谊会</th>
          <td>{{ formState["isEntrepreneurMem"] == "1" ? "是" : "否" }}</td>
        </tr>
        <div>
          <h3 class="tit" style="font-size: 18px; font-weight: bold">
            工作情况
          </h3>
        </div>
        <tr>
          <th>所属单位</th>
          <td>{{ formState["affiliatedUnitCity"] }}</td>
          <th>所属单位详情</th>
          <td>{{ formState["affiliatedUnitDetail"] }}</td>
          <th>技术职称</th>
          <td>{{ formState["technologyName"] }}</td>
        </tr>
        <tr>
          <th>参加工作时间</th>
          <td>{{ formState["workDate"] }}</td>
          <th>专业职务</th>
          <td>{{ formState["technologyPost"] }}</td>
          <th>职务级别</th>
          <td>{{ formState["oldGwyPostName"] }}</td>
        </tr>
        <div>
          <h3 class="tit" style="font-size: 18px; font-weight: bold">
            联系信息
          </h3>
        </div>
        <tr>
          <th>单位通讯地址</th>
          <td>{{ formState["workAddress"] }}</td>
          <th>邮政编码(办公)</th>
          <td>{{ formState["workPostcode"] }}</td>
          <th>工作电话</th>
          <td>{{ formState["workPhoneNum"] }}</td>
        </tr>
        <tr>
          <th>家庭住址</th>
          <td>{{ formState["homeAddress"] }}</td>
          <th>邮政编码(住宅)</th>
          <td>{{ formState["homePostcode"] }}</td>
          <th>住宅电话</th>
          <td>{{ formState["homePhoneNum"] }}</td>
        </tr>
        <tr>
          <th>电子邮箱</th>
          <td>{{ formState["email"] }}</td>
          <th>传真</th>
          <td>{{ formState["faxNum"] }}</td>
          <th>手机号码</th>
          <td>{{ formState["phoneNum"] }}</td>
        </tr>
        <tr>
          <th>是否邮寄</th>
          <td colspan="5">{{ formState["isMail"] == "1" ? "是" : "否" }}</td>
        </tr>

        <div>
          <h3 class="tit" style="font-size: 18px; font-weight: bold">
            扩展信息
          </h3>
        </div>
        <tr>
          <th>继任委员证号</th>
          <td>{{ formState["lastMemNum"] }}</td>
          <th>出生地</th>
          <td>{{ formState["birthplace"] }}</td>
          <th>健康状况</th>
          <td>{{ formState["healthName"] }}</td>
        </tr>
        <tr>
          <th>有何专长</th>
          <td>{{ formState["speciality"] }}</td>
          <th>入党时间</th>
          <td>{{ formState["politicalDate"] }}</td>
          <th>职称</th>
          <td>{{ formState["rankName"] }}</td>
        </tr>
        <tr>
          <th>第二党派入党时间</th>
          <td>{{ formState["politicalTwoDate"] }}</td>
          <th>委员职务</th>
          <td>{{ formState["specialRegionalGroupPost"] }}</td>
          <th>委员任职时间</th>
          <td>{{ formState["committeePostDate"] }}</td>
        </tr>
        <tr>
          <th>委员任免记录</th>
          <td colspan="5">{{ formState["removalRecord"] }}</td>
        </tr>
        <tr>
          <th>常委组成人员</th>
          <td>{{ formState["oftenMemPost"] }}</td>
          <th>常委任职时间</th>
          <td>{{ formState["oftenMemPostDate"] }}</td>
          <th>常委离任时间</th>
          <td>{{ formState["oftenMemPostOutDate"] }}</td>
        </tr>
        <tr>
          <th>常委任免记录</th>
          <td colspan="5">{{ formState["oftenMemRemovalRecord"] }}</td>
        </tr>

        <tr>
          <th>专委会名称</th>
          <td>{{ formState["specialName"] }}</td>
          <th>专委会职务</th>
          <td>{{ formState["specialPostName"] }}</td>
          <th>专委会任职时间</th>
          <td>{{ formState["specialPostDate"] }}</td>
        </tr>

        <tr>
          <th>专委会离任时间</th>
          <td>{{ formState["specialPostOutDate"] }}</td>
          <th>委员工作站</th>
          <td>{{ formState["specialRegionalGroupName"] }}</td>
          <th>委员工作站职务</th>
          <td>{{ formState["specialRegionalGroupPost"] }}</td>
        </tr>

        <tr>
          <th>专委会任免记录</th>
          <td colspan="5">{{ formState["specialRemovalRecord"] }}</td>
        </tr>
        <tr>
          <th>委员工作站安排记录</th>
          <td colspan="5">{{ formState["specialRegionalGroupRecord"] }}</td>
        </tr>

        <tr>
          <th>全日制院校</th>
          <td>{{ formState["fullEducationSchool"] }}</td>
          <th>在职学历</th>
          <td>{{ formState["workEducationName"] }}</td>
          <th>在职学位</th>
          <td>{{ formState["workEducationDegreeCode"] }}</td>
        </tr>
        <tr>
          <th>在职院校</th>
          <td>{{ formState["workEducationSchool"] }}</td>
          <th>在职专业</th>
          <td>{{ formState["workEducationSpecialty"] }}</td>
          <th>兼任职务</th>
          <td>{{ formState["otherWorkPost"] }}</td>
        </tr>

        <tr>
          <th>移动电话</th>
          <td colspan="5">{{ formState["phone"] }}</td>
        </tr>

        <tr>
          <th>联系地址</th>
          <td colspan="5">{{ formState["address"] }}</td>
        </tr>
        <tr>
          <th>个人简历</th>
          <td colspan="5">{{ formState["resume"] }}</td>
        </tr>
        <tr>
          <th>主要表现</th>
          <td colspan="5">{{ formState["expression"] }}</td>
        </tr>
        <tr>
          <th>主要成就</th>
          <td colspan="5">{{ formState["achievement"] }}</td>
        </tr>
        <tr>
          <th>备注</th>
          <td colspan="5">{{ formState["remark"] }}</td>
        </tr>
      </table>
    </div>

    <!-- <div>照片</div> -->
    <img v-if="imageUrl" :src="imageUrl" alt="照片" class="img" />
  </div>
</template>

<script>
import { auth as http } from "@zh/common-utils/http";

export default {
  props: {
    formState: {
      type: Object,
    },
    imageUrl: {
      type: String,
    },
  },
  data() {
    return {
      formState: props.formState,
      form: this.$form.createForm(this, { name: "council_mem" }),
      visible: false,
      labelCol: { span: 12 },
      wrapperCol: { span: 12 },
      formState: {
        name: undefined,
        region: undefined,
        date1: undefined,
        delivery: undefined,
        type: undefined,
        resource: undefined,
        desc: undefined,
      },
    };
  },
  mounted() {
    this.getDict();
  },
  methods: {
    open() {
      this.visible = true;
    },
    getDict() {
      http.get("/dictCode/getByType?codeType=gb3304");
    },
    cancel() {
      this.visible = false;
    },
    handleOk() {
      this.form.validateFields((err) => {
        if (!err) {
          console.info("success");
        }
      });
      // this.cancel();
    },
  },
};
</script>

<style scoped lang="less">
@import url("./addEdit.less");

.tit {
  border-left: 6px solid #0da1ff;
  height: 21px;
  line-height: 21px;
  color: #333;
  font-size: 18px;
  padding-left: 9px;
  margin: 30px 0px 30px 10px;
  text-align: left;
}
.page{
  position: relative;
  .img{
    position: absolute;
    right: 30px;
    top: 80px;
    width: 110px;
    height: 120px;
  }
}

</style>
