<template>
  <div>
    <a-modal :visible="visible" :destroyOnClose="true" title="信息列表" width="1200px" @ok="handleOk" @cancel="() => cancel()" :bodyStyle="{ overflow: 'auto' }">
      <a-table
        :dataSource="dataSource"
        :columns="columns"
        :pagination="pagination"
        :row-selection="rowSelection"
        @change="
          (obj) => {
            getList(obj.current, obj.pageSize);
          }
        "
      >
        <a slot="name" slot-scope="text, record" @click="addOpen(record)">
          {{ record ? record["titel"] : "" }}
        </a>
        <span slot="reportDate" slot-scope="text">
          {{ text ? dayjs(text).format("YYYY-MM-DD") : text }}
        </span>
        <span slot="oaYear" slot-scope="text, record"> {{ text ? text + "年" : "" }}{{ record["oaIssue"] ? `第${record["oaIssue"]}期` : "" }} </span>
        <span slot="accpetDate" slot-scope="text">
          {{ text ? dayjs(text).format("YYYY-MM-DD") : text }}
        </span>
        <span slot="action" slot-scope="text, record">
          <a @click="addOpen(record)">详情</a>
        </span>
      </a-table>
    </a-modal>
  </div>
</template>

<script>
import { auth as http } from "@zh/common-utils/http";
import dayjs from "dayjs";

export default {
  components: {},
  props: {},
  data() {
    return {
      dayjs: dayjs,
      visible: false,
      dataSource: [{ id: "123456", name: 1231 }],
      columns: [
        {
          title: "报送标题",
          dataIndex: "titel",
          width: 400,
          scopedSlots: { customRender: "name" },
        },
        {
          title: "报送时间",
          dataIndex: "reportDate",
          key: "reportDate",
          scopedSlots: { customRender: "reportDate" },
        },
        {
          title: "报送单位",
          dataIndex: "reportUnitName",
          key: "reportUnitName",
        },
        {
          title: "采用情况",
          dataIndex: "oaYear",
          key: "oaYear",
          scopedSlots: { customRender: "oaYear" },
        },
        {
          title: "采编状态",
          dataIndex: "auditStatusName",
          key: "auditStatusName",
        },
        {
          title: "采用时间",
          dataIndex: "accpetDate",
          key: "accpetDate",
          scopedSlots: { customRender: "accpetDate" },
        },
      ],
      selectedRows: [],
      rowSelection: {
        onChange: (selectedRowKeys, selectedRows) => {
          console.log(`selectedRowKeys: ${selectedRowKeys}`, "selectedRows: ", selectedRows);
          this.selectedRows = selectedRows;
        },
        onSelect: (record, selected, selectedRows) => {
          console.log(record, selected, selectedRows);
        },
        onSelectAll: (selected, selectedRows, changeRows) => {
          console.log(selected, selectedRows, changeRows);
        },
      },
    };
  },
  methods: {
    open(id) {
      this.getList();
      this.visible = true;
    },
    getList(pageNum = 1, pageSize = 10) {
      let temp = { ...this.search };
      Object.keys(temp).forEach((key) => {
        if (key.includes("Time") && temp[key]) {
          temp[key] = dayjs(temp[key]).valueOf();
        }
      });
      http
        .post("/workReport/list", {
          ...temp,
          auditStatus: "4",
          excludeContact: true,
          pageNum: pageNum || this.pageNum,
          pageSize: pageSize || this.pageSize,
        })
        .then((res) => {
          const { currPage, pageSize, list, totalCount } = res;
          this.dataSource = list;
          this.pageNum = currPage;
          this.pageSize = pageSize;
          this.pagination = {
            current: currPage,
            total: totalCount,
          };
        });
    },
    cancel() {
      this.visible = false;
    },
    handleOk() {
      this.$emit("change", this.selectedRows);
      this.cancel();
    },
  },
};
</script>

<style scoped lang="less"></style>
