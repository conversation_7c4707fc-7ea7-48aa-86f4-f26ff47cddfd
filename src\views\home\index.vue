<template>
  <div class="home-container">
    <div class="home-bg"></div>
    <!-- 顶部导航栏 -->
    <div class="top-navbar">
      <div class="navbar-left">
        <span class="org-text">中国人民政治协商会议重庆市委员会主办</span>
        <span class="sub-text">（本平台为非涉密平台，严禁处理、传输国家秘密！）</span>
      </div>
      <div class="navbar-right">
        <!-- <a-icon type="bell" class="notification-icon" @click="handleNotificationClick" /> -->

        <!-- 用户信息区域 -->
        <div class="user-info-section">
          <a-dropdown placement="bottomRight">
            <div class="user-dropdown-trigger">
              <a-icon type="user" class="user-icon" />
              <span class="user-name">{{ getUserDisplayName() }}</span>
              <a-icon type="down" class="dropdown-arrow" />
            </div>
            <a-menu slot="overlay" @click="handleUserMenuClick">
              <!-- 用户信息展示 -->
              <!-- <div class="user-info-header">
                <div class="user-info-name">{{ getUserDisplayName() }}</div>
                <div class="user-info-dept" v-if="getUserDeptInfo()">{{ getUserDeptInfo() }}</div>
                <div class="user-info-unit" v-if="userInfo && userInfo.unitName">{{ userInfo.unitName }}</div>
              </div>
              <a-menu-divider /> -->

              <!-- <a-menu-item key="workspace">
                <a-icon type="desktop" />
                工作台
              </a-menu-item> -->
              <a-menu-item key="switch">
                <a-icon type="swap" />
                切换单位
              </a-menu-item>
              <a-menu-divider />
              <!-- <a-menu-item key="logout">
                <a-icon type="logout" />
                退出登录
              </a-menu-item> -->
            </a-menu>
          </a-dropdown>
        </div>
      </div>
    </div>

    <!-- 标题和徽标区域 -->
    <div class="header-section">
      <div class="logo-section">
        <img src="@/assets/home/<USER>" alt="政协徽标" class="cppcc-logo" />
      </div>
      <h1 class="main-title">重庆数字政协</h1>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 综合应用区域 -->
      <div class="app-section">
        <div class="section-header">
          <a-icon type="appstore" class="header-icon" />
          <span class="header-title">综合应用</span>
        </div>

        <!-- 功能模块网格 -->
        <div class="modules-grid" v-if="!loading">
          <div v-for="(module, index) in modules" :key="module.id" class="module-item" :class="getModuleBgClass(index)" @click="handleModuleClick(module)">
            <div v-if="module.appLogo" style="margin-bottom: 8px">
              <img :src="module.appLogo" width="60" />
            </div>
            <div v-else class="module-icon">
              <a-icon :type="module.icon" />
            </div>
            <div class="module-title">{{ module.title }}</div>
          </div>
        </div>

        <!-- 加载状态 -->
        <div v-if="loading" class="loading-container">
          <a-spin size="large" tip="加载应用模块中...">
            <div class="loading-placeholder"></div>
          </a-spin>
        </div>
      </div>
    </div>

    <!-- 切换单位模态框 -->
    <a-modal v-model="switchIdentityVisible" title="切换单位" :width="700" :footer="null" :maskClosable="false">
      <div class="identity-switch-content">
        <a-table :columns="identityColumns" :data-source="userDeptList" :pagination="false" :row-selection="rowSelection" row-key="deptId" size="middle" class="identity-table">
          <template slot="jobName" slot-scope="text, record">
            {{ record.jobName || "" }}
          </template>

          <template slot="deptName" slot-scope="text, record">
            {{ record.deptName }}
          </template>

          <template slot="isDefault" slot-scope="text, record">
            {{ record.isDefault ? "是" : "否" }}
          </template>
        </a-table>

        <div class="modal-footer">
          <a-button @click="cancelSwitch" class="cancel-btn">取消</a-button>
          <a-button type="primary" @click="confirmSwitch" class="confirm-btn">保存</a-button>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script>
import { auth as http } from "@zh/common-utils/http";
import storage from "@zh/common-utils/utils/storage";
import dayjs from "dayjs";
export default {
  name: "Home",
  data() {
    return {
      userInfo: null, // 用户信息
      switchIdentityVisible: false, // 身份切换模态框显示状态
      selectedDeptIndex: 0, // 选中的部门索引
      selectedRowKeys: [], // Table选中的行keys
      modules: [], // 应用模块列表，从接口获取
      loading: false, // 加载状态
      // Table列配置
      identityColumns: [
        {
          title: "职位名称",
          dataIndex: "jobName",
          key: "jobName",
          scopedSlots: { customRender: "jobName" },
          width: 200
        },
        {
          title: "部门名称",
          dataIndex: "deptName",
          key: "deptName",
          scopedSlots: { customRender: "deptName" }
        },
        {
          title: "是否主身份",
          dataIndex: "isDefault",
          key: "isDefault",
          scopedSlots: { customRender: "isDefault" },
          width: 120
        }
      ]
    };
  },
  computed: {
    // 用户部门列表
    userDeptList() {
      console.log(dayjs().valueOf(), "ttttttttttt");

      if (!this.userInfo || !this.userInfo.depts || !Array.isArray(this.userInfo.depts)) {
        return [];
      }
      return this.userInfo.depts;
    },

    // Table行选择配置
    rowSelection() {
      return {
        type: "radio",
        selectedRowKeys: this.selectedRowKeys,
        onChange: (selectedRowKeys, selectedRows) => {
          this.selectedRowKeys = selectedRowKeys;
          if (selectedRows.length > 0) {
            // 找到选中行在数组中的索引
            this.selectedDeptIndex = this.userDeptList.findIndex((dept) => dept.deptId === selectedRows[0].deptId);
          }
        },
        getCheckboxProps: (record) => ({
          name: record.deptId
        })
      };
    }
  },
  mounted() {
    this.getUserInfo();
    this.getModules();
  },
  methods: {
    handleModuleClick(module) {
      console.log("点击模块:", module.title, module);
      console.log("模块数据:", module);

      let url = null;
      let urlSource = "";

      // 根据 networkType 参数决定跳转路径
      if (module.networkType) {
        // networkType 有值时，使用 loginUrl 或者 redirectUrl
        url = module.loginUrl || module.redirectUrl;
        urlSource = module.loginUrl ? "loginUrl" : "redirectUrl";
        console.log(`networkType 有值，使用 ${urlSource}:`, url);
      } else {
        // networkType 没传时，取 accessRanges 里 isDefault: 1 那条
        if (module.accessRanges && Array.isArray(module.accessRanges)) {
          console.log("accessRanges:", module.accessRanges);
          const defaultRange = module.accessRanges.find(range => range.isDefault === 1 || range.isDefault === "1");
          if (defaultRange) {
            url = defaultRange.loginUrl || defaultRange.redirectUrl;
            urlSource = defaultRange.loginUrl ? "defaultRange.loginUrl" : "defaultRange.redirectUrl";
            console.log(`找到默认 accessRange，使用 ${urlSource}:`, url);
          } else {
            console.log("未找到默认的 accessRange");
          }
        } else {
          console.log("accessRanges 为空或不是数组");
        }

        // 如果没有找到默认的 accessRange，则使用原有逻辑
        if (!url) {
          url = module.ssoLoginUrl || module.targetUrl || module.path;
          urlSource = module.ssoLoginUrl ? "ssoLoginUrl" : (module.targetUrl ? "targetUrl" : "path");
          console.log(`使用原有逻辑，${urlSource}:`, url);
        }
      }

      // 如果最终没有获取到URL，使用原有逻辑作为兜底
      if (!url) {
        url = module.ssoLoginUrl || module.targetUrl || module.path;
        urlSource = module.ssoLoginUrl ? "ssoLoginUrl(兜底)" : (module.targetUrl ? "targetUrl(兜底)" : "path(兜底)");
        console.log(`兜底逻辑，使用 ${urlSource}:`, url);
      }

      console.log(`最终跳转URL (${urlSource}):`, url);

      if (url && url !== "#") {
        const finalUrl = url + `${url.includes("?") ? "&" : "?"}zwdd_id=${this.userInfo.zwddId}`;
        console.log("完整跳转URL:", finalUrl);
        window.location.href = finalUrl;
        // let token = JSON.parse(localStorage.getItem("access-token") || "{}").value;
        // window.location.href = `${url}${url.toString().includes("?") ? "&" : "?"}token=${token}`;
      } else {
        console.log("无有效URL，跳转取消");
      }
    },
    handleNotificationClick() {
      console.log("点击通知");
      // 这里可以添加通知相关逻辑
    },
    handleLoginClick() {
      console.log("点击登录");
      // 这里可以添加登录跳转逻辑
      // this.$router.push('/login')
    },

    // 获取用户信息
    async getUserInfo() {
      try {
        const response = await http.get("/sys/sso/getUserSimleInfo?checkToken=true");
        if (response) {
          this.userInfo = response;
          console.log("用户信息:", this.userInfo);
        }
      } catch (error) {
        console.error("获取用户信息异常:", error);
        this.userInfo = null;
      }
    },

    // 获取应用模块列表
    async getModules() {
      try {
        this.loading = true;
        const response = await http.post(`/task/TcWorktask/appListStatForClass/1?category=all&classCode=${process.env.VUE_APP_classCode}`);
        if (response && Array.isArray(response)) {
          // 处理新的分类数据结构
          const allApps = [];

          // 遍历每个分类
          response.forEach((category) => {
            if (category.list && Array.isArray(category.list)) {
              // 遍历分类下的应用列表
              category.list.forEach((app) => {
                allApps.push({
                  id: app.id || app.appCode || Math.random().toString(36).substr(2, 9),
                  title: app.name || "未知应用",
                  icon: this.getIconByAppCode(app.appCode) || "appstore",
                  path: app.targetUrl || "#",
                  appCode: app.appCode,
                  appLogo: app.logo ? process.env.VUE_APP_API_BASE_URL + `${app.logo}` : "",
                  logoColor: app.logoColor,
                  num: parseInt(app.num) || 0, // 待办任务数量
                  openMode: "1", // 默认新窗口打开
                  targetUrl: app.targetUrl,
                  mobileTargetUrl: app.mobileTargetUrl,
                  ssoLoginUrl: app.ssoLoginUrl,
                  mobileLoginUrl: app.mobileLoginUrl,
                  category: app.category,
                  categoryName: category.appClassificationName, // 分类名称
                  categoryIcon: category.icon, // 分类图标
                  categoryCode: category.code, // 分类代码
                  orderNo: app.orderno || 0,
                  // 新增字段支持
                  networkType: app.networkType,
                  loginUrl: app.loginUrl,
                  redirectUrl: app.redirectUrl,
                  accessRanges: app.accessRanges || []
                });
              });
            }
          });

          // 按排序序号排序
          this.modules = allApps.sort((a, b) => (a.orderNo || 0) - (b.orderNo || 0));

          console.log("应用模块列表:", this.modules);
          console.log("原始分类数据:", response);
        } else {
          console.error("获取应用模块失败:", response);
          // 如果接口失败，使用默认数据
          this.setDefaultModules();
        }
      } catch (error) {
        console.error("获取应用模块失败:", error);
        // 如果接口失败，使用默认数据
        this.setDefaultModules();
      } finally {
        this.loading = false;
      }
    },

    // 根据应用编码获取对应的图标
    getIconByAppCode(appCode) {
      const iconMap = {
        meeting: "calendar",
        proposal: "file-text",
        "social-info": "message",
        "member-duty": "user",
        panorama: "global",
        "wechat-study": "read",
        "smart-qa": "question-circle",
        "member-studio": "home",
        cooperation: "team",
        reading: "file",
        consultation: "solution",
        "smart-cppcc": "bulb"
      };
      return iconMap[appCode] || "appstore";
    },

    // 设置默认模块数据（作为备用）
    setDefaultModules() {
      this.modules = [];
    },

    // 根据索引获取模块背景类
    getModuleBgClass(index) {
      const bgClasses = ["module-bg-blue", "module-bg-purple", "module-bg-green"];
      return bgClasses[index % 3];
    },

    // 获取用户显示名称
    getUserDisplayName() {
      if (!this.userInfo) return "用户";

      // 优先显示用户名称，如果没有则显示账号
      return this.userInfo.userName || this.userInfo.account || "用户";
    },

    // 获取用户部门信息
    getUserDeptInfo() {
      if (!this.userInfo) return "";

      // 如果有多部门任职，显示默认部门
      if (this.userInfo.depts && this.userInfo.depts.length > 0) {
        const defaultDept = this.userInfo.depts.find((dept) => dept.isDefault);
        if (defaultDept) {
          return `${defaultDept.deptName} - ${defaultDept.jobName || ""}`;
        }
        // 如果没有默认部门，显示第一个部门
        const firstDept = this.userInfo.depts[0];
        return `${firstDept.deptName} - ${firstDept.jobName || ""}`;
      }

      // 显示当前部门信息
      const deptInfo = [];
      if (this.userInfo.deptName) deptInfo.push(this.userInfo.deptName);
      if (this.userInfo.jobName) deptInfo.push(this.userInfo.jobName);

      return deptInfo.join(" - ");
    },

    // 处理用户菜单点击
    handleUserMenuClick({ key }) {
      switch (key) {
        case "workspace":
          console.log("跳转到工作台");
          // this.$router.push('/workspace');
          break;
        case "switch":
          console.log("切换身份");
          this.showSwitchIdentityModal();
          break;
        case "logout":
          console.log("退出登录");
          this.handleLogout();
          break;
        default:
          break;
      }
    },

    // 退出登录
    handleLogout() {
      this.$confirm({
        title: "确认退出",
        content: "确定要退出登录吗？",
        onOk: () => {
          // 清除用户信息
          this.userInfo = null;
          // 清除token等登录信息
          // storage.removeToken();
          // 跳转到登录页
          // this.$router.push('/login');
          console.log("已退出登录");
        }
      });
    },

    // 显示切换身份模态框
    showSwitchIdentityModal() {
      if (!this.userInfo || !this.userInfo.depts || this.userInfo.depts.length <= 1) {
        console.log("当前用户只有一个身份，无需切换");
        this.message.info("当前用户只有一个身份，无需切换");
        return;
      }

      // 找到当前默认部门的索引
      const defaultIndex = this.userInfo.depts.findIndex((dept) => dept.isDefault);
      this.selectedDeptIndex = defaultIndex >= 0 ? defaultIndex : 0;

      // 设置Table选中状态
      if (this.userDeptList.length > 0 && this.selectedDeptIndex >= 0) {
        const selectedDept = this.userDeptList[this.selectedDeptIndex];
        this.selectedRowKeys = [selectedDept.deptId];
      }

      // 显示模态框
      this.switchIdentityVisible = true;
    },

    // 获取当前部门ID
    getCurrentDeptId() {
      if (!this.userInfo || !this.userInfo.depts) return null;

      const defaultDept = this.userInfo.depts.find((dept) => dept.isDefault);
      return defaultDept ? defaultDept.deptId : this.userInfo.depts[0] ? this.userInfo.depts[0].deptId : null;
    },

    // 取消切换
    cancelSwitch() {
      this.switchIdentityVisible = false;
    },

    // 确认切换
    async confirmSwitch() {
      if (this.selectedDeptIndex >= 0 && this.userDeptList[this.selectedDeptIndex]) {
        const selectedDept = this.userDeptList[this.selectedDeptIndex];
        console.log("切换到身份:", selectedDept);

        try {
          // 调用切换单位接口
          const response = await http.get(`/sys/sso/changeUnit/${selectedDept.unitUserId}`);

          if (response) {
            console.log(storage.getOwnerToken());

            // 设置新的token
            storage.setToken(response, 7200);

            this.message.success(`已切换到：${selectedDept.deptName} - ${selectedDept.jobName || ""}`);
            this.switchIdentityVisible = false;

            setTimeout(() => {
              window.location.reload();
            }, 500);
          } else {
            this.message.error("切换单位失败：未获取到有效token");
          }
        } catch (error) {
          console.error("切换单位失败:", error);
          this.message.error("切换单位失败：" + (error.message || "网络错误"));
        }
      }
    },

    // 切换身份
    async switchIdentity(deptId) {
      try {
        // 这里应该调用切换身份的API
        console.log("切换到部门ID:", deptId);
        this.message.success("身份切换成功");
        // 重新获取用户信息
        await this.getUserInfo();
      } catch (error) {
        console.error("切换身份失败:", error);
        this.message.error("切换身份失败");
      }
    }
  }
};
</script>

<style scoped lang="less">
.home-container {
  min-height: 100vh;
  background: white;
  margin: -10px;
  position: relative;
  z-index: 1;
}
.home-bg {
  z-index: -1;
  position: absolute;
  top: 0;
  left: 0;
  background: url("../../assets/home/<USER>") no-repeat;
  background-size: 100% 100%;
  width: calc(100%);
  height: 50vh;
}
.top-navbar {
  height: 60px;
  background: rgba(85, 157, 255, 0.3);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 35px;
}

.navbar-content {
  max-width: 1400px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
}

.navbar-left {
  display: flex;
  align-items: center;
  color: white;
  font-size: 20px;
}

.org-text {
  color: white;
  font-weight: 500;
}

.sub-text {
  color: red;
}

.divider {
  margin: 0 12px;
  color: rgba(255, 255, 255, 0.6);
}

.navbar-right {
  display: flex;
  align-items: center;
  color: white;
  font-size: 14px;
}

.notification-icon {
  font-size: 16px;
  margin-right: 16px;
  cursor: pointer;

  &:hover {
    color: rgba(255, 255, 255, 0.8);
  }
}

.login-text {
  cursor: pointer;

  &:hover {
    color: rgba(255, 255, 255, 0.8);
  }
}

.user-info-section {
  margin-left: 16px;
}

.user-dropdown-trigger {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.3s;

  &:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }
}

.user-icon {
  font-size: 16px;
  margin-right: 6px;
  color: white;
}

.user-name {
  color: white;
  font-size: 14px;
  margin-right: 4px;
  max-width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.dropdown-arrow {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
}

.user-info-header {
  padding: 12px 16px;
  background-color: #f5f5f5;
  border-bottom: 1px solid #e8e8e8;
  margin: -8px -12px 8px -12px;
}

.user-info-name {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  margin-bottom: 4px;
}

.user-info-dept {
  font-size: 13px;
  color: #595959;
  margin-bottom: 2px;
}

.user-info-unit {
  font-size: 12px;
  color: #8c8c8c;
}

.user-dropdown-menu {
  background: white;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 4px 0;
  min-width: 200px;
}

.user-menu-item {
  padding: 8px 16px;
  cursor: pointer;
  display: flex;
  align-items: center;
  transition: background-color 0.3s;

  &:hover {
    background-color: #f5f5f5;
  }

  .anticon {
    margin-right: 8px;
    font-size: 14px;
  }

  span {
    font-size: 14px;
    color: #262626;
  }
}

.user-menu-divider {
  height: 1px;
  background-color: #f0f0f0;
  margin: 4px 0;
}

// 身份切换模态框样式
.identity-switch-content {
  .identity-table {
    margin-bottom: 24px;

    // 自定义Table样式
    .ant-table-thead > tr > th {
      background-color: #fafafa;
      font-weight: 600;
      color: #262626;
      border-bottom: 1px solid #e8e8e8;
    }

    .ant-table-tbody > tr > td {
      padding: 12px 16px;
      border-bottom: 1px solid #f0f0f0;
    }

    .ant-table-tbody > tr:hover > td {
      background-color: #f5f5f5;
    }

    // 单选按钮样式
    .ant-radio-wrapper {
      margin-right: 0;
    }
  }

  .modal-footer {
    text-align: right;

    .cancel-btn {
      margin-right: 12px;
    }

    .confirm-btn {
      background-color: #4a90e2;
      border-color: #4a90e2;
    }
  }
}

.header-section {
  text-align: center;
  margin-bottom: 30px;
  padding: 20px 20px 0px;
}

.logo-section {
  margin-bottom: 20px;
}

.cppcc-logo {
  width: 90px;
  height: 90px;
  object-fit: contain;
}

.main-title {
  color: white;
  font-size: 36px;
  font-weight: 600;
  margin: 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  letter-spacing: 2px;
}

.main-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px 40px;
}

.app-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 12px;
  border-bottom: 2px solid #f0f0f0;
}

.header-icon {
  font-size: 20px;
  color: #1890ff;
  margin-right: 8px;
}

.header-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.modules-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  padding: 10px 0;
  max-width: 990px;
  margin: 0 auto;
}

.module-item {
  width: 150px;
  height: 120px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid rgba(24, 144, 255, 0.1);
  min-height: 120px;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(24, 144, 255, 0.15);
    border-color: rgba(24, 144, 255, 0.3);
  }
}

// 模块背景图片样式
.module-bg-blue {
  background-image: url("../../assets/home/<USER>");
}

.module-bg-purple {
  background-image: url("../../assets/home/<USER>");
}

.module-bg-green {
  background-image: url("../../assets/home/<USER>");
}

.module-icon {
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #4a90e2, #5ba0f2);
  border-radius: 8px;
  margin-bottom: 8px;
  box-shadow: 0 2px 8px rgba(74, 144, 226, 0.3);
  position: relative;

  .anticon {
    font-size: 20px;
    color: white;
  }
}

.module-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  z-index: 10;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;

  .loading-placeholder {
    width: 100%;
    height: 150px;
  }
}

.module-title {
  font-size: 13px;
  font-weight: 500;
  color: #333;
  text-align: center;
  line-height: 1.3;
  max-width: 100%;
  word-break: break-word;
}

// 响应式设计
@media (max-width: 1200px) {
  .navbar-content {
    padding: 0 15px;
  }

  .main-content {
    padding: 0 15px 30px;
  }
}

@media (max-width: 768px) {
  .navbar-left {
    font-size: 12px;
  }

  .org-text,
  .sub-text {
    display: none;
  }

  .org-text {
    display: inline;
  }

  .divider {
    display: none;
  }

  .user-info-section {
    margin-left: 8px;
  }

  .user-name {
    display: none;
  }

  .dropdown-arrow {
    display: none;
  }

  .header-section {
    padding: 30px 15px 15px;
  }

  .cppcc-logo {
    width: 60px;
    height: 60px;
  }

  .main-title {
    font-size: 28px;
    letter-spacing: 1px;
  }

  .main-content {
    padding: 0 10px 20px;
  }

  .modules-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
  }

  .module-item {
    padding: 16px 8px;
    min-height: 100px;
  }

  .module-icon {
    width: 32px;
    height: 32px;

    .anticon {
      font-size: 16px;
    }
  }

  .module-title {
    font-size: 11px;
  }
}
</style>
