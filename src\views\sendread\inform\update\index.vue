<template>
  <div class="cp-page">
    <div class="sear">
      <div>
        <span>约稿主题：</span>
        <a-space>
          <a-input v-model="search.ygTheme" placeholder="请输入" style="width: 300px" />
        </a-space>
      </div>

      <div>
        <span>约稿时间：</span>
        <a-space>
          <a-date-picker v-model="search.startTime" style="width: 120px" />
          <span>至</span>
          <a-date-picker v-model="search.endTime" style="width: 120px" />
        </a-space>
      </div>
      <div>
        <a-space>
          <a-button type="primary" @click="getList(1)">查询</a-button>
          <a-button
            type="primary"
            @click="
              () => {
                search = {};
                getList(1);
              }
            "
            >重置</a-button
          >
        </a-space>
      </div>
    </div>
    <a-table
      :dataSource="dataSource"
      :columns="columns"
      :pagination="pagination"
      @change="
        (obj) => {
          getList(obj.current, obj.pageSize);
        }
      "
    >
      <a slot="ygTheme" slot-scope="text, record" @click="addOpen(record)" :title="text">
        {{ text }}
      </a>
      <span slot="ygTime" slot-scope="text">
        {{ text ? dayjs(text).format("YYYY-MM-DD") : text }}
      </span>
      <span slot="publishUnit"> 市政协 </span>
      <span slot="action" slot-scope="text, record"> 
        <a-space>
          <a @click="addOpen(record)">修改</a>
          <a-popconfirm title="是否确认删除？" @confirm="del(record)">
            <a style="color: red">删除</a>
        </a-popconfirm>
     
        </a-space>
      </span>
    </a-table>
    <AddEdit ref="addEditRef" @callBack="getList" />
  </div>
</template>

<script>
import { auth as http } from "@zh/common-utils/http";
import AddEdit from "./components/addEdit.vue";
import dayjs from "dayjs";

export default {
  components: { AddEdit },
  data() {
    return {
      dayjs,
      dataSource: [],
      columns: [
        {
          title: "约稿主题",
          dataIndex: "ygTheme",
          width: 400,
          scopedSlots: { customRender: "ygTheme" },
        },
        {
          title: "约稿时间",
          dataIndex: "ygTime",
          key: "ygTime",
          scopedSlots: { customRender: "ygTime" },
        },
        {
          title: "发布单位",
          dataIndex: "publishUnit",
          key: "publishUnit",
          scopedSlots: { customRender: "publishUnit" },
        },
        {
          title: "状态",
          dataIndex: "statusName",
          key: "statusName",
        },
        {
          title: "操作",
          key: "action",
          width: 100,
          scopedSlots: { customRender: "action" },
        },
      ],
      search: {
        ygTheme: undefined,
        startTime: undefined,
        endTime: undefined,
      },
      pageNum: 1,
      pageSize: 10,
      labelCol: { span: 3 },
      wrapperCol: { span: 20 },
      pagination: {},
    };
  },
  // mounted() {
  //   this.getList();
  // },
  activated() {
    this.getList();
  },
  methods: {
    getList(pageNum = 1, pageSize = 10) {
      let temp = { ...this.search };
      Object.keys(temp).forEach((key) => {
        if (key.includes("Time") && temp[key]) {
          temp[key] = dayjs(temp[key]).valueOf();
        }
      });
      http
        .post("/ygNotice/list", {
          ...temp,
          type: 2,
          pageNum: pageNum || this.pageNum,
          pageSize: pageSize || this.pageSize,
        })
        .then((res) => {
          const { currPage, pageSize, list, totalCount } = res;
          this.dataSource = list;
          this.pageNum = currPage;
          this.pageSize = pageSize;
          this.pagination = {
            current: currPage,
            total: totalCount,
          };
        });
    },
    addOpen(record) {
      this.$refs.addEditRef.open(record["id"]);
    },
    del(record){
      http.get(`/ygNotice/delete?id=${record["id"]}`).then(() => {
        this.getList();
      });
    }
  },
};
</script>

<style scoped lang="less">
.sear {
  & > div {
    display: inline-block;
    margin-right: 36px;
    margin-bottom: 18px;
  }
}
</style>
